// @ts-expect-error
import { createClient } from "@connectrpc/connect";
// @ts-expect-error
import { createGrpcTransport } from "@connectrpc/connect-node";
import { TestingService } from "./backend/proto/tools/v1/testing_service_pb";
import { StagingService } from "./backend/proto/tools/v1/staging_service_pb";
import { MessageQueueService } from "./backend/proto/tools/v1/message_queue_service_pb";
import { EnvironmentService } from "./backend/proto/tools/v1/environment_service_pb";
import { DeployPlatformService } from "./backend/proto/tools/v1/deploy_platform_pb";
import { DatabaseService } from "./backend/proto/tools/v1/database_service_pb";
import { ClusterService } from "./backend/proto/tools/v1/cluster_service_pb";
import { CacheService } from "./backend/proto/tools/v1/cache_service_pb";
import { AdminerManagementService } from "./backend/proto/tools/v1/adminer_management_pb";
import { PetService } from "./backend/proto/pet/v1/pet_service_pb";
import { CustomerService } from "./backend/proto/customer/v1/customer_service_pb";
import { CustomerQueryService } from "./backend/proto/customer/v1/customer_query_service_pb";
import { AIStudioService } from "./backend/proto/aistudio/v1/aistudio_pb";
import { SalesService } from "./backend/proto/sales/v1/sales_service_pb";
import { PlatformSalesService } from "./backend/proto/sales/v1/platform_sales_service_pb";
import { MoegoPayCustomFeeApprovalService } from "./backend/proto/sales/v1/moego_pay_custom_fee_approval_service_pb";
import { MoegoPayContractService } from "./backend/proto/sales/v1/moego_pay_contract_service_pb";
import { AnnualContractService } from "./backend/proto/sales/v1/annual_contract_service_pb";
import { CSPageWatcherService } from "./backend/proto/cs_page_watcher/v1/cs_page_watcher_pb";
import { OpenPlatformService } from "./backend/proto/open_platform/v1/open_platform_service_pb";
import { MetadataService as MetadataServiceV2 } from "./backend/proto/customer/v2/metadata_service_pb";
import { DataMigrationService as DataMigrationServiceV2 } from "./backend/proto/customer/v2/data_migration_service_pb";
import { ActivityService as ActivityServiceV2 } from "./backend/proto/customer/v2/activity_service_pb";
import { InstanceService } from "./backend/proto/fulfillment/v1/instance_service_pb";
import { FulfillmentService } from "./backend/proto/fulfillment/v1/fulfillment_service_pb";
import { FulfillmentReportService } from "./backend/proto/fulfillment/v1/fulfillment_report_service_pb";
import { AppointmentService } from "./backend/proto/fulfillment/v1/appointment_service_pb";
import { ServiceService } from "./backend/proto/offering/v1/service_service_pb";
import { ServiceCategoryService } from "./backend/proto/offering/v1/service_category_service_pb";
import { LodgingService } from "./backend/proto/offering/v1/lodging_service_pb";
import { CareTypeService } from "./backend/proto/offering/v1/care_type_service_pb";
import { AddOnService } from "./backend/proto/offering/v1/addon_service_pb";
import { TestAccountService } from "./backend/proto/test_account/v1/test_account_service_pb";
import { CustomerPortalService } from "./backend/proto/customer_portal/v1/customer_portal_service_pb";
import { OnboardingService } from "./backend/proto/onboarding/v1/onboarding_service_pb";
import { TwilioCallbackService } from "./backend/proto/message_hub/v1/twilio_callback_service_pb";
import { MessageHubService } from "./backend/proto/message_hub/v1/message_hub_service_pb";
import { CallbackService } from "./backend/proto/message_hub/v1/callback_service_pb";
import { MfaManagementService } from "./backend/proto/authn/v1/mfa_management_service_pb";
import { AuthnService } from "./backend/proto/authn/v1/authn_service_pb";

export const TestingServiceClient = createClient(TestingService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const StagingServiceClient = createClient(StagingService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const MessageQueueServiceClient = createClient(MessageQueueService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const EnvironmentServiceClient = createClient(EnvironmentService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const DeployPlatformServiceClient = createClient(DeployPlatformService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const DatabaseServiceClient = createClient(DatabaseService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const ClusterServiceClient = createClient(ClusterService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const CacheServiceClient = createClient(CacheService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const AdminerManagementServiceClient = createClient(AdminerManagementService, createGrpcTransport({ baseUrl: 'http://moego-tools:9090' }));
export const PetServiceClient = createClient(PetService, createGrpcTransport({ baseUrl: 'http://moego-pet:9090' }));
export const CustomerServiceClient = createClient(CustomerService, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const CustomerQueryServiceClient = createClient(CustomerQueryService, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const AIStudioServiceClient = createClient(AIStudioService, createGrpcTransport({ baseUrl: 'http://moego-aistudio:9090' }));
export const SalesServiceClient = createClient(SalesService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const PlatformSalesServiceClient = createClient(PlatformSalesService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const MoegoPayCustomFeeApprovalServiceClient = createClient(MoegoPayCustomFeeApprovalService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const MoegoPayContractServiceClient = createClient(MoegoPayContractService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const AnnualContractServiceClient = createClient(AnnualContractService, createGrpcTransport({ baseUrl: 'http://moego-sales:9090' }));
export const CSPageWatcherServiceClient = createClient(CSPageWatcherService, createGrpcTransport({ baseUrl: 'http://moego-cs-page-watcher:9090' }));
export const OpenPlatformServiceClient = createClient(OpenPlatformService, createGrpcTransport({ baseUrl: 'http://moego-open-platform-v2:9090' }));
export const MetadataServiceV2Client = createClient(MetadataServiceV2, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const DataMigrationServiceV2Client = createClient(DataMigrationServiceV2, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const ActivityServiceV2Client = createClient(ActivityServiceV2, createGrpcTransport({ baseUrl: 'http://moego-customer:9090' }));
export const InstanceServiceClient = createClient(InstanceService, createGrpcTransport({ baseUrl: 'http://moego-fulfillment:9090' }));
export const FulfillmentServiceClient = createClient(FulfillmentService, createGrpcTransport({ baseUrl: 'http://moego-fulfillment:9090' }));
export const FulfillmentReportServiceClient = createClient(FulfillmentReportService, createGrpcTransport({ baseUrl: 'http://moego-fulfillment:9090' }));
export const AppointmentServiceClient = createClient(AppointmentService, createGrpcTransport({ baseUrl: 'http://moego-fulfillment:9090' }));
export const ServiceServiceClient = createClient(ServiceService, createGrpcTransport({ baseUrl: 'http://moego-offering:9090' }));
export const ServiceCategoryServiceClient = createClient(ServiceCategoryService, createGrpcTransport({ baseUrl: 'http://moego-offering:9090' }));
export const LodgingServiceClient = createClient(LodgingService, createGrpcTransport({ baseUrl: 'http://moego-offering:9090' }));
export const CareTypeServiceClient = createClient(CareTypeService, createGrpcTransport({ baseUrl: 'http://moego-offering:9090' }));
export const AddOnServiceClient = createClient(AddOnService, createGrpcTransport({ baseUrl: 'http://moego-offering:9090' }));
export const TestAccountServiceClient = createClient(TestAccountService, createGrpcTransport({ baseUrl: 'http://moego-test-account:9090' }));
export const CustomerPortalServiceClient = createClient(CustomerPortalService, createGrpcTransport({ baseUrl: 'http://moego-customer-portal:9090' }));
export const OnboardingServiceClient = createClient(OnboardingService, createGrpcTransport({ baseUrl: 'http://moego-onboarding:9090' }));
export const TwilioCallbackServiceClient = createClient(TwilioCallbackService, createGrpcTransport({ baseUrl: 'http://moego-message-hub:9090' }));
export const MessageHubServiceClient = createClient(MessageHubService, createGrpcTransport({ baseUrl: 'http://moego-message-hub:9090' }));
export const CallbackServiceClient = createClient(CallbackService, createGrpcTransport({ baseUrl: 'http://moego-message-hub:9090' }));
export const MfaManagementServiceClient = createClient(MfaManagementService, createGrpcTransport({ baseUrl: 'http://moego-authn:9090' }));
export const AuthnServiceClient = createClient(AuthnService, createGrpcTransport({ baseUrl: 'http://moego-authn:9090' }));
