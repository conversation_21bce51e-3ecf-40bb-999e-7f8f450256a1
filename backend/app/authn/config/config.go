package config

import (
	"fmt"
	"sync"
	"time"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/utils/env"
)

var (
	globalConfig *AuthnConfig
	initOnce     sync.Once
)

// AuthnConfig holds the service-specific configurations for the authn service.
type AuthnConfig struct {
	Session SessionConfig `yaml:"session"`
	MFA     MFAConfig     `yaml:"mfa"`
}

// SessionConfig defines settings related to user sessions.
type SessionConfig struct {
	MaxAgeHours int `yaml:"max_age_hours"`
	MaxAge      time.Duration
}

// UnmarshalYAML implements the yaml.Unmarshaler interface.
func (s *SessionConfig) UnmarshalYAML(unmarshal func(interface{}) error) error {
	type rawSessionConfig SessionConfig
	raw := &rawSessionConfig{}
	if err := unmarshal(raw); err != nil {
		return err
	}

	s.MaxAgeHours = raw.MaxAgeHours
	s.MaxAge = time.Hour * time.Duration(raw.MaxAgeHours)

	return nil
}

// MFAConfig defines settings related to Multi-Factor Authentication.
type MFAConfig struct {
	TrustedDeviceExpirationDays int `yaml:"trusted_device_expiration_days"`
	TrustedDeviceExpiration     time.Duration
	VerificationCode            VerificationCodeConfig `yaml:"verification_code"`
}

// UnmarshalYAML implements the yaml.Unmarshaler interface.
func (m *MFAConfig) UnmarshalYAML(unmarshal func(interface{}) error) error {
	type rawMFAConfig MFAConfig
	raw := &rawMFAConfig{}
	if err := unmarshal(raw); err != nil {
		return err
	}

	m.TrustedDeviceExpirationDays = raw.TrustedDeviceExpirationDays
	m.TrustedDeviceExpiration = time.Hour * 24 * time.Duration(raw.TrustedDeviceExpirationDays)
	m.VerificationCode = raw.VerificationCode

	return nil
}

// VerificationCodeConfig holds settings for MFA verification codes.
type VerificationCodeConfig struct {
	TTLSeconds           int `yaml:"ttl_seconds"`
	TTL                  time.Duration
	RateLimitTTLSeconds  int `yaml:"rate_limit_ttl_seconds"`
	RateLimitTTL         time.Duration
	RateLimitMaxAttempts int `yaml:"rate_limit_max_attempts"`
	MaxAttempts          int `yaml:"max_attempts"`
}

// UnmarshalYAML implements the yaml.Unmarshaler interface.
func (v *VerificationCodeConfig) UnmarshalYAML(unmarshal func(interface{}) error) error {
	type rawVerificationCodeConfig VerificationCodeConfig
	raw := &rawVerificationCodeConfig{}
	if err := unmarshal(raw); err != nil {
		return err
	}

	v.TTLSeconds = raw.TTLSeconds
	v.TTL = time.Second * time.Duration(raw.TTLSeconds)
	v.RateLimitTTLSeconds = raw.RateLimitTTLSeconds
	v.RateLimitTTL = time.Second * time.Duration(raw.RateLimitTTLSeconds)
	v.RateLimitMaxAttempts = raw.RateLimitMaxAttempts
	v.MaxAttempts = raw.MaxAttempts

	return nil
}

// Init loads the service-specific configuration from authn.yaml.
// It should be called once from the main function.
func Init(dir string) {
	initOnce.Do(func() {
		path := fmt.Sprintf("%s/%s/authn.yaml", dir, env.GetEnv())

		c, err := config.DefaultConfigLoader.Load(path)
		if err != nil {
			panic(fmt.Sprintf("failed to load authn config from %s: %v", path, err))
		}

		cfg := &AuthnConfig{}
		if err = c.Unmarshal(cfg); err != nil {
			panic(fmt.Sprintf("failed to unmarshal authn config: %v", err))
		}
		globalConfig = cfg
	})
}

// Get returns the global configuration instance.
// It will panic if Init has not been called.
func Get() *AuthnConfig {
	if globalConfig == nil {
		panic("authn config has not been initialized")
	}

	return globalConfig
}
