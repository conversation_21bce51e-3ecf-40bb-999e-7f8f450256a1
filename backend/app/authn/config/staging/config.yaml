server:
  app: template
  server: template-go
  filter:
    - opentelemetry
    - recovery
    - debuglog
    - validation
  service:
    - name: backend.proto.authn.v1.AuthnService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 5000
    - name: backend.proto.authn.v1.MfaManagementService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 5000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: moego-message-hub
      target: dns://moego-message-hub:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-account
      target: dns://moego-svc-account:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-server-business
      target: http://moego-service-business:9203
      protocol: http
    - callee: greeter_cutomized_name
      target: ip://127.0.0.1:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: postgres.moego_account
      target: dsn://postgresql://${secret.datasource.postgres.moego_account.username}:${secret.datasource.postgres.moego_account.password}@${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_account
      protocol: gorm
      transport: gorm
    - callee: redis
      target: redis://${secret.redis.host}:${secret.redis.port}/0?tls=${secret.redis.tls}
      password: ${secret.redis.password}
      timeout: 1000
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_account
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true
          server_addr: ${secret.nacos.server-addr}
          username: ${secret.nacos.username}
          password: ${secret.nacos.password}
          namespace: ${secret.nacos.namespace}

secrets:
  - name: 'moego/staging/datasource'
    prefix: 'secret.datasource.'
  - name: "moego/staging/nacos"
    prefix: "secret.nacos."
  - name: 'moego/staging/redis'
    prefix: 'secret.redis.'
