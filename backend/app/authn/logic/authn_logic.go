package logic

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/durationpb"

	accountsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1"
	"github.com/MoeGolibrary/moego/backend/app/authn/config"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/account"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/cache"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/message"
	authnutils "github.com/MoeGolibrary/moego/backend/app/authn/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

const (
	// loginVerificationContentTmpl is the SMS content for login verification.
	loginVerificationContentTmpl = "[MoeGo] Your login verification code is %s"
)

type AuthnLogic struct {
	cfg        config.AuthnConfig
	accountAPI account.API
	db         db.Database
	codeCache  cache.VerificationCodeRepo
	messageAPI message.API
}

// NewAuthnLogic is the constructor for production use.
// It initializes dependencies from global sources (e.g., config.Get()).
func NewAuthnLogic() *AuthnLogic {
	codeCache, err := cache.NewVerificationCodeRepo()
	if err != nil {
		panic(err)
	}

	return newAuthnLogic(*config.Get(),
		account.NewAPI(),
		db.New(),
		codeCache,
		message.NewAPI(),
	)
}

// newAuthnLogic is the constructor for internal and test use.
// It accepts all dependencies as arguments, making it suitable for mocking.
func newAuthnLogic(cfg config.AuthnConfig, accountAPI account.API, db db.Database,
	codeCache cache.VerificationCodeRepo,
	messageAPI message.API) *AuthnLogic {
	return &AuthnLogic{
		cfg:        cfg,
		accountAPI: accountAPI,
		db:         db,
		codeCache:  codeCache,
		messageAPI: messageAPI,
	}
}

func (l *AuthnLogic) Login(ctx context.Context, req *authnpb.LoginRequest) (*authnpb.LoginResponse, error) {
	// check if account exists
	acc, err := l.accountAPI.GetAccountByEmail(ctx, req.GetEmail())
	if err != nil {
		return nil, err
	}
	if acc == nil {
		return nil, authnutils.AccountOrPasswordError
	}

	// check if password is correct
	if !l.accountAPI.ValidatePassword(ctx, acc.GetId(), req.GetPassword()) {
		return nil, authnutils.AccountOrPasswordError
	}

	// 没有传 challenge token 和 challenge code，说明是第一次请求 login，需要检查是否需要发起 MFA
	if req.ChallengeToken == nil && req.ChallengeCode == nil {
		// 检查是否有需要验证的 factor，如果有，则返回要求验证
		if factor := l.checkMFA(ctx, acc.GetId(), req.GetDeviceId()); factor != nil {
			return &authnpb.LoginResponse{
				RequireMfa: true,
				Factor:     factor,
			}, nil
		}
	} else {
		if err := l.challengeMFA(ctx, acc.GetId(), req.GetChallengeCode(), req.GetChallengeToken()); err != nil {
			return nil, err
		}
		l.trustDevice(ctx, acc.GetId(), req.GetDeviceId())
	}

	// create session
	createSessionReq := l.toCreateSessionRequest(req, acc.GetId())
	sessionResp, err := l.accountAPI.CreateSession(ctx, createSessionReq)
	if err != nil {
		log.ErrorContextf(ctx, "failed to create session for account %d: %v", acc.GetId(), err)

		return nil, authnutils.LoginError
	}

	// special logic for business source
	if req.GetSource() == authnpb.LoginRequest_BUSINESS {
		// init business session context
		if err := l.accountAPI.InitBusinessSessionContext(ctx, acc.GetId(), sessionResp.GetId()); err != nil {
			log.ErrorContextf(ctx, "failed to init business session context for account %d: %v", acc.GetId(), err)

			return nil, authnutils.LoginError
		}

		// first session, sync to hubspot (async run)
		if sessionResp.GetFirstSession() {
			l.handleFirstSessionTasks(ctx, acc.GetId())
		}
	}

	return &authnpb.LoginResponse{
		SessionToken:  &sessionResp.SessionToken,
		SessionMaxAge: createSessionReq.MaxAge,
		RequireMfa:    false,
	}, nil
}

func (l *AuthnLogic) SendLoginChallengeCode(ctx context.Context,
	req *authnpb.SendLoginChallengeCodeRequest) (string, error) {
	// check if account exists
	acc, err := l.accountAPI.GetAccountByEmail(ctx, req.GetEmail())
	if err != nil {
		return "", err
	}
	if acc == nil {
		return "", authnutils.AccountNotFoundError
	}

	// Find the phone number factor.
	factor, err := l.db.GetPhoneNumberFactor(ctx, acc.GetId())
	if err != nil {
		log.ErrorContextf(ctx,
			"GetPhoneNumberFactor err, accountID:%d, err:%+v", acc.GetId(), err)

		return "", authnutils.VerificationCodeSendError
	}

	// If no phone number factor is configured, we can't send a code.
	if factor == nil {
		log.WarnContextf(ctx, "no phone number factor for account %d", acc.GetId())

		return "", authnutils.NoPhoneNumberFactorError
	}

	details, ok := factor.ParsedDetails.(db.PhoneNumberDetails)
	if !ok {
		log.ErrorContextf(ctx, "unexpected factor details type for account %d", acc.GetId())

		return "", authnutils.VerificationCodeSendError
	}

	token, code, err := l.codeCache.Create(ctx, cache.PurposeMfaLogin, acc.GetId(), details.E164Number)
	if err != nil {
		return "", err
	}

	content := fmt.Sprintf(loginVerificationContentTmpl, code)
	if err := l.messageAPI.SendVerificationCode(ctx, details.RegionCode, details.E164Number, content); err != nil {
		return "", err
	}

	return token, nil
}

// checkMFA check if MFA is required and return response if need to challenge
func (l *AuthnLogic) checkMFA(ctx context.Context, accountID int64, deviceID string) *authnpb.AuthenticationFactor {
	factor, err := l.db.GetPhoneNumberFactor(ctx, accountID)
	// 暂时忽略错误，如果底层出错，则跳过 MFA，不影响用户使用
	if err != nil {
		log.ErrorContextf(ctx, "failed to get phone number factor for account %d: %v", accountID, err)

		return nil
	}

	// 没有配置 phone number factor，跳过 mfa
	if factor == nil {
		return nil
	}

	device, _ := l.db.GetTrustedDevice(ctx, accountID, deviceID)
	// 没查到设备，是新设备，需要 MFA
	if device == nil {
		return l.toPbFactor(ctx, factor)
	}

	// 当前设备超过有效期没有重新验证，需要 MFA
	expiration := l.cfg.MFA.TrustedDeviceExpiration
	if device.LastVerifiedAt.Before(time.Now().Add(-expiration)) {
		return l.toPbFactor(ctx, factor)
	}

	return nil // Trusted device, skip MFA
}

func (l *AuthnLogic) challengeMFA(ctx context.Context, accountID int64, challengeCode, challengeToken string) error {
	factor, err := l.db.GetPhoneNumberFactor(ctx, accountID)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get phone number factor for account %d: %v", accountID, err)

		return authnutils.VerificationCodeMismatchError
	}
	details, ok := factor.ParsedDetails.(db.PhoneNumberDetails)
	if !ok {
		log.ErrorContextf(ctx, "unexpected factor details type for account %d", accountID)

		return authnutils.VerificationCodeMismatchError
	}

	if !l.codeCache.Verify(ctx, cache.PurposeMfaLogin, challengeToken, challengeCode, details.E164Number) {
		return authnutils.VerificationCodeMismatchError
	}

	return nil
}

func (l *AuthnLogic) trustDevice(ctx context.Context, accountID int64, deviceID string) {
	// Upsert the device as a trusted device.
	device := &db.TrustedDevice{
		ID:        authnutils.NewUUIDv7(),
		AccountID: accountID,
		DeviceID:  deviceID,
	}
	if err := l.db.UpsertTrustedDevice(ctx, device); err != nil {
		// Trusting a device is a non-critical operation for the login flow.
		// If it fails, we should log the error but not fail the entire login request.
		log.ErrorContextf(ctx, "failed to upsert trusted device for account %d: %v", accountID, err)
	}
}

func (l *AuthnLogic) handleFirstSessionTasks(ctx context.Context, accountID int64) {
	go func(ctx context.Context, accountID int64) {
		defer func() {
			if err := recover(); err != nil {
				log.ErrorContextf(ctx, "panic in sync hubspot account: %v", err)
			}
		}()
		// The original context `ctx` will be canceled when the request finishes.
		// To prevent the background task from being prematurely terminated,
		// we create a new context that inherits the values (like trace IDs)
		// but is not tied to the request's lifecycle.
		// A timeout is added to prevent the task from running indefinitely.
		bgCtx, cancel := context.WithTimeout(context.WithoutCancel(ctx), time.Minute)
		defer cancel()
		l.accountAPI.SyncHubspotAccount(bgCtx, accountID)
	}(ctx, accountID)
}

func (l *AuthnLogic) toCreateSessionRequest(req *authnpb.LoginRequest,
	accountID int64) *accountsvcpb.CreateSessionRequest {
	return &accountsvcpb.CreateSessionRequest{
		AccountId:        accountID,
		Ip:               req.GetIp(),
		UserAgent:        req.GetUserAgent(),
		RefererLink:      req.GetRefererLink(),
		RefererSessionId: req.GetRefererSessionId(),
		DeviceId:         req.GetDeviceId(),
		MaxAge:           durationpb.New(l.cfg.Session.MaxAge),
		Source:           GetSourceString(req.GetSource()),
	}
}

func (l *AuthnLogic) toPbFactor(ctx context.Context, factor *db.AuthenticationFactor) *authnpb.AuthenticationFactor {
	details, ok := factor.ParsedDetails.(db.PhoneNumberDetails)
	if !ok {
		log.ErrorContextf(ctx, "unexpected factor details type for factor %s", factor.ID)

		return nil
	}

	return &authnpb.AuthenticationFactor{
		Id:   factor.ID,
		Type: authnpb.FactorType_PHONE_NUMBER,
		Details: &authnpb.AuthenticationFactor_PhoneNumber_{
			PhoneNumber: &authnpb.AuthenticationFactor_PhoneNumber{
				E164Number: details.E164Number,
				RegionCode: details.RegionCode,
			},
		},
	}
}

func GetSourceString(source authnpb.LoginRequest_Source) string {
	switch source {
	case authnpb.LoginRequest_BUSINESS:
		return "business"
	default:
		return "unknown"
	}
}
