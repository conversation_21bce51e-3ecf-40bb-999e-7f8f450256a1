package logic

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	accountpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	accountsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1"
	"github.com/MoeGolibrary/moego/backend/app/authn/config"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/cache"
	"github.com/MoeGolibrary/moego/backend/app/authn/repo/db"
	mockaccount "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/account"
	mockcache "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/cache"
	mockdb "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/db"
	mockmessage "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/message"
	authnutils "github.com/MoeGolibrary/moego/backend/app/authn/utils"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

type authnLogicTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	mockAccountAPI *mockaccount.MockAPI
	mockDB         *mockdb.MockDatabase
	mockCodeCache  *mockcache.MockVerificationCodeRepo
	mockMessageAPI *mockmessage.MockAPI
	logic          *AuthnLogic
	testCfg        config.AuthnConfig
}

func (s *authnLogicTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockAccountAPI = mockaccount.NewMockAPI(s.ctrl)
	s.mockDB = mockdb.NewMockDatabase(s.ctrl)
	s.mockCodeCache = mockcache.NewMockVerificationCodeRepo(s.ctrl)
	s.mockMessageAPI = mockmessage.NewMockAPI(s.ctrl)

	// Initialize a test config that mirrors the post-unmarshal state.
	s.testCfg = config.AuthnConfig{
		Session: config.SessionConfig{
			MaxAgeHours: 24,
			MaxAge:      24 * time.Hour,
		},
		MFA: config.MFAConfig{
			TrustedDeviceExpirationDays: 90,
			TrustedDeviceExpiration:     90 * 24 * time.Hour,
			VerificationCode: config.VerificationCodeConfig{
				TTLSeconds:          300,
				TTL:                 300 * time.Second,
				RateLimitTTLSeconds: 55,
				RateLimitTTL:        55 * time.Second,
				MaxAttempts:         5,
			},
		},
	}

	s.logic = newAuthnLogic(s.testCfg, s.mockAccountAPI, s.mockDB, s.mockCodeCache, s.mockMessageAPI)
}

func (s *authnLogicTestSuite) TearDownTest() {
	s.ctrl.Finish()
}

func TestAuthnLogicTestSuite(t *testing.T) {
	suite.Run(t, new(authnLogicTestSuite))
}

func (s *authnLogicTestSuite) TestLogin_Success_NoMFA() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password",
		DeviceId: "device-123",
	}
	acc := &accountpb.AccountModel{Id: 1, Email: "<EMAIL>"}
	sessionResp := &accountsvcpb.CreateSessionResponse{
		SessionToken: "session-token-xyz",
		FirstSession: false,
	}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(nil, nil)
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(sessionResp, nil)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.False(resp.RequireMfa)
	s.Require().NotNil(resp.SessionToken)
	s.Equal(sessionResp.SessionToken, *resp.SessionToken)
}

func (s *authnLogicTestSuite) TestLogin_Success_NoMFA_FactorDBError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>", Password: "password"}
	acc := &accountpb.AccountModel{Id: 1}
	sessionResp := &accountsvcpb.CreateSessionResponse{SessionToken: "session-token-xyz"}
	dbErr := errors.New("database error")

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(nil, dbErr) // Simulate DB error
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(sessionResp, nil)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.False(resp.RequireMfa)
	s.Equal("session-token-xyz", *resp.SessionToken)
}

func (s *authnLogicTestSuite) TestLogin_Success_TrustedDevice() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>", Password: "password", DeviceId: "trusted-device"}
	acc := &accountpb.AccountModel{Id: 1}
	factor := &db.AuthenticationFactor{ID: "factor-1"}
	trustedDevice := &db.TrustedDevice{LastVerifiedAt: time.Now().Add(-10 * 24 * time.Hour)} // Verified 10 days ago
	sessionResp := &accountsvcpb.CreateSessionResponse{SessionToken: "session-token-xyz"}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockDB.EXPECT().GetTrustedDevice(ctx, acc.GetId(), req.GetDeviceId()).Return(trustedDevice, nil)
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(sessionResp, nil)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.False(resp.RequireMfa)
	s.Equal("session-token-xyz", *resp.SessionToken)
}

func (s *authnLogicTestSuite) TestLogin_Failure_AccountNotFound() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>", Password: "password"}
	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(nil, nil)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
	s.Equal(authnutils.AccountOrPasswordError, err)
}

func (s *authnLogicTestSuite) TestLogin_Failure_GetAccountByEmailError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>", Password: "password"}
	expectedErr := errors.New("internal server error")
	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(nil, expectedErr)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
	s.Equal(expectedErr, err)
}

func (s *authnLogicTestSuite) TestLogin_Failure_InvalidPassword() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>", Password: "wrong-password"}
	acc := &accountpb.AccountModel{Id: 1, Email: "<EMAIL>"}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(false)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
	s.Equal(authnutils.AccountOrPasswordError, err)
}

func (s *authnLogicTestSuite) TestLogin_RequireMFA_NewDevice() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>", Password: "password", DeviceId: "new-device"}
	acc := &accountpb.AccountModel{Id: 1}
	factorDetails := db.PhoneNumberDetails{E164Number: "+***********", RegionCode: "US"}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: factorDetails}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockDB.EXPECT().GetTrustedDevice(ctx, acc.GetId(), req.GetDeviceId()).Return(nil, nil) // New device

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.True(resp.RequireMfa)
	s.Equal(authnpb.FactorType_PHONE_NUMBER, resp.Factor.Type)
}

func (s *authnLogicTestSuite) TestLogin_RequireMFA_ExpiredDevice() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>", Password: "password", DeviceId: "expired-device"}
	acc := &accountpb.AccountModel{Id: 1}
	factorDetails := db.PhoneNumberDetails{E164Number: "+***********", RegionCode: "US"}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: factorDetails}
	expiredDevice := &db.TrustedDevice{LastVerifiedAt: time.Now().Add(-100 * 24 * time.Hour)} // Verified 100 days ago

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockDB.EXPECT().GetTrustedDevice(ctx, acc.GetId(), req.GetDeviceId()).Return(expiredDevice, nil)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.True(resp.RequireMfa)
}

func (s *authnLogicTestSuite) TestLogin_Success_WithCorrectMfaCode() {
	// --- Arrange ---
	ctx := context.Background()
	challengeToken := "mfa-token"
	challengeCode := "123456"
	e164Number := "+***********"
	req := &authnpb.LoginRequest{
		Email:          "<EMAIL>",
		Password:       "password",
		DeviceId:       "device-123",
		ChallengeToken: &challengeToken,
		ChallengeCode:  &challengeCode,
	}
	acc := &accountpb.AccountModel{Id: 1}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: db.PhoneNumberDetails{E164Number: e164Number}}
	sessionResp := &accountsvcpb.CreateSessionResponse{SessionToken: "session-token-xyz"}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockCodeCache.EXPECT().Verify(ctx, cache.PurposeMfaLogin, challengeToken, challengeCode, e164Number).Return(true)
	s.mockDB.EXPECT().UpsertTrustedDevice(ctx, gomock.Any()).Return(nil)
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(sessionResp, nil)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.False(resp.RequireMfa)
	s.Equal("session-token-xyz", *resp.SessionToken)
}

func (s *authnLogicTestSuite) TestLogin_Success_WithCorrectMfaCode_UpsertDeviceError() {
	// --- Arrange ---
	ctx := context.Background()
	challengeToken := "mfa-token"
	challengeCode := "123456"
	e164Number := "+***********"
	req := &authnpb.LoginRequest{
		Email:          "<EMAIL>",
		Password:       "password",
		DeviceId:       "device-123",
		ChallengeToken: &challengeToken,
		ChallengeCode:  &challengeCode,
	}
	acc := &accountpb.AccountModel{Id: 1}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: db.PhoneNumberDetails{E164Number: e164Number}}
	sessionResp := &accountsvcpb.CreateSessionResponse{SessionToken: "session-token-xyz"}
	upsertErr := errors.New("db error")

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockCodeCache.EXPECT().Verify(ctx, cache.PurposeMfaLogin, challengeToken, challengeCode, e164Number).Return(true)
	s.mockDB.EXPECT().UpsertTrustedDevice(ctx, gomock.Any()).Return(upsertErr) // Simulate an error
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(sessionResp, nil)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	// Login should still succeed even if upserting the trusted device fails.
	s.Require().NoError(err)
	s.False(resp.RequireMfa)
	s.Equal("session-token-xyz", *resp.SessionToken)
}

func (s *authnLogicTestSuite) TestLogin_Failure_WithIncorrectMfaCode() {
	// --- Arrange ---
	ctx := context.Background()
	challengeToken := "mfa-token"
	challengeCode := "wrong-code"
	e164Number := "+***********"
	req := &authnpb.LoginRequest{
		Email:          "<EMAIL>",
		Password:       "password",
		ChallengeToken: &challengeToken,
		ChallengeCode:  &challengeCode,
	}
	acc := &accountpb.AccountModel{Id: 1}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: db.PhoneNumberDetails{E164Number: e164Number}}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, acc.GetId(), req.GetPassword()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockCodeCache.EXPECT().Verify(ctx, cache.PurposeMfaLogin, challengeToken, challengeCode, e164Number).Return(false)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
	s.Equal(authnutils.VerificationCodeMismatchError, err)
}

func (s *authnLogicTestSuite) TestLogin_Success_BusinessSource_FirstSession() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{
		Email:  "<EMAIL>",
		Source: authnpb.LoginRequest_BUSINESS,
	}
	acc := &accountpb.AccountModel{Id: 2}
	sessionResp := &accountsvcpb.CreateSessionResponse{
		Id:           100,
		SessionToken: "session-token-abc",
		FirstSession: true,
	}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, gomock.Any(), gomock.Any()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(nil, nil)
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(sessionResp, nil)
	s.mockAccountAPI.EXPECT().InitBusinessSessionContext(ctx, acc.GetId(), sessionResp.GetId()).Return(nil)
	s.mockAccountAPI.EXPECT().SyncHubspotAccount(gomock.Any(), acc.GetId()).Return() // gomock.Any() for the new go routine context

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Equal("session-token-abc", *resp.SessionToken)
	// Allow some time for the goroutine to be called
	time.Sleep(10 * time.Millisecond)
}

func (s *authnLogicTestSuite) TestLogin_Failure_BusinessSource_InitError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{
		Email:  "<EMAIL>",
		Source: authnpb.LoginRequest_BUSINESS,
	}
	acc := &accountpb.AccountModel{Id: 2}
	sessionResp := &accountsvcpb.CreateSessionResponse{Id: 100, SessionToken: "session-token-abc"}
	internalError := errors.New("init context failed")

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, gomock.Any(), gomock.Any()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(nil, nil)
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(sessionResp, nil)
	s.mockAccountAPI.EXPECT().InitBusinessSessionContext(ctx, acc.GetId(), sessionResp.GetId()).Return(internalError)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
	s.Equal(authnutils.LoginError, err)
}

func (s *authnLogicTestSuite) TestLogin_Failure_CreateSessionError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.LoginRequest{Email: "<EMAIL>"}
	acc := &accountpb.AccountModel{Id: 1}
	internalError := errors.New("session service unavailable")

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockAccountAPI.EXPECT().ValidatePassword(ctx, gomock.Any(), gomock.Any()).Return(true)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(nil, nil)
	s.mockAccountAPI.EXPECT().CreateSession(ctx, gomock.Any()).Return(nil, internalError)

	// --- Act ---
	resp, err := s.logic.Login(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Nil(resp)
	s.Equal(authnutils.LoginError, err)
}

func (s *authnLogicTestSuite) TestSendLoginChallengeCode_Success() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.SendLoginChallengeCodeRequest{Email: "<EMAIL>"}
	acc := &accountpb.AccountModel{Id: 1}
	factorDetails := db.PhoneNumberDetails{E164Number: "+***********", RegionCode: "US"}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: factorDetails}
	expectedToken := "new-mfa-token"
	expectedCode := "654321"
	expectedContent := fmt.Sprintf(loginVerificationContentTmpl, expectedCode)

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockCodeCache.EXPECT().Create(ctx, cache.PurposeMfaLogin, acc.GetId(), factorDetails.E164Number).Return(expectedToken, expectedCode, nil)
	s.mockMessageAPI.EXPECT().SendVerificationCode(ctx, factorDetails.RegionCode, factorDetails.E164Number, expectedContent).Return(nil)

	// --- Act ---
	token, err := s.logic.SendLoginChallengeCode(ctx, req)

	// --- Assert ---
	s.Require().NoError(err)
	s.Equal(expectedToken, token)
}

func (s *authnLogicTestSuite) TestSendLoginChallengeCode_Failure_AccountNotFound() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.SendLoginChallengeCodeRequest{Email: "<EMAIL>"}
	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(nil, nil)

	// --- Act ---
	token, err := s.logic.SendLoginChallengeCode(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Empty(token)
	s.Equal(authnutils.AccountNotFoundError, err)
}

func (s *authnLogicTestSuite) TestSendLoginChallengeCode_Failure_NoPhoneNumberFactor() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.SendLoginChallengeCodeRequest{Email: "<EMAIL>"}
	acc := &accountpb.AccountModel{Id: 1}

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(nil, nil)

	// --- Act ---
	token, err := s.logic.SendLoginChallengeCode(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Empty(token)
	s.Equal(authnutils.NoPhoneNumberFactorError, err)
}

func (s *authnLogicTestSuite) TestSendLoginChallengeCode_Failure_CreateTokenError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.SendLoginChallengeCodeRequest{Email: "<EMAIL>"}
	acc := &accountpb.AccountModel{Id: 1}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: db.PhoneNumberDetails{E164Number: "+***********"}}
	expectedErr := errors.New("cache unavailable")

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockCodeCache.EXPECT().Create(ctx, cache.PurposeMfaLogin, acc.GetId(), "+***********").Return("", "", expectedErr)

	// --- Act ---
	token, err := s.logic.SendLoginChallengeCode(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Empty(token)
	s.Equal(expectedErr, err)
}

func (s *authnLogicTestSuite) TestSendLoginChallengeCode_Failure_MessageError() {
	// --- Arrange ---
	ctx := context.Background()
	req := &authnpb.SendLoginChallengeCodeRequest{Email: "<EMAIL>"}
	acc := &accountpb.AccountModel{Id: 1}
	factorDetails := db.PhoneNumberDetails{E164Number: "+***********", RegionCode: "US"}
	factor := &db.AuthenticationFactor{ID: "factor-1", Type: db.FactorTypePhoneNumber, ParsedDetails: factorDetails}
	expectedErr := errors.New("sms service unavailable")
	expectedCode := "654321"
	expectedContent := fmt.Sprintf(loginVerificationContentTmpl, expectedCode)

	s.mockAccountAPI.EXPECT().GetAccountByEmail(ctx, req.GetEmail()).Return(acc, nil)
	s.mockDB.EXPECT().GetPhoneNumberFactor(ctx, acc.GetId()).Return(factor, nil)
	s.mockCodeCache.EXPECT().Create(ctx, cache.PurposeMfaLogin, acc.GetId(), factorDetails.E164Number).Return("token", expectedCode, nil)
	s.mockMessageAPI.EXPECT().SendVerificationCode(ctx, factorDetails.RegionCode, factorDetails.E164Number, expectedContent).Return(expectedErr)

	// --- Act ---
	token, err := s.logic.SendLoginChallengeCode(ctx, req)

	// --- Assert ---
	s.Require().Error(err)
	s.Empty(token)
	s.Equal(expectedErr, err)
}
