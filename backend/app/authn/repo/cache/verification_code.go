package cache

import (
	"context"
	"fmt"

	redis "github.com/redis/go-redis/v9"

	"github.com/MoeGolibrary/moego/backend/app/authn/config"
	authnutils "github.com/MoeGolibrary/moego/backend/app/authn/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// Purpose defines the purpose of a verification code to prevent misuse across different flows.
// Using a specific purpose for each flow ensures that a token generated for one process
// (e.g., phone number binding) cannot be used in another (e.g., MFA login).
type Purpose string

const (
	// PurposePhoneNumberBinding is for verifying a phone number when adding it as an MFA factor.
	PurposePhoneNumberBinding Purpose = "phone_number_binding"
	// PurposeMfaLogin is for MFA verification during the login process.
	PurposeMfaLogin Purpose = "mfa_login"
)

const (
	// verificationCodeKeyTmpl is the Redis key template for storing verification code data.
	// Format: verification_code:{purpose}:{token}
	verificationCodeKeyTmpl = "verification_code:%s:%s"
	// rateLimitKeyTmpl is the Redis key template for tracking the rate limit of sending codes.
	// Format: verification_code_rate_limit:{purpose}:{account_id}
	rateLimitKeyTmpl = "verification_code_rate_limit:%s:%d"
)

const (
	// Redis hash field keys used for storing verification code data.
	fieldCode       = "code"        // The 6-digit verification code.
	fieldE164Number = "e164_number" // The phone number the code was sent to.
	fieldAttempt    = "attempt"     // The number of verification attempts made.

	// verifyScriptTmpl is a Lua script template that atomically verifies the code and e164 number,
	// increments the attempt counter, and manages the lifecycle based on the attempt limit.
	//
	// Template parameters (filled by fmt.Sprintf):
	// 1. %s: The hash field name for the attempt counter (fieldAttempt).
	// 2. %d: The maximum number of allowed verification attempts (cfg.MaxAttempts).
	// 3. %s: The hash field name for the verification code (fieldCode).
	// 4. %s: The hash field name for the E.164 phone number (fieldE164Number).
	//
	// Redis script parameters:
	// KEYS[1]: The key for the verification code hash (e.g., "verification_code:mfa_login:some-token").
	// ARGV[1]: The user-provided verification code to check.
	// ARGV[2]: The E.164 formatted phone number to check.
	verifyScriptTmpl = `
		if redis.call("EXISTS", KEYS[1]) == 0 then return 0 end
		local attempt = redis.call("HINCRBY", KEYS[1], "%s", 1)
		if attempt > %d then
			redis.call("DEL", KEYS[1])
			return 0
		end
		local data = redis.call("HMGET", KEYS[1], "%s", "%s")
		if data[1] ~= ARGV[1] or data[2] ~= ARGV[2] then return 0 end
		redis.call("DEL", KEYS[1])
		return 1
	`

	// createScriptTmpl is a Lua script template that implements a rate limit using a counter.
	// It allows a certain number of attempts within a sliding time window.
	//
	// Template parameters (filled by fmt.Sprintf):
	// 1. %s: The hash field name for the verification code (fieldCode).
	// 2. %s: The hash field name for the E.164 phone number (fieldE164Number).
	// 3. %s: The hash field name for the attempt counter (fieldAttempt).
	//
	// Redis script parameters:
	// KEYS[1]: The key for the rate-limiting counter (e.g., "verification_code_rate_limit:mfa_login:123").
	// KEYS[2]: The key for the verification code hash where the code will be stored.
	// ARGV[1]: The TTL for the rate-limiting key, in seconds.
	// ARGV[2]: The maximum number of allowed requests within the rate-limit window.
	// ARGV[3]: The TTL for the verification code hash, in seconds.
	// ARGV[4]: The generated 6-digit verification code.
	// ARGV[5]: The E.164 formatted phone number the code is being sent to.
	createScriptTmpl = `
		local count = redis.call("INCR", KEYS[1])
		if count == 1 then
			redis.call("EXPIRE", KEYS[1], ARGV[1])
		end
		if count > tonumber(ARGV[2]) then
			return 0
		end
		redis.call("HSET", KEYS[2], "%s", ARGV[4], "%s", ARGV[5], "%s", 0)
		redis.call("EXPIRE", KEYS[2], ARGV[3])
		return 1
`
)

// VerificationCodeRepo defines the business operations for verification code caching.
type VerificationCodeRepo interface {
	Create(ctx context.Context, purpose Purpose, accountID int64, e164Number string) (token, code string, err error)
	Verify(ctx context.Context, purpose Purpose, token, code, e164Number string) bool
}

// redisVerificationCodeRepo is the Redis implementation of VerificationCodeRepo.
type redisVerificationCodeRepo struct {
	redisCli     redis.UniversalClient
	cfg          config.VerificationCodeConfig
	verifyScript *redis.Script
	createScript *redis.Script
}

// NewVerificationCodeRepo is the constructor for production use.
// It initializes dependencies from global sources (e.g., config.Get()).
func NewVerificationCodeRepo() (VerificationCodeRepo, error) {
	cfg := config.Get().MFA.VerificationCode

	return newVerificationCodeRepo(cfg)
}

// newVerificationCodeRepo is the constructor for internal and test use.
// It accepts all dependencies as arguments, making it suitable for mocking.
func newVerificationCodeRepo(cfg config.VerificationCodeConfig) (VerificationCodeRepo, error) {
	cli, err := goredis.New("redis")
	if err != nil {
		return nil, fmt.Errorf("failed to create redis client: %w", err)
	}

	verifyScriptString := fmt.Sprintf(verifyScriptTmpl, fieldAttempt, cfg.MaxAttempts, fieldCode, fieldE164Number)
	createScriptString := fmt.Sprintf(createScriptTmpl, fieldCode, fieldE164Number, fieldAttempt)

	return &redisVerificationCodeRepo{
		redisCli:     cli,
		cfg:          cfg,
		verifyScript: redis.NewScript(verifyScriptString),
		createScript: redis.NewScript(createScriptString),
	}, nil
}

func (r *redisVerificationCodeRepo) Create(
	ctx context.Context, purpose Purpose, accountID int64, e164Number string) (string, string, error) {
	token := authnutils.NewUUID()
	code := authnutils.New6DigitCode()

	limitKey := fmt.Sprintf(rateLimitKeyTmpl, purpose, accountID)
	codeKey := fmt.Sprintf(verificationCodeKeyTmpl, purpose, token)

	result, err := r.createScript.Run(ctx, r.redisCli,
		[]string{limitKey, codeKey},
		r.cfg.RateLimitTTL.Seconds(),
		r.cfg.RateLimitMaxAttempts,
		r.cfg.TTL.Seconds(),
		code,
		e164Number,
	).Result()

	if err != nil {
		log.ErrorContextf(ctx, "failed to run redis script for creating code for account %d: %v", accountID, err)

		return "", "", authnutils.VerificationCodeSendError
	}

	if success, ok := result.(int64); !ok {
		log.ErrorContextf(ctx, "unexpected result type from redis script: %T", result)

		return "", "", authnutils.VerificationCodeSendError
	} else if success == 0 {
		log.WarnContextf(ctx, "verification code rate limit exceeded for account %d", accountID)

		return "", "", authnutils.VerificationCodeRateLimitError
	}

	return token, code, nil
}

func (r *redisVerificationCodeRepo) Verify(
	ctx context.Context, purpose Purpose, token, code, e164Number string,
) bool {
	key := fmt.Sprintf(verificationCodeKeyTmpl, purpose, token)
	result, err := r.verifyScript.Run(ctx, r.redisCli, []string{key}, code, e164Number).Result()
	if err != nil {
		log.ErrorContextf(ctx, "failed to run redis script for verifying code: %v", err)

		return false
	}

	if success, ok := result.(int64); !ok {
		log.ErrorContextf(ctx, "unexpected result type from redis script: %T", result)

		return false
	} else if success == 0 {
		return false
	}

	return true
}
