load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "account",
    srcs = ["account.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/repo/account",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/account/v1:account",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/account/v1:account",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/organization/v1:organization",
    ],
)
