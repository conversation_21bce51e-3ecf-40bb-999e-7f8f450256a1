package db

import (
	"context"
	"errors"
	"sync"

	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

const (
	// pgUniqueViolationCode is the error code for unique_violation in PostgreSQL.
	pgUniqueViolationCode = "23505"
)

var (
	db   Database
	once sync.Once
)

// Database is the interface for the database repository.
type Database interface {
	AuthenticationFactorReadWriter
	TrustedDeviceReadWriter

	// WithTx executes the given function within a database transaction.
	// If the function returns an error, the transaction is rolled back. Otherwise, the transaction is committed.
	WithTx(ctx context.Context, fn func(repo Database) error) error
}

type database struct {
	db *gorm.DB
}

// New returns a new database repository.
func New() Database {
	once.Do(func() {
		gormDB, err := igorm.NewClientProxy("postgres.moego_account")
		if err != nil {
			panic(err)
		}
		db = &database{db: gormDB}
	})

	return db
}

func (d *database) WithTx(ctx context.Context, fn func(repo Database) error) error {
	return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(&database{db: tx})
	})
}

// IsUniqueConstraintViolationError checks if the error is a unique constraint violation error.
func IsUniqueConstraintViolationError(err error) bool {
	var pgErr *pgconn.PgError

	return errors.As(err, &pgErr) && pgErr.Code == pgUniqueViolationCode
}
