package db

import (
	"sync"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

var (
	globalDB *gorm.DB
	initDB   sync.Once
)

func newDB() *gorm.DB {
	// 这个名字是自定义的，要和config.yaml中的数据库配置名字一致
	// 得到的 db 是一个原生的 *gorm.DB
	initDB.Do(func() {
		db, err := igorm.NewClientProxy("postgres.moego_account")
		if err != nil {
			panic(err)
		}
		globalDB = db
	})

	return globalDB
}
