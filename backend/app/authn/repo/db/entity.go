package db

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	FactorTypePhoneNumber = "PHONE_NUMBER"
)

// FactorDetails 定义了所有 factor details 结构体必须实现的接口
type FactorDetails interface {
	// isFactorDetails is a marker method to ensure only our detail types can be assigned.
	isFactorDetails()
}

// PhoneNumberDetails 存储 PHONE_NUMBER 类型的具体信息
type PhoneNumberDetails struct {
	E164Number string `json:"e164_number"`
	RegionCode string `json:"region_code"`
}

func (PhoneNumberDetails) isFactorDetails() {}

// AuthenticationFactor 是数据库表的主结构体
type AuthenticationFactor struct {
	ID        string `json:"id" gorm:"column:id;primaryKey"`
	AccountID int64  `json:"account_id" gorm:"column:account_id"`
	Type      string `json:"type" gorm:"column:type"` // e.g., "PHONE_NUMBER"

	// Details 用于和数据库直接交互，存储原始 JSON
	Details datatypes.JSON `json:"details" gorm:"column:details"`

	// ParsedDetails 用于业务逻辑，存储解析后的具体 Details struct
	// gorm:"-" 表示这个字段不会被 GORM 映射到数据库列
	ParsedDetails FactorDetails `json:"-" gorm:"-"`

	CreatedAt time.Time  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"column:deleted_at"`
}

func (*AuthenticationFactor) TableName() string {
	return "authentication_factor"
}

// AfterFind 是一个 GORM hook，在查询后自动执行
func (af *AuthenticationFactor) AfterFind(_ *gorm.DB) (err error) {
	if af.Details == nil {
		return nil
	}

	// 根据 Type 字段的值，决定要解析成哪种 struct
	switch af.Type {
	case FactorTypePhoneNumber:
		var details PhoneNumberDetails
		if err := json.Unmarshal(af.Details, &details); err != nil {
			return fmt.Errorf("failed to unmarshal phone number details: %w", err)
		}
		af.ParsedDetails = details
	// 后续可以添加更多类型，比如 EMAIL、TOTP 等
	default:
		// 如果有未知的类型，可以选择返回错误或保持 ParsedDetails 为 nil
		return fmt.Errorf("unknown authentication factor type: %s", af.Type)
	}

	return nil
}

// BeforeSave 是一个 GORM hook，在保存（创建/更新）前自动执行
func (af *AuthenticationFactor) BeforeSave(_ *gorm.DB) (err error) {
	if af.ParsedDetails == nil {
		// 如果业务逻辑没有设置 ParsedDetails，我们假设 Details 字段中的原始 JSON 是正确的
		return nil
	}

	// 将 ParsedDetails 序列化为 JSON，并存入 Details 字段
	bytes, err := json.Marshal(af.ParsedDetails)
	if err != nil {
		return fmt.Errorf("failed to marshal details to json: %w", err)
	}
	af.Details = bytes

	return nil
}

// TrustedDevice maps to the trusted_device table.
type TrustedDevice struct {
	ID             string    `gorm:"column:id;primaryKey"`
	AccountID      int64     `gorm:"column:account_id"`
	DeviceID       string    `gorm:"column:device_id"`
	CreatedAt      time.Time `gorm:"column:created_at"`
	LastVerifiedAt time.Time `gorm:"column:last_verified_at"`
}

func (*TrustedDevice) TableName() string {
	return "trusted_device"
}
