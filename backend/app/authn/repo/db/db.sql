CREATE TABLE authentication_factor (
    id TEXT PRIMARY KEY,
    account_id BIGINT NOT NULL,
    type TEXT NOT NULL,
    details JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);

COMMENT ON TABLE authentication_factor IS 'Stores registered MFA factors for users. Each row represents a single MFA method configured by an account.';
COMMENT ON COLUMN authentication_factor.id IS 'Unique identifier for the factor (e.g., UUID), as a string.';
COMMENT ON COLUMN authentication_factor.account_id IS 'Account id.';
COMMENT ON COLUMN authentication_factor.type IS 'The type of the MFA factor (e.g., ''PHONE_NUMBER'').';
COMMENT ON COLUMN authentication_factor.details IS 'A JSONB column to store type-specific details for the factor. For a PHONE_NUMBER factor, this would store: ''{"phone_number": "+***********"}''.';
COMMENT ON COLUMN authentication_factor.created_at IS 'The timestamp when the factor was created.';
COMMENT ON COLUMN authentication_factor.updated_at IS 'The timestamp when the factor was last updated.';
COMMENT ON COLUMN authentication_factor.deleted_at IS 'The timestamp when the factor was soft-deleted. A NULL value means the factor is active.';

CREATE TABLE trusted_device (
    id TEXT PRIMARY KEY,
    account_id BIGINT NOT NULL,
    device_id TEXT NOT NULL,
    created_at   TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_verified_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (account_id, device_id)
);

COMMENT ON TABLE trusted_device is 'Stores information about trusted devices for multi-factor authentication.';
COMMENT ON COLUMN trusted_device.id IS 'The unique identifier for the record.';
COMMENT ON COLUMN trusted_device.account_id IS 'The unique identifier for the account.';
COMMENT ON COLUMN trusted_device.device_id IS 'The unique identifier for the device.';
COMMENT ON COLUMN trusted_device.created_at IS 'The timestamp when the record was created.';
COMMENT ON COLUMN trusted_device.last_verified_at IS 'The timestamp when the device was last verified.';
