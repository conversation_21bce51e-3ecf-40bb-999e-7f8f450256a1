package db

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// TrustedDeviceReadWriter defines the interface for interacting with trusted devices.
type TrustedDeviceReadWriter interface {
	// UpsertTrustedDevice creates a new trusted device record or updates the last_verified_at timestamp
	// if a record with the same account_id and device_id already exists.
	UpsertTrustedDevice(ctx context.Context, device *TrustedDevice) error

	// GetTrustedDevice retrieves a trusted device by account ID and device ID.
	GetTrustedDevice(ctx context.Context, accountID int64, deviceID string) (*TrustedDevice, error)
}

// UpsertTrustedDevice implements the logic to create or update a trusted device.
// If a device with the same account_id and device_id exists, it updates the last_verified_at field.
func (d *database) UpsertTrustedDevice(ctx context.Context, device *TrustedDevice) error {
	now := time.Now()
	device.LastVerifiedAt = now
	device.CreatedAt = now

	return d.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "account_id"}, {Name: "device_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"last_verified_at"}),
	}).Create(device).Error
}

// GetTrustedDevice retrieves a trusted device from the database by account_id and device_id.
// It returns (nil, nil) if the record is not found.
func (d *database) GetTrustedDevice(ctx context.Context, accountID int64, deviceID string) (*TrustedDevice, error) {
	var device TrustedDevice
	err := d.db.WithContext(ctx).
		Where("account_id = ? AND device_id = ?", accountID, deviceID).
		First(&device).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Not found, return nil device and nil error
		}

		return nil, err // Other database error
	}

	return &device, nil
}
