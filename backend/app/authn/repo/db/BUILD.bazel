load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "db",
    srcs = [
        "authentication_factor.go",
        "db.go",
        "entity.go",
        "trusted_device.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/repo/db",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/database/gorm",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
