// Code generated by MockGen. DO NOT EDIT.
// Source: ./db/trusted_device.go
//
// Generated by this command:
//
//	mockgen -source=./db/trusted_device.go -destination=./mock/db/trusted_device_mock.go -package=mockdb
//

// Package mockdb is a generated GoMock package.
package mockdb

import (
	context "context"
	reflect "reflect"

	db "github.com/MoeGolibrary/moego/backend/app/authn/repo/db"
	gomock "go.uber.org/mock/gomock"
)

// MockTrustedDeviceReadWriter is a mock of TrustedDeviceReadWriter interface.
type MockTrustedDeviceReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockTrustedDeviceReadWriterMockRecorder
	isgomock struct{}
}

// MockTrustedDeviceReadWriterMockRecorder is the mock recorder for MockTrustedDeviceReadWriter.
type MockTrustedDeviceReadWriterMockRecorder struct {
	mock *MockTrustedDeviceReadWriter
}

// NewMockTrustedDeviceReadWriter creates a new mock instance.
func NewMockTrustedDeviceReadWriter(ctrl *gomock.Controller) *MockTrustedDeviceReadWriter {
	mock := &MockTrustedDeviceReadWriter{ctrl: ctrl}
	mock.recorder = &MockTrustedDeviceReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTrustedDeviceReadWriter) EXPECT() *MockTrustedDeviceReadWriterMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockTrustedDeviceReadWriter) Get(ctx context.Context, accountID int64, deviceID string) (*db.TrustedDevice, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, accountID, deviceID)
	ret0, _ := ret[0].(*db.TrustedDevice)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockTrustedDeviceReadWriterMockRecorder) Get(ctx, accountID, deviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockTrustedDeviceReadWriter)(nil).Get), ctx, accountID, deviceID)
}

// Upsert mocks base method.
func (m *MockTrustedDeviceReadWriter) Upsert(ctx context.Context, device *db.TrustedDevice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, device)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockTrustedDeviceReadWriterMockRecorder) Upsert(ctx, device any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockTrustedDeviceReadWriter)(nil).Upsert), ctx, device)
}
