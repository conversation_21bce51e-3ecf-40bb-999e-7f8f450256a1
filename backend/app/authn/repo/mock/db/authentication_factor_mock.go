// Code generated by MockGen. DO NOT EDIT.
// Source: ./db/authentication_factor.go
//
// Generated by this command:
//
//	mockgen -source=./db/authentication_factor.go -destination=./mock/db/authentication_factor_mock.go -package=mockdb
//

// Package mockdb is a generated GoMock package.
package mockdb

import (
	context "context"
	reflect "reflect"

	db "github.com/MoeGolibrary/moego/backend/app/authn/repo/db"
	gomock "go.uber.org/mock/gomock"
)

// MockAuthenticationFactorReadWriter is a mock of AuthenticationFactorReadWriter interface.
type MockAuthenticationFactorReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockAuthenticationFactorReadWriterMockRecorder
	isgomock struct{}
}

// MockAuthenticationFactorReadWriterMockRecorder is the mock recorder for MockAuthenticationFactorReadWriter.
type MockAuthenticationFactorReadWriterMockRecorder struct {
	mock *MockAuthenticationFactorReadWriter
}

// NewMockAuthenticationFactorReadWriter creates a new mock instance.
func NewMockAuthenticationFactorReadWriter(ctrl *gomock.Controller) *MockAuthenticationFactorReadWriter {
	mock := &MockAuthenticationFactorReadWriter{ctrl: ctrl}
	mock.recorder = &MockAuthenticationFactorReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthenticationFactorReadWriter) EXPECT() *MockAuthenticationFactorReadWriterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAuthenticationFactorReadWriter) Create(ctx context.Context, factor *db.AuthenticationFactor) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, factor)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockAuthenticationFactorReadWriterMockRecorder) Create(ctx, factor any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAuthenticationFactorReadWriter)(nil).Create), ctx, factor)
}

// Delete mocks base method.
func (m *MockAuthenticationFactorReadWriter) Delete(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockAuthenticationFactorReadWriterMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockAuthenticationFactorReadWriter)(nil).Delete), ctx, id)
}

// Get mocks base method.
func (m *MockAuthenticationFactorReadWriter) Get(ctx context.Context, id int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockAuthenticationFactorReadWriterMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockAuthenticationFactorReadWriter)(nil).Get), ctx, id)
}

// GetPhoneNumberFactor mocks base method.
func (m *MockAuthenticationFactorReadWriter) GetPhoneNumberFactor(ctx context.Context, accountID int64) (*db.AuthenticationFactor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhoneNumberFactor", ctx, accountID)
	ret0, _ := ret[0].(*db.AuthenticationFactor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPhoneNumberFactor indicates an expected call of GetPhoneNumberFactor.
func (mr *MockAuthenticationFactorReadWriterMockRecorder) GetPhoneNumberFactor(ctx, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhoneNumberFactor", reflect.TypeOf((*MockAuthenticationFactorReadWriter)(nil).GetPhoneNumberFactor), ctx, accountID)
}

// List mocks base method.
func (m *MockAuthenticationFactorReadWriter) List(ctx context.Context, accountID int64) ([]*db.AuthenticationFactor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, accountID)
	ret0, _ := ret[0].([]*db.AuthenticationFactor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockAuthenticationFactorReadWriterMockRecorder) List(ctx, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockAuthenticationFactorReadWriter)(nil).List), ctx, accountID)
}
