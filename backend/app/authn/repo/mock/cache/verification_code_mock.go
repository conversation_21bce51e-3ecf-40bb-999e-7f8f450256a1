// Code generated by MockGen. DO NOT EDIT.
// Source: ./cache/verification_code.go
//
// Generated by this command:
//
//	mockgen -source=./cache/verification_code.go -destination=./mock/cache/verification_code_mock.go -package=mockcache
//

// Package mockcache is a generated GoMock package.
package mockcache

import (
	context "context"
	reflect "reflect"

	cache "github.com/MoeGolibrary/moego/backend/app/authn/repo/cache"
	gomock "go.uber.org/mock/gomock"
)

// MockVerificationCodeRepo is a mock of VerificationCodeRepo interface.
type MockVerificationCodeRepo struct {
	ctrl     *gomock.Controller
	recorder *MockVerificationCodeRepoMockRecorder
	isgomock struct{}
}

// MockVerificationCodeRepoMockRecorder is the mock recorder for MockVerificationCodeRepo.
type MockVerificationCodeRepoMockRecorder struct {
	mock *MockVerificationCodeRepo
}

// NewMockVerificationCodeRepo creates a new mock instance.
func NewMockVerificationCodeRepo(ctrl *gomock.Controller) *MockVerificationCodeRepo {
	mock := &MockVerificationCodeRepo{ctrl: ctrl}
	mock.recorder = &MockVerificationCodeRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVerificationCodeRepo) EXPECT() *MockVerificationCodeRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockVerificationCodeRepo) Create(ctx context.Context, purpose cache.Purpose, accountID int64, e164Number string) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, purpose, accountID, e164Number)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Create indicates an expected call of Create.
func (mr *MockVerificationCodeRepoMockRecorder) Create(ctx, purpose, accountID, e164Number any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockVerificationCodeRepo)(nil).Create), ctx, purpose, accountID, e164Number)
}

// Verify mocks base method.
func (m *MockVerificationCodeRepo) Verify(ctx context.Context, purpose cache.Purpose, token, code, e164Number string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Verify", ctx, purpose, token, code, e164Number)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Verify indicates an expected call of Verify.
func (mr *MockVerificationCodeRepoMockRecorder) Verify(ctx, purpose, token, code, e164Number any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Verify", reflect.TypeOf((*MockVerificationCodeRepo)(nil).Verify), ctx, purpose, token, code, e164Number)
}
