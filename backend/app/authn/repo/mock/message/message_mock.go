// Code generated by MockGen. DO NOT EDIT.
// Source: ./message/message.go
//
// Generated by this command:
//
//	mockgen -source=./message/message.go -destination=./mock/message/message_mock.go -package=mockmessage
//

// Package mockmessage is a generated GoMock package.
package mockmessage

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// SendVerificationCode mocks base method.
func (m *MockAPI) SendVerificationCode(ctx context.Context, regionCode, e164Number, content string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendVerificationCode", ctx, regionCode, e164Number, content)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendVerificationCode indicates an expected call of SendVerificationCode.
func (mr *MockAPIMockRecorder) SendVerificationCode(ctx, regionCode, e164Number, content any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendVerificationCode", reflect.TypeOf((*MockAPI)(nil).SendVerificationCode), ctx, regionCode, e164Number, content)
}
