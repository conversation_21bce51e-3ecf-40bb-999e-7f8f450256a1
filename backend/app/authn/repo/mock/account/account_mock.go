// Code generated by MockGen. DO NOT EDIT.
// Source: ./account/account.go
//
// Generated by this command:
//
//	mockgen -source=./account/account.go -destination=./mock/account/account_mock.go -package=mockaccount
//

// Package mockaccount is a generated GoMock package.
package mockaccount

import (
	context "context"
	reflect "reflect"

	accountpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	accountsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// CreateSession mocks base method.
func (m *MockAPI) CreateSession(ctx context.Context, req *accountsvcpb.CreateSessionRequest) (*accountsvcpb.CreateSessionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSession", ctx, req)
	ret0, _ := ret[0].(*accountsvcpb.CreateSessionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSession indicates an expected call of CreateSession.
func (mr *MockAPIMockRecorder) CreateSession(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSession", reflect.TypeOf((*MockAPI)(nil).CreateSession), ctx, req)
}

// GetAccountByEmail mocks base method.
func (m *MockAPI) GetAccountByEmail(ctx context.Context, email string) (*accountpb.AccountModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByEmail", ctx, email)
	ret0, _ := ret[0].(*accountpb.AccountModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByEmail indicates an expected call of GetAccountByEmail.
func (mr *MockAPIMockRecorder) GetAccountByEmail(ctx, email any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByEmail", reflect.TypeOf((*MockAPI)(nil).GetAccountByEmail), ctx, email)
}

// InitBusinessSessionContext mocks base method.
func (m *MockAPI) InitBusinessSessionContext(ctx context.Context, accountID, sessionID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitBusinessSessionContext", ctx, accountID, sessionID)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitBusinessSessionContext indicates an expected call of InitBusinessSessionContext.
func (mr *MockAPIMockRecorder) InitBusinessSessionContext(ctx, accountID, sessionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitBusinessSessionContext", reflect.TypeOf((*MockAPI)(nil).InitBusinessSessionContext), ctx, accountID, sessionID)
}

// SyncHubspotAccount mocks base method.
func (m *MockAPI) SyncHubspotAccount(ctx context.Context, accountID int64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SyncHubspotAccount", ctx, accountID)
}

// SyncHubspotAccount indicates an expected call of SyncHubspotAccount.
func (mr *MockAPIMockRecorder) SyncHubspotAccount(ctx, accountID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncHubspotAccount", reflect.TypeOf((*MockAPI)(nil).SyncHubspotAccount), ctx, accountID)
}

// ValidatePassword mocks base method.
func (m *MockAPI) ValidatePassword(ctx context.Context, accountID int64, password string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidatePassword", ctx, accountID, password)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ValidatePassword indicates an expected call of ValidatePassword.
func (mr *MockAPIMockRecorder) ValidatePassword(ctx, accountID, password any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidatePassword", reflect.TypeOf((*MockAPI)(nil).ValidatePassword), ctx, accountID, password)
}
