load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "account",
    srcs = ["account_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/repo/mock/account",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/account/v1:account",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/account/v1:account",
        "@org_uber_go_mock//gomock",
    ],
)
