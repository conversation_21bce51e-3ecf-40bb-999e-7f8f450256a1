load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "message",
    srcs = ["message.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/repo/message",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/authn/utils",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/message_hub/v1:message_hub",
        "@org_golang_google_genproto//googleapis/type/phone_number",
        "@org_golang_google_grpc//codes",
    ],
)
