package service

import (
	"context"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/moego/backend/app/authn/logic"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

// MfaManagementService provides methods for managing a user's MFA settings.
type MfaManagementService struct {
	authnpb.UnimplementedMfaManagementServiceServer
	logic *logic.MfaManagementLogic
}

// NewMfaManagementService creates a new MfaManagementService.
func NewMfaManagementService() *MfaManagementService {
	return &MfaManagementService{
		logic: logic.NewMfaManagementLogic(),
	}
}

// ListAuthenticationFactors lists all MFA factors registered by the authenticated user.
func (s *MfaManagementService) ListAuthenticationFactors(ctx context.Context,
	req *authnpb.ListAuthenticationFactorsRequest) (*authnpb.ListAuthenticationFactorsResponse, error) {
	factors, err := s.logic.ListAuthenticationFactors(ctx, req.AccountId)
	if err != nil {
		return nil, err
	}

	return &authnpb.ListAuthenticationFactorsResponse{
		AuthenticationFactors: factors,
	}, nil
}

// SendPhoneNumberVerificationCode sends a verification code to the user's phone number.
func (s *MfaManagementService) SendPhoneNumberVerificationCode(ctx context.Context,
	req *authnpb.SendPhoneNumberVerificationCodeRequest) (*authnpb.SendPhoneNumberVerificationCodeResponse, error) {
	token, err := s.logic.SendPhoneNumberVerificationCode(ctx, req)
	if err != nil {
		return nil, err
	}

	return &authnpb.SendPhoneNumberVerificationCodeResponse{
		VerificationToken: token,
	}, nil
}

// AddPhoneNumberFactor adds a phone number factor to the user's account.
func (s *MfaManagementService) AddPhoneNumberFactor(ctx context.Context,
	req *authnpb.AddPhoneNumberFactorRequest) (*authnpb.AddPhoneNumberFactorResponse, error) {
	factor, err := s.logic.AddPhoneNumberFactor(ctx, req)
	if err != nil {
		return nil, err
	}

	return &authnpb.AddPhoneNumberFactorResponse{
		AuthenticationFactor: factor,
	}, nil
}

// DeleteAuthenticationFactor deletes an authentication factor.
func (s *MfaManagementService) DeleteAuthenticationFactor(ctx context.Context,
	req *authnpb.DeleteAuthenticationFactorRequest) (*emptypb.Empty, error) {
	err := s.logic.DeleteAuthenticationFactor(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
