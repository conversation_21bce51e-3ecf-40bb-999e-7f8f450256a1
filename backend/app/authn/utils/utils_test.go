package authnutils

import (
	"regexp"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewUUID(t *testing.T) {
	uuid := NewUUID()
	assert.NotEmpty(t, uuid)
	assert.Len(t, uuid, 32, "should be a 32-character hex string")
	match, _ := regexp.MatchString(`^[a-f0-9]{32}$`, uuid)
	assert.True(t, match, "should be a valid UUID")
}

func TestNewUUIDv7(t *testing.T) {
	uuidV7 := NewUUIDv7()
	assert.NotEmpty(t, uuidV7)
	assert.Len(t, uuidV7, 32, "should be a 32-character hex string")
	match, _ := regexp.MatchString(`^[a-f0-9]{32}$`, uuidV7)
	assert.True(t, match, "should be a valid hex string")
}

func TestNew6DigitCode(t *testing.T) {
	for i := 0; i < 100; i++ {
		code := New6DigitCode()
		assert.Len(t, code, 6, "should be 6 digits long")

		value, err := strconv.ParseInt(code, 10, 64)
		assert.NoError(t, err)

		_, isWeak := weakCodes[value]
		assert.False(t, isWeak, "should not be a weak code")
	}
}

func TestRandInt(t *testing.T) {
	maxValue := int64(100)
	for i := 0; i < 100; i++ {
		val := RandInt(maxValue)
		assert.True(t, val >= 0 && val < maxValue, "should be in range [0, maxValue)")
	}
}

func TestRandInt_ZeroMax(t *testing.T) {
	// rand.Int panics if max <= 0, so we expect a panic here.
	assert.Panics(t, func() {
		RandInt(0)
	})
	assert.Panics(t, func() {
		RandInt(-1)
	})
}
