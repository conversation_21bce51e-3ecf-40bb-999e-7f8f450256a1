package authnutils

import (
	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

var (
	AccountNotFoundError   = errs.Newm(codes.NotFound, "Account not found.")
	AccountOrPasswordError = errs.Newm(codes.InvalidArgument, "Account or password error.")

	VerificationCodeSendError      = errs.Newm(codes.Internal, "Failed to send verification code.")
	VerificationCodeMismatchError  = errs.Newm(codes.InvalidArgument, "Verification code mismatch.")
	VerificationCodeRateLimitError = errs.Newm(codes.ResourceExhausted,
		"Request is too frequent, please try again later.")

	CreatePhoneNumberFactorError = errs.Newm(codes.Internal,
		"Failed to create phone number factor. Please try again later.")
	NoPhoneNumberFactorError = errs.Newm(codes.NotFound, "Phone number factor not found.")

	LoginError = errs.Newm(codes.Internal, "Failed to login. Please try again later.")
)
