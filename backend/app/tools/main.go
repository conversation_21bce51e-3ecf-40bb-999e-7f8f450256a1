package main

import (
	"log"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
	"github.com/MoeGolibrary/moego/backend/app/tools/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	tools "github.com/MoeGolibrary/moego/backend/proto/tools/v1"
)

func main() {
	// 设置全局时区为 UTC
	time.Local = time.UTC

	s := rpc.NewServer()

	adminerCfg := configinit.InitAdminerManagementLocalConfig("./config")

	messageConfig := configinit.InitMessageAlertLocalConfig()

	taskRunner, err := service.NewTaskRunnerService(messageConfig)
	if err != nil {
		log.Fatal(err)
	}

	// 这里需要注册grpc 的service
	grpc.Register(s, &tools.DeployPlatformService_ServiceDesc, service.NewDeployPlatformService())
	grpc.Register(s, &tools.AdminerManagementService_ServiceDesc,
		service.NewAdminerManagementService(adminerCfg.AdminerManagement.BaseToken))
	grpc.Register(s, &tools.TaskRunner_ServiceDesc, taskRunner)

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
	log.Println("server started")
}
