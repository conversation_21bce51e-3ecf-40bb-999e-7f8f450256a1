package service

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/datadog"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/slack"
)

// MessageAuditService handles the periodic audit of messages
type MessageAuditService struct {
	ddClient     *datadog.DDClient
	openaiClient *datadog.OpenAIClient
	slackClient  *slack.Client
	slackChannel string
	config       *configinit.MessageConfig
}

// NewMessageAuditService creates a new message audit service
func NewMessageAuditService(config *configinit.MessageConfig) (*MessageAuditService, error) {
	// Create Datadog client
	ddClient, err := datadog.NewDDClient(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Datadog client: %w", err)
	}

	// Create OpenAI client
	openaiClient := datadog.NewAuditClient(config)

	// Create Slack client
	slackClient := slack.NewSlackClient(config)

	return &MessageAuditService{
		ddClient:     ddClient,
		openaiClient: openaiClient,
		slackClient:  slackClient,
		slackChannel: config.MessageAlert.SlackChannel,
		config:       config,
	}, nil
}

// PullLogsAuditAndSend pulls logs from Datadog, audits them with OpenAI, and sends results to Slack
func (s *MessageAuditService) PullLogsAuditAndSend() error {
	// Pull logs and audit them
	result, err := s.ddClient.PullLogsAndAudit(context.Background(), s.openaiClient)
	if err != nil {
		return fmt.Errorf("failed to pull logs and audit: %w", err)
	}

	// If we have results, send them to Slack
	if result != "" && result != "无风险短信" {
		result += " <@U02CC02EPNJ> <@U04CFB2JX9C> <@U05BR4MMXTM>"
		err = s.slackClient.SendMessage2Channel(s.slackChannel, result)
		if err != nil {
			return fmt.Errorf("failed to send message to Slack: %w", err)
		}
	}

	return nil
}
