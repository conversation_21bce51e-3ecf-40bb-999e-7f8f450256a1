load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "adminer_management.go",
        "deploy_platform.go",
        "message_audit.go",
        "task_runner.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/configinit",
        "//backend/app/tools/logic/deployplatform/state",
        "//backend/app/tools/repo/adminermng",
        "//backend/app/tools/repo/datadog",
        "//backend/app/tools/repo/deployplatform",
        "//backend/app/tools/repo/deployplatform/entity",
        "//backend/app/tools/repo/slack",
        "//backend/common/rpc/framework/config",
        "//backend/common/rpc/framework/log",
        "//backend/proto/tools/v1:tools",
        "@com_github_slack_go_slack//:slack",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "adminer_management_test.go",
        "message_audit_test.go",
    ],
    embed = [":service"],
    deps = [
        "//backend/app/tools/configinit",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "@com_github_stretchr_testify//assert",
    ],
)
