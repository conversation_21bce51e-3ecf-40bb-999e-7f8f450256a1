server:
  app: moego-tools
  server: moego-tools
  filter:
    - recovery
    - debuglog
  service:
    - name: backend.proto.tools.v1.DeployPlatformService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.tools.v1.AdminerManagementService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.tools.v1.TaskRunner
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: postgres
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_tools?sslmode=disable
      protocol: gorm
      transport: gorm
      timeout: 600000
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true
          server_addr: 127.0.0.1:8848
          username: user_test
          password: 5kkAGF3cf33fFmhCjrptRYxNw3JBk8B7
          namespace: f4875c9e-3b90-48d0-9188-c94bc2a90609
