server:
  app: moego-tools
  server: moego-tools
  filter:
    - recovery
    - debuglog
  service:
    - name: backend.proto.tools.v1.DeployPlatformService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.tools.v1.AdminerManagementService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
    - name: backend.proto.tools.v1.TaskRunner
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: postgres
      target: dsn://postgresql://${secret.datasource.postgres.moego_tools.username}:${secret.datasource.postgres.moego_tools.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_tools?sslmode=disable
      protocol: gorm
      transport: gorm
      timeout: 600000
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true
          server_addr: ${secret.nacos.server-addr}
          username: ${secret.nacos.username}
          password: ${secret.nacos.password}
          namespace: ${secret.nacos.namespace}

# TODO 增加devops的secret
secrets:
  - name: "moego/devops/nacos"
    prefix: "secret.nacos."
  - name: "moego/devops/datasource"
    prefix: "secret.datasource."
