package state

import (
	"encoding/json"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// 定义CI工作流可能的状态常量
const (
	StatusCompleted  = "completed"
	StatusInProgress = "in_progress"
	StatusQueued     = "queued"
	StatusPending    = "pending"
	StatusWaiting    = "waiting"

	ConcluSuccess   = "success"
	ConcluNeutral   = "neutral"
	ConcluSkipped   = "skipped"
	ConcluCancelled = "cancelled"
	ConcluFailure   = "failure"
	ConcluTimedOut  = "timed_out"
	ConcluStartup   = "startup_failure"
)

type CIParameters struct {
	TaskParameters
	Action     string `json:"action"`
	Status     string `json:"status"`
	Conclusion string `json:"conclusion"`
}

// CIPhase 代表CI阶段的状态管理。
type CIPhase struct {
	Phase
}

// NewCIPhase 创建一个新的CIPhase实例，并初始化其状态。
// 参数:
//
//	repo - 部署平台的读写接口。
//	task - 部署任务的实体。
//
// 返回值:
//
//	*CIPhase - 初始化后的CIPhase实例。
//	error - 如果同步数据库状态失败，则返回错误。
func NewCIPhase(repo deployplatform.Deployer, task *entity.DeployTask) (*CIPhase, error) {
	ciPhase := &CIPhase{
		Phase: Phase{
			Repo: repo,
			Task: task,
		},
	}

	return ciPhase, nil
}

// Change 根据当前阶段状态改变任务状态或阶段。
// 参数:
//
//	_ - 未使用的参数，占位符。
//
// 返回值:
//
//	Task - 下一个任务阶段。
//	error - 如果改变状态失败，则返回错误。
func (p *CIPhase) Change(_ map[string]string) (Task, error) {
	// 从数据库中同步当前任务的状态
	if err := p.SyncStateFromDB(); err != nil {
		return nil, err
	}
	// 如果任务已经1小时没有更新，则结束任务。
	if !p.Task.IsEnd() && p.Task.UpdatedAt.Add(time.Hour).Before(time.Now()) {
		p.End(true)

		return p, nil
	}
	currentPhase, err := p.GetCurrentPhase()
	log.Debugf("current phase state: %s, taskID: %d", currentPhase.State, p.Task.ID)
	if err != nil {
		return nil, err
	}
	if currentPhase.EndedAt.Valid {
		return nil, nil
	}

	params, err := p.GetParams()
	if err != nil {
		return nil, err
	}

	log.Debugf("current phase state: %s, params: %+v", currentPhase.State, params)

	switch currentPhase.State {
	case deployplatform.Init:
		return p.handleInitState()
	case deployplatform.Running:
		return p.handleRunningState(params)
	case deployplatform.Succeeded:
		return p.handleEndState()
	default:
		p.End(true)
	}

	return p, nil
}

// handleInitState 处理 Init 状态。
func (p *CIPhase) handleInitState() (Task, error) {
	log.Debugf("CI Init->Running, taskID: %d", p.Task.ID)
	if err := p.SetState(deployplatform.Running); err != nil {
		return nil, err
	}

	return p, nil
}

// handleRunningState 处理 Running 状态。
func (p *CIPhase) handleRunningState(ciParams *CIParameters) (Task, error) {
	nextState := getNewStateFromCIParameters(ciParams)
	if nextState == deployplatform.Running {
		return p, nil
	}
	log.Debugf("CI Running->%s, taskID: %d", nextState, p.Task.ID)
	if err := p.SetState(nextState); err != nil {
		return nil, err
	}

	return p, nil
}

// handleEndState 处理 Skipped 和 Succeeded 状态。
func (p *CIPhase) handleEndState() (Task, error) {
	p.End(true)

	return p, nil
}

// End Phase, Maybe End Task
func (p *CIPhase) End(endTask bool) {
	for _, phase := range p.Phases {
		if phase.Type == deployplatform.PhaseCI {
			p.endPhaseOrTask(phase, endTask)

			break
		}
	}
}

// GetParams 获取CI参数
func (p *CIPhase) GetParams() (*CIParameters, error) {
	currentPhase, err := p.GetCurrentPhase()
	if err != nil {
		return nil, err
	}
	var ciParams CIParameters
	if err := json.Unmarshal(currentPhase.Parameters, &ciParams); err != nil {
		return nil, err
	}
	if err := json.Unmarshal(p.Task.Parameters, &ciParams); err != nil {
		return nil, err
	}

	return &ciParams, nil
}

// getNewStateFromCIParameters 根据CI参数确定新的状态。
// 参数:
//
//	params - CI工作流的参数。
//
// 返回值:
//
//	string - 新的状态。
func getNewStateFromCIParameters(params *CIParameters) string {
	switch params.Status {
	case StatusCompleted:
		switch params.Conclusion {
		case ConcluSuccess, ConcluNeutral:
			return deployplatform.Succeeded
		case ConcluSkipped:
			return deployplatform.Skipped
		case ConcluCancelled:
			return deployplatform.Cancelled
		case ConcluStartup, ConcluTimedOut, ConcluFailure:
			return deployplatform.Failed
		default:
			return deployplatform.End
		}
	case StatusInProgress, StatusQueued, StatusPending, StatusWaiting:
		return deployplatform.Running
	default:
		return deployplatform.Running
	}
}
