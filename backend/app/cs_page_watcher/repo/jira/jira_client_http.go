package jira

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	jira "github.com/andygrunwald/go-jira"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// JqlSearchRequest represents the request body for the JQL search API
type JqlSearchRequest struct {
	Jql        string   `json:"jql"`
	StartAt    int      `json:"startAt,omitempty"`
	MaxResults int      `json:"maxResults,omitempty"`
	Fields     []string `json:"fields,omitempty"`
}

// JqlSearchResponse represents the response from the JQL search API
type JqlSearchResponse struct {
	Issues     []JqlIssueResponse `json:"issues"`
	StartAt    int                `json:"startAt"`
	MaxResults int                `json:"maxResults"`
	Total      int                `json:"total"`
}

// JqlIssueResponse represents an issue in the JQL search response
type JqlIssueResponse struct {
	ID     string `json:"id"`
	Key    string `json:"key"`
	Fields struct {
		Summary string `json:"summary"`
		Status  struct {
			Name string `json:"name"`
		} `json:"status"`
	} `json:"fields"`
}

// searchJql performs a JQL search using the new Jira API endpoint with pagination
func (r *IssueRepositoryIns) searchJql(jql string) ([]jira.Issue, error) {
	var allIssues []jira.Issue
	startAt := 0
	maxResults := 50

	for {
		// Prepare the request body
		reqBody := JqlSearchRequest{
			Jql:        jql,
			StartAt:    startAt,
			MaxResults: maxResults,
			Fields:     []string{"summary", "status"},
		}

		// Convert to JSON
		jsonBody, err := json.Marshal(reqBody)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %v", err)
		}

		// Create the HTTP request
		url := fmt.Sprintf("%srest/api/3/search/jql", r.baseURL)
		httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
		if err != nil {
			return nil, fmt.Errorf("failed to create HTTP request: %v", err)
		}

		// Set headers
		httpReq.Header.Set("Content-Type", "application/json")
		httpReq.Header.Set("Accept", "application/json")
		httpReq.SetBasicAuth(r.email, r.apiToken)

		// Perform the request
		resp, err := r.httpClient.Do(httpReq)
		if err != nil {
			return nil, fmt.Errorf("failed to perform HTTP request: %v", err)
		}
		defer resp.Body.Close()

		// Read the response body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read response body: %v", err)
		}

		// Check for HTTP errors
		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(body))
		}

		// Parse the response
		var searchResp JqlSearchResponse
		if err := json.Unmarshal(body, &searchResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal response: %v", err)
		}

		// Convert to jira.Issue format
		for _, issue := range searchResp.Issues {
			jiraIssue := jira.Issue{
				ID:  issue.ID,
				Key: issue.Key,
				Fields: &jira.IssueFields{
					Summary: issue.Fields.Summary,
					Status: &jira.Status{
						Name: issue.Fields.Status.Name,
					},
				},
			}
			allIssues = append(allIssues, jiraIssue)
		}

		// Check if we've fetched all issues
		if searchResp.Total <= startAt+len(searchResp.Issues) {
			break
		}
		startAt += len(searchResp.Issues)

		log.Infof("Fetched %d issues, total: %d, continuing pagination...", len(searchResp.Issues), searchResp.Total)
	}

	return allIssues, nil
}
