package configloader

import (
	"context"
	"sync"
	"sync/atomic"

	yaml "gopkg.in/yaml.v3"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const csPageConfigKey = "cs-page-watcher.yaml"

// Schedule represents a team schedule configuration
type Schedule struct {
	Name                string `yaml:"name"`
	ScheduleID          string `yaml:"schedule_id"`
	AdminTaskScheduleID string `yaml:"admin_task_schedule_id"`
	TeamHandle          string `yaml:"team_handle"`
}

// CSPageConfig represents the structure of your deploy-platform.yaml configuration
type CSPageConfig struct {
	// Define your configuration fields here
	ExampleKey               string              `yaml:"example_key"`
	ComponentsSquadsMapping  map[string]string   `yaml:"components_squads_mapping"`
	RefUser                  []string            `yaml:"ref_user"`
	DataImportUserEmails     []string            `yaml:"data_import_user_emails"`
	SquadSlackChannelMapping map[string]string   `yaml:"squad_slack_channel_mapping"`
	TeamSchedules            map[string]Schedule `yaml:"team_schedules"`
}

var GlobalNacosCsPageConfig atomic.Pointer[CSPageConfig]

var initNacos sync.Once

// InitNacosConfigAndGet initializes Nacos configuration and returns the loaded config.
// It should be called once during application startup.
func InitNacosConfigAndGet(ctx context.Context) *CSPageConfig {
	initNacosConfig(ctx)

	return GlobalNacosCsPageConfig.Load()
}

func initNacosConfig(ctx context.Context) {
	initNacos.Do(func() {
		cfgClient := config.Get("nacos")
		r, err := cfgClient.Get(ctx, csPageConfigKey)
		if err != nil {
			log.Fatalf("Failed to get initial config from Nacos: %v", err)
		}

		nacosCfg := &CSPageConfig{}
		log.Infof("Initial config from Nacos: %s", r.Value())
		if err := yaml.Unmarshal([]byte(r.Value()), nacosCfg); err != nil {
			log.Fatalf("Failed to unmarshal initial config from Nacos: %v", err)
		}
		GlobalNacosCsPageConfig.Store(nacosCfg)
		watchConfig(ctx, cfgClient)
	})
}

func watchConfig(ctx context.Context, cfgClient config.KVConfig) {
	c, err := cfgClient.Watch(ctx, csPageConfigKey)
	if err != nil {
		log.Fatalf("Failed to watch config: %v", err)
	}

	// 启动协程监听变更
	go func() {
		log.Infof("Starting Nacos config watch goroutine.")
		for {
			select {
			case update := <-c:
				log.Infof("Config updated: %s", update.Value())
				nacosCfg := &CSPageConfig{}
				if err := yaml.Unmarshal([]byte(update.Value()), nacosCfg); err != nil {
					log.Errorf("Failed to unmarshal updated config from Nacos: %v. Keeping old config.", err)

					continue // 继续监听下一个更新，保留旧配置
				}
				GlobalNacosCsPageConfig.Store(nacosCfg)
			case <-ctx.Done():
				log.Infof("Nacos config watch goroutine shutting down.")

				return // 协程退出
			}
		}
	}()
}
