package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/jiraslareminder"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
)

func TestScheduledJiraReminder_Remind_Manual(t *testing.T) {
	t.Skip("Skipping manual test for sending real Slack message")
	setUp()
	configloader.InitNacosConfigAndGet(context.Background())

	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	jiraRepo, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)

	slackClient := slack.NewClient(cfg.CsPageWatcher.SlackToken)

	slaReminder := jiraslareminder.NewSLAReminder()

	reminder := &ScheduledJiraReminder{
		slaReminder: slaReminder,
		slackClient: slackClient,
	}

	// 未关闭并且提示break
	issues, err := jiraRepo.GetNewOrUpdatedBugTickets(time.Now().AddDate(0, 0, -30), "Data Modification", false)
	assert.Nil(t, err)
	squadGroups, noSquadGroup := reminder.remind(issues, true, []string{global.PriorityP0, global.PriorityP1})
	t.Logf("squadGroups: %v", squadGroups)
	t.Logf("noSquadGroup: %v", noSquadGroup)
	// reminder.sendSlackReminders(squadGroups, noSquadGroup, "JiraSlaRemind")
}

func TestScheduledJiraReminder_groupMessagesBySquad(t *testing.T) {
	s := &ScheduledJiraReminder{}

	// Mock data
	results := []*jiraslareminder.SLAResult{
		{
			JiraKey:          "BUG-1",
			DevEngineerEmail: "<EMAIL>",
			AssigneeEmail:    "<EMAIL>",
			TimeUntilBreach:  10 * time.Hour,
		},
		{
			JiraKey:          "BUG-2",
			DevEngineerEmail: "<EMAIL>",
			AssigneeEmail:    "",
			TimeUntilBreach:  5 * time.Hour,
		},
		{
			JiraKey:          "BUG-3",
			DevEngineerEmail: "",
			AssigneeEmail:    "<EMAIL>",
			TimeUntilBreach:  20 * time.Hour,
		},
		{
			JiraKey:          "BUG-4",
			DevEngineerEmail: "<EMAIL>",
			AssigneeEmail:    "<EMAIL>",
			TimeUntilBreach:  1 * time.Hour,
		},
	}

	issueMap := map[string]*jira.Issue{
		"BUG-1": {
			Key:           "BUG-1",
			Summary:       "Bug 1 Summary",
			IssuePriority: "High",
			JiraSquad:     "SquadA",
		},
		"BUG-2": {
			Key:           "BUG-2",
			Summary:       "Bug 2 Summary",
			IssuePriority: "Medium",
			JiraSquad:     "SquadB",
		},
		"BUG-3": {
			Key:           "BUG-3",
			Summary:       "Bug 3 Summary",
			IssuePriority: "Low",
			JiraSquad:     "", // No squad
		},
		"BUG-4": {
			Key:           "BUG-4",
			Summary:       "Bug 4 Summary",
			IssuePriority: "Critical",
			JiraSquad:     "SquadA",
		},
	}

	email2UserIDMap := map[string]string{
		"<EMAIL>":      "U001",
		"<EMAIL>":      "U002",
		"<EMAIL>": "U003",
		"<EMAIL>": "U004",
	}

	squadGroups, noSquadGroup := s.groupMessagesBySquad(results, issueMap, email2UserIDMap)

	expectedSquadGroups := map[string][]string{
		"SquadA": {
			"<https://moego.atlassian.net/browse/BUG-1|BUG-1> [High] Bug 1 Summary (Expires in: 10h 0m) <@U001> <@U003>",
			"<https://moego.atlassian.net/browse/BUG-4|BUG-4> [Critical] Bug 4 Summary (Expires in: 1h 0m) <@U001> <@U003>",
		},
		"SquadB": {
			"<https://moego.atlassian.net/browse/BUG-2|BUG-2> [Medium] Bug 2 Summary (Expires in: 5h 0m) <@U002>",
		},
	}

	expectedNoSquadGroup := []string{
		"<https://moego.atlassian.net/browse/BUG-3|BUG-3> [Low] Bug 3 Summary (Expires in: 20h 0m) <@U004>",
	}

	assert.Equal(t, expectedSquadGroups, squadGroups)
	assert.Equal(t, expectedNoSquadGroup, noSquadGroup)
}

func TestScheduledJiraReminder_filterByPriority(t *testing.T) {
	s := &ScheduledJiraReminder{}

	// Mock SLA results
	results := []*jiraslareminder.SLAResult{
		{JiraKey: "BUG-1"},
		{JiraKey: "BUG-2"},
		{JiraKey: "BUG-3"},
		{JiraKey: "BUG-4"},
		{JiraKey: "BUG-5"},
	}

	// Mock issue map with different priorities
	issueMap := map[string]*jira.Issue{
		"BUG-1": {Key: "BUG-1", IssuePriority: "P0-Block"},
		"BUG-2": {Key: "BUG-2", IssuePriority: "P1-Critical"},
		"BUG-3": {Key: "BUG-3", IssuePriority: "P2-High"},
		"BUG-4": {Key: "BUG-4", IssuePriority: "P3-Moderate"},
		"BUG-5": {Key: "BUG-5", IssuePriority: "P0-Block"},
	}

	t.Run("Filter P0 and P1 priorities", func(t *testing.T) {
		priorityFilter := []string{"P0-Block", "P1-Critical"}
		filtered := s.filterByPriority(results, issueMap, priorityFilter)

		assert.Len(t, filtered, 3) // BUG-1, BUG-2, BUG-5

		expectedKeys := map[string]bool{
			"BUG-1": true,
			"BUG-2": true,
			"BUG-5": true,
		}

		for _, result := range filtered {
			assert.True(t, expectedKeys[result.JiraKey], "Unexpected key: %s", result.JiraKey)
		}
	})

	t.Run("Filter only P0 priority", func(t *testing.T) {
		priorityFilter := []string{"P0-Block"}
		filtered := s.filterByPriority(results, issueMap, priorityFilter)

		assert.Len(t, filtered, 2) // BUG-1, BUG-5

		for _, result := range filtered {
			issue := issueMap[result.JiraKey]
			assert.Equal(t, "P0-Block", issue.IssuePriority)
		}
	})

	t.Run("Empty priority filter returns all results", func(t *testing.T) {
		priorityFilter := []string{}
		filtered := s.filterByPriority(results, issueMap, priorityFilter)

		assert.Len(t, filtered, len(results))
		assert.Equal(t, results, filtered)
	})

	t.Run("Nil priority filter returns all results", func(t *testing.T) {
		filtered := s.filterByPriority(results, issueMap, nil)

		assert.Len(t, filtered, len(results))
		assert.Equal(t, results, filtered)
	})

	t.Run("Missing issue in map is skipped", func(t *testing.T) {
		resultsWithMissing := append(results, &jiraslareminder.SLAResult{JiraKey: "MISSING-1"})
		priorityFilter := []string{"P0-Block", "P1-Critical"}

		filtered := s.filterByPriority(resultsWithMissing, issueMap, priorityFilter)

		assert.Len(t, filtered, 3) // Still only BUG-1, BUG-2, BUG-5
	})

	t.Run("No matching priorities returns empty", func(t *testing.T) {
		priorityFilter := []string{"P5-Low"}
		filtered := s.filterByPriority(results, issueMap, priorityFilter)

		assert.Len(t, filtered, 0)
	})

	t.Run("Using global constants for P0/P1 filtering", func(t *testing.T) {
		// Import the global package to use actual constants
		priorityFilter := []string{"P0-Block", "P1-Critical"} // These match global.PriorityP0 and global.PriorityP1
		filtered := s.filterByPriority(results, issueMap, priorityFilter)

		assert.Len(t, filtered, 3) // BUG-1, BUG-2, BUG-5

		// Verify that all filtered results have P0 or P1 priority
		for _, result := range filtered {
			issue := issueMap[result.JiraKey]
			assert.Contains(t, []string{"P0-Block", "P1-Critical"}, issue.IssuePriority)
		}
	})
}
