load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "calendar",
    srcs = ["lodging.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/calendar",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/app/fulfillment/repo/offering",
        "//backend/app/fulfillment/repo/pet",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "calendar_test",
    srcs = ["lodging_test.go"],
    embed = [":calendar"],
    deps = [
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
    ],
)
