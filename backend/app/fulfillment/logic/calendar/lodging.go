package calendar

import (
	"context"
	"fmt"
	"sort"
	"time"

	date "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/offering"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/pet"
	calendarpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type Logic struct {
	appointmentRepo    appointment.ReadWriter
	fulfillmentRepo    fulfillment.ReadWriter
	lodgingReader      offering.LodgingReader
	serviceInstanceCli serviceinstance.ReadWriter
	petCli             pet.ReadWriter
}

func New() *Logic {
	return &Logic{
		appointmentRepo:    appointment.New(),
		fulfillmentRepo:    fulfillment.New(),
		lodgingReader:      offering.NewLodgingReader(),
		serviceInstanceCli: serviceinstance.New(),
		petCli:             pet.New(),
	}
}

func NewByParams(
	appointmentRepo appointment.ReadWriter,
	fulfillmentRepo fulfillment.ReadWriter,
	lodgingReader offering.LodgingReader,
	serviceInstanceCli serviceinstance.ReadWriter,
	petCli pet.ReadWriter,
) *Logic {
	return &Logic{
		appointmentRepo:    appointmentRepo,
		fulfillmentRepo:    fulfillmentRepo,
		lodgingReader:      lodgingReader,
		serviceInstanceCli: serviceInstanceCli,
		petCli:             petCli,
	}
}

// GetLodgingCalendar 获取住宿日历
func (l *Logic) GetLodgingCalendar(ctx context.Context,
	req *calendarpb.GetLodgingCalendarRequest) (*calendarpb.GetLodgingCalendarResponse, error) {
	// 1. 根据company_id、business_id、start_time、end_time查询appointment和fulfillment数据
	appointments, fulfillments, err := l.queryAppointmentAndFulfillment(ctx, req)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to query appointment and fulfillment: %v", err)
	}

	// 3. 根据去重后的lodging_id列表调用ListLodgingUnit接口
	lodgingUnits, err := l.getLodgingUnits(ctx, req.GetBusinessId(), []int64{})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get lodging units: %v", err)
	}
	// 对lodging_id去重
	lodgingTypeIDsMap := make(map[int64]bool)
	for _, l := range lodgingUnits {
		if l.LodgingTypeId > 0 {
			lodgingTypeIDsMap[l.LodgingTypeId] = true
		}
	}

	// 转换为lodging_id列表
	var lodgingTypeIDs []int64
	for lodgingID := range lodgingTypeIDsMap {
		lodgingTypeIDs = append(lodgingTypeIDs, lodgingID)
	}

	// 4. 根据company_id、business_id调用GetLodgingTypeListByCompanyID
	lodgingTypes, err := l.getLodgingTypes(ctx, req.GetCompanyId(), lodgingTypeIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get lodging types: %v", err)
	}

	// 5. 组装响应数据
	var startTime, endTime time.Time
	if req.GetStartTime() != nil {
		startTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime() != nil {
		endTime = req.GetEndTime().AsTime()
	}

	// 构建平铺的数据结构
	calendarLodgingTypes := l.buildCalendarLodgingTypes(lodgingTypes, lodgingUnits, fulfillments, startTime, endTime)
	calendarLodgingUnits := l.buildCalendarLodgingUnits(lodgingUnits)
	calendarLodgingAppointments := l.buildCalendarLodgingAppointments(appointments, fulfillments, lodgingUnits)

	// 6. 获取宠物信息（整合GetPetsByTimeRange功能）
	pets, err := l.getPetsByTimeRange(ctx, req, appointments)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get pets by time range: %v", err)
	}

	// 7. 获取宠物统计数据（整合GetPetCountByTimeRange功能）
	dailyPetCounts, err := l.getPetCountByTimeRange(fulfillments)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get pet count by time range: %v", err)
	}

	// 8. 获取ServiceInstances数据
	serviceInstances, err := l.getServiceInstances(ctx, fulfillments)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get service instances: %v", err)
	}

	return &calendarpb.GetLodgingCalendarResponse{
		LodgingTypes:        calendarLodgingTypes,
		LodgingUnits:        calendarLodgingUnits,
		LodgingAppointments: calendarLodgingAppointments,
		Pets:                pets,
		DailyPetCounts:      dailyPetCounts,
		ServiceInstances:    serviceInstances,
	}, nil
}

// queryAppointmentAndFulfillment 查询appointment和fulfillment数据
func (l *Logic) queryAppointmentAndFulfillment(ctx context.Context,
	req *calendarpb.GetLodgingCalendarRequest) ([]*appointment.Appointment, []*fulfillment.Fulfillment, error) {
	// 构建fulfillment查询参数
	fulfillmentParam := &fulfillment.BaseParam{
		BusinessID: int32(req.GetBusinessId()),
		CompanyID:  int32(req.GetCompanyId()),
	}
	if req.GetStartTime() != nil {
		fulfillmentParam.StartTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime() != nil {
		fulfillmentParam.EndTime = req.GetEndTime().AsTime()
	}

	// 先查询fulfillments
	fulfillments, err := l.fulfillmentRepo.List(ctx, fulfillmentParam, nil)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to query fulfillments: %w", err)
	}

	// 如果没有fulfillments，直接返回空结果
	if len(fulfillments) == 0 {
		return []*appointment.Appointment{}, fulfillments, nil
	}

	// 对appointmentId去重
	appointmentIDMap := make(map[int64]bool)
	for _, f := range fulfillments {
		if f.AppointmentID > 0 {
			appointmentIDMap[f.AppointmentID] = true
		}
	}

	// 转换为appointmentId列表
	var appointmentIDs []int64
	for appointmentID := range appointmentIDMap {
		appointmentIDs = append(appointmentIDs, appointmentID)
	}

	// 根据去重后的appointmentId列表查询appointments
	appointments, err := l.appointmentRepo.GetByIDs(ctx, appointmentIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to query appointments by ids: %w", err)
	}

	return appointments, fulfillments, nil
}

// getLodgingUnits 获取住宿单元信息
func (l *Logic) getLodgingUnits(ctx context.Context, businessID int64,
	lodgingIDs []int64) ([]*offeringpb.LodgingUnit, error) {
	var allLodgingUnits []*offeringpb.LodgingUnit

	response, err := l.lodgingReader.ListLodgingUnit(ctx, businessID)
	if err != nil {
		return nil, fmt.Errorf("failed to list lodging units for lodging_ids %v: %w", lodgingIDs, err)
	}

	allLodgingUnits = append(allLodgingUnits, response.LodgingUnits...)

	return allLodgingUnits, nil
}

// getLodgingTypes 获取住宿类型信息
func (l *Logic) getLodgingTypes(ctx context.Context, companyID int64,
	lodgingTypeIDs []int64) ([]*offeringpb.LodgingType, error) {
	response, err := l.lodgingReader.GetLodgingTypeList(ctx, companyID,
		&offeringpb.LodgingTypeFilter{
			LodgingTypeIds: lodgingTypeIDs,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get lodging types: %w", err)
	}

	return response.LodgingTypes, nil
}

// calculateDailyCapacityUsed 计算每日容量使用情况
func (l *Logic) calculateDailyCapacityUsed(lodgingType *offeringpb.LodgingType, lodgingUnits []*offeringpb.LodgingUnit,
	fulfillments []*fulfillment.Fulfillment, startTime, endTime time.Time) []*calendarpb.DailyCapacityEntry {
	// 生成日期范围
	days := l.generateDateRange(startTime, endTime)

	// 创建lodging unit ID映射
	unitIDMap := make(map[int64]bool)
	for _, unit := range lodgingUnits {
		if unit.LodgingTypeId == lodgingType.Id {
			unitIDMap[unit.Id] = true
		}
	}

	// 按日期和lodging unit分组统计
	petCountPerDayPerLodging := make(map[string]map[int64]int32)

	for _, f := range fulfillments {
		if f.LodgingID > 0 && unitIDMap[f.LodgingID] {
			date := f.StartTime.Format("2006-01-02")
			if petCountPerDayPerLodging[date] == nil {
				petCountPerDayPerLodging[date] = make(map[int64]int32)
			}
			// 每个fulfillment记录代表一只宠物
			petCountPerDayPerLodging[date][f.LodgingID]++
		}
	}

	// 根据LodgingUnitType计算容量使用情况
	var result map[string]int32
	if lodgingType.LodgingUnitType == offeringpb.LodgingUnitType_AREA {
		result = l.calculatePetCountPerDay(days, lodgingUnits, petCountPerDayPerLodging)
	} else {
		result = l.calculateOccupiedRoomPerDay(days, lodgingUnits, petCountPerDayPerLodging)
	}

	// 构建有序的DailyCapacityEntry数组
	return l.buildDailyCapacityEntries(days, result)
}

// buildDailyCapacityEntries 构建有序的DailyCapacityEntry数组，确保日期按顺序排列
func (l *Logic) buildDailyCapacityEntries(days []string, result map[string]int32) []*calendarpb.DailyCapacityEntry {
	// 对日期进行排序，确保按时间顺序
	sortedDays := make([]string, len(days))
	copy(sortedDays, days)
	sort.Strings(sortedDays) // "YYYY-MM-DD" 格式天然支持字典序排序

	var entries []*calendarpb.DailyCapacityEntry

	// 按照排序后的日期顺序构建DailyCapacityEntry数组
	for _, date := range sortedDays {
		var used int32
		if count, exists := result[date]; exists {
			used = count
		}
		entries = append(entries, &calendarpb.DailyCapacityEntry{
			Date: date,
			Used: used,
		})
	}

	return entries
}

// getPetsByTimeRange 根据时间区间获取宠物信息（整合GetPetsByTimeRange功能）
func (l *Logic) getPetsByTimeRange(ctx context.Context, req *calendarpb.GetLodgingCalendarRequest,
	appointments []*appointment.Appointment) ([]*calendarpb.PetInfo, error) {
	// 提取appointmentIDs
	appointmentIDs := make([]int64, 0, len(appointments))
	for _, apt := range appointments {
		appointmentIDs = append(appointmentIDs, int64(apt.ID))
	}

	if len(appointmentIDs) == 0 {
		return []*calendarpb.PetInfo{}, nil
	}

	// 通过appointmentIDs查询serviceInstanceList，统计petIDs并去重
	petIDs, err := l.getPetIDsFromAppointments(ctx, appointmentIDs)
	if err != nil {
		return nil, err
	}

	if len(petIDs) == 0 {
		return []*calendarpb.PetInfo{}, nil
	}

	// 通过petIDs查询pet信息并填充回包
	pets, err := l.getPetInfos(ctx, req.GetCompanyId(), petIDs)
	if err != nil {
		return nil, err
	}

	return pets, nil
}

// getPetCountByTimeRange 根据时间区间统计不同careType的宠物数量（整合GetPetCountByTimeRange功能）
func (l *Logic) getPetCountByTimeRange(fulfillments []*fulfillment.Fulfillment) ([]*calendarpb.DailyPetCount, error) {
	if len(fulfillments) == 0 {
		return []*calendarpb.DailyPetCount{}, nil
	}

	// 分析fulfillment数据，按日期和care_type统计
	dailyCounts, _ := l.analyzeFulfillmentData(fulfillments)

	return dailyCounts, nil
}

// getPetIDsFromAppointments 通过appointmentIDs查询serviceInstanceList，统计petIDs并去重
func (l *Logic) getPetIDsFromAppointments(ctx context.Context, appointmentIDs []int64) ([]int64, error) {
	if len(appointmentIDs) == 0 {
		return []int64{}, nil
	}

	// 批量查询所有appointmentIDs对应的service instances
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentIDs(ctx, appointmentIDs)
	if err != nil {
		return nil, err
	}

	// 统计petIDs并去重
	petIDSet := make(map[int64]bool)
	for _, si := range serviceInstances {
		if si.PetID > 0 {
			petIDSet[int64(si.PetID)] = true
		}
	}

	// 转换为slice并排序以确保结果一致性
	petIDs := make([]int64, 0, len(petIDSet))
	for petID := range petIDSet {
		petIDs = append(petIDs, petID)
	}

	return petIDs, nil
}

// getPetInfos 通过petIDs查询pet信息并转换为PetInfo
func (l *Logic) getPetInfos(ctx context.Context, companyID int64, petIDs []int64) ([]*calendarpb.PetInfo, error) {
	if len(petIDs) == 0 {
		return []*calendarpb.PetInfo{}, nil
	}

	// 批量查询pet信息
	petModels, err := l.petCli.BatchGetPetInfo(ctx, companyID, petIDs)
	if err != nil {
		return nil, err
	}

	// 转换为PetInfo
	pets := make([]*calendarpb.PetInfo, 0, len(petModels))
	for _, petModel := range petModels {
		petInfo := &calendarpb.PetInfo{
			Id:         petModel.GetId(),
			AvatarPath: petModel.GetAvatarPath(),
			Name:       petModel.GetPetName(),
			Type:       int32(petModel.GetPetType()),
		}
		pets = append(pets, petInfo)
	}

	return pets, nil
}

// analyzeFulfillmentData 分析fulfillment数据，按日期和care_type统计
func (l *Logic) analyzeFulfillmentData(fulfillments []*fulfillment.Fulfillment) ([]*calendarpb.DailyPetCount, int) {
	// 按日期分组的统计数据
	dateCareTypeCount := make(map[string]map[int64]int32) // date -> care_type_id -> count
	dateLodgingCount := make(map[string]int32)            // date -> lodging_pet_count
	petIDSet := make(map[int64]bool)                      // 用于统计总宠物数

	for _, f := range fulfillments {
		date := f.StartTime.Format("2006-01-02")
		careTypeID := int64(f.CareType)

		// 初始化日期map
		if dateCareTypeCount[date] == nil {
			dateCareTypeCount[date] = make(map[int64]int32)
		}

		// 统计按care_type分组的数量
		dateCareTypeCount[date][careTypeID]++

		// 统计需要住宿的宠物数量（BOARDING和DAYCARE）
		if f.CareType == 1 || f.CareType == 2 { // 假设1是BOARDING，2是DAYCARE
			dateLodgingCount[date]++
		}

		// 统计总宠物数（去重）
		petIDSet[f.PetID] = true
	}

	// 构建DailyPetCount列表
	var dailyCounts []*calendarpb.DailyPetCount
	for date, careTypeCounts := range dateCareTypeCount {
		dailyCount := &calendarpb.DailyPetCount{
			Date:                date,
			PetCareTypeCount:    careTypeCounts,
			PetCountWithLodging: dateLodgingCount[date],
		}
		dailyCounts = append(dailyCounts, dailyCount)
	}

	// 按日期排序
	sort.Slice(dailyCounts, func(i, j int) bool {
		return dailyCounts[i].Date < dailyCounts[j].Date
	})

	return dailyCounts, len(petIDSet)
}

// generateDateRange 生成日期范围
func (l *Logic) generateDateRange(startTime, endTime time.Time) []string {
	var days []string
	current := startTime.Truncate(24 * time.Hour)
	end := endTime.Truncate(24 * time.Hour)

	for current.Before(end) || current.Equal(end) {
		days = append(days, current.Format("2006-01-02"))
		current = current.Add(24 * time.Hour)
	}

	return days
}

// calculatePetCountPerDay 计算每天的总宠物数量（AREA类型）
func (l *Logic) calculatePetCountPerDay(
	days []string,
	lodgingUnits []*offeringpb.LodgingUnit,
	petCountPerDayPerLodging map[string]map[int64]int32,
) map[string]int32 {
	result := make(map[string]int32)

	// 计算每天的总宠物数量
	for _, date := range days {
		totalPetCount := int32(0)
		for _, unit := range lodgingUnits {
			if petCountPerDayPerLodging[date] != nil {
				totalPetCount += petCountPerDayPerLodging[date][unit.Id]
			}
		}
		result[date] = totalPetCount
	}

	return result
}

// calculateOccupiedRoomPerDay 计算每天被占用的房间数量（非AREA类型）
func (l *Logic) calculateOccupiedRoomPerDay(
	days []string,
	lodgingUnits []*offeringpb.LodgingUnit,
	petCountPerDayPerLodging map[string]map[int64]int32,
) map[string]int32 {
	result := make(map[string]int32)

	// 计算每天被占用的房间数量
	for _, date := range days {
		occupiedRoomCount := int32(0)
		for _, unit := range lodgingUnits {
			if petCountPerDayPerLodging[date] != nil && petCountPerDayPerLodging[date][unit.Id] > 0 {
				occupiedRoomCount++
			}
		}
		result[date] = occupiedRoomCount
	}

	return result
}

// buildCalendarLodgingTypes 构建平铺的LodgingType列表
func (l *Logic) buildCalendarLodgingTypes(
	lodgingTypes []*offeringpb.LodgingType,
	lodgingUnits []*offeringpb.LodgingUnit,
	fulfillments []*fulfillment.Fulfillment,
	startTime, endTime time.Time,
) []*calendarpb.LodgingType {
	var calendarLodgingTypes []*calendarpb.LodgingType

	for _, lodgingType := range lodgingTypes {
		// 计算每日容量使用情况
		dailyCapacityUsed := l.calculateDailyCapacityUsed(lodgingType, lodgingUnits, fulfillments, startTime, endTime)

		calendarLodgingType := &calendarpb.LodgingType{
			Id:                lodgingType.Id,
			UnitType:          int32(lodgingType.LodgingUnitType),
			Name:              lodgingType.Name,
			MaxPetNumber:      lodgingType.MaxPetNum,
			MaxPetTotalNumber: lodgingType.MaxPetTotalWeight, // 这里假设是总重量，可能需要调整
			DailyCapacityUsed: dailyCapacityUsed,
			NeedPetSizeFilter: lodgingType.PetSizeFilter,
			PetSizeIds:        lodgingType.PetSizeIds,
			TotalCapacity:     0, // TODO: 需要从其他地方获取总容量信息
		}

		calendarLodgingTypes = append(calendarLodgingTypes, calendarLodgingType)
	}

	return calendarLodgingTypes
}

// buildCalendarLodgingUnits 构建平铺的LodgingUnit列表
func (l *Logic) buildCalendarLodgingUnits(lodgingUnits []*offeringpb.LodgingUnit) []*calendarpb.LodgingUnit {
	var calendarLodgingUnits []*calendarpb.LodgingUnit

	for _, unit := range lodgingUnits {
		calendarLodgingUnit := &calendarpb.LodgingUnit{
			Id:            unit.Id,
			Name:          unit.Name,
			LodgingTypeId: unit.LodgingTypeId,
		}

		calendarLodgingUnits = append(calendarLodgingUnits, calendarLodgingUnit)
	}

	return calendarLodgingUnits
}

// buildCalendarLodgingAppointments 构建平铺的LodgingAppointmentInfo列表
func (l *Logic) buildCalendarLodgingAppointments(
	appointments []*appointment.Appointment,
	fulfillments []*fulfillment.Fulfillment,
	lodgingUnits []*offeringpb.LodgingUnit,
) []*calendarpb.LodgingAppointmentInfo {
	// 创建各种映射
	fulfillmentMap := l.createFulfillmentMap(fulfillments)
	lodgingUnitMap := l.createLodgingUnitMap(lodgingUnits)

	var calendarLodgingAppointments []*calendarpb.LodgingAppointmentInfo
	appointmentIDSet := make(map[int64]bool) // 用于去重

	// 遍历appointments构建响应
	for _, apt := range appointments {
		// 去重：如果appointment已经添加过，则跳过
		if appointmentIDSet[int64(apt.ID)] {
			continue
		}
		// 检查是否有住宿并获取相关信息
		if lodgingInfo := l.extractLodgingInfo(apt, fulfillmentMap, lodgingUnitMap); lodgingInfo != nil {
			appointmentInfo := l.buildLodgingAppointmentInfo(apt, lodgingInfo)
			calendarLodgingAppointments = append(calendarLodgingAppointments, appointmentInfo)
			appointmentIDSet[int64(apt.ID)] = true // 标记已添加
		}
	}

	return calendarLodgingAppointments
}

// LodgingInfo 住宿信息结构体
type LodgingInfo struct {
	ServiceInstanceID int64
	LodgingUnitID     int64
	LodgingTypeID     int64
}

// createFulfillmentMap 创建fulfillment映射，按appointment_id分组
func (l *Logic) createFulfillmentMap(fulfillments []*fulfillment.Fulfillment) map[int64][]*fulfillment.Fulfillment {
	fulfillmentMap := make(map[int64][]*fulfillment.Fulfillment)
	for _, f := range fulfillments {
		if f.AppointmentID > 0 {
			fulfillmentMap[f.AppointmentID] = append(fulfillmentMap[f.AppointmentID], f)
		}
	}

	return fulfillmentMap
}

// createLodgingUnitMap 创建lodging unit映射，用于通过lodging unit id查找lodging type id
func (l *Logic) createLodgingUnitMap(lodgingUnits []*offeringpb.LodgingUnit) map[int64]*offeringpb.LodgingUnit {
	lodgingUnitMap := make(map[int64]*offeringpb.LodgingUnit)
	for _, unit := range lodgingUnits {
		lodgingUnitMap[unit.Id] = unit
	}

	return lodgingUnitMap
}

// extractLodgingInfo 从appointment中提取住宿信息
func (l *Logic) extractLodgingInfo(
	apt *appointment.Appointment,
	fulfillmentMap map[int64][]*fulfillment.Fulfillment,
	lodgingUnitMap map[int64]*offeringpb.LodgingUnit,
) *LodgingInfo {
	fulfillments, exists := fulfillmentMap[int64(apt.ID)]
	if !exists {
		return nil
	}
	// 查找有住宿的fulfillment
	for _, f := range fulfillments {
		if f.LodgingID > 0 { // 有住宿的fulfillment
			lodgingInfo := &LodgingInfo{
				ServiceInstanceID: f.ServiceInstanceID,
				LodgingUnitID:     f.LodgingID, // fulfillment中的LodgingID对应LodgingUnitId
			}
			// 通过lodging unit id查找对应的lodging type id
			if unit, exists := lodgingUnitMap[f.LodgingID]; exists {
				lodgingInfo.LodgingTypeID = unit.LodgingTypeId
			} else {
				lodgingInfo.LodgingTypeID = 0 // 如果找不到对应的lodging unit，设为0
			}

			return lodgingInfo
		}
	}

	return nil
}

// buildLodgingAppointmentInfo 构建LodgingAppointmentInfo
func (l *Logic) buildLodgingAppointmentInfo(apt *appointment.Appointment,
	lodgingInfo *LodgingInfo) *calendarpb.LodgingAppointmentInfo {
	return &calendarpb.LodgingAppointmentInfo{
		Id:                int64(apt.ID),
		CustomerId:        int64(apt.CustomerID),
		ColorCode:         apt.ColorCode,
		StartTime:         timestamppb.New(apt.StartTime),
		EndTime:           timestamppb.New(apt.EndTime),
		LodgingUnitId:     lodgingInfo.LodgingUnitID,
		LodgingTypeId:     lodgingInfo.LodgingTypeID,
		ServiceInstanceId: lodgingInfo.ServiceInstanceID,
	}
}

// getServiceInstances 获取ServiceInstances数据
func (l *Logic) getServiceInstances(ctx context.Context,
	fulfillments []*fulfillment.Fulfillment) ([]*calendarpb.CalendarServiceInstance, error) {
	// 根据fulfillments中的lodgingId > 0来过滤appointmentIDs
	appointmentIDSet := make(map[int64]bool)
	for _, f := range fulfillments {
		if f.LodgingID > 0 && f.AppointmentID > 0 {
			appointmentIDSet[f.AppointmentID] = true
		}
	}

	// 转换为slice
	appointmentIDs := make([]int64, 0, len(appointmentIDSet))
	for appointmentID := range appointmentIDSet {
		appointmentIDs = append(appointmentIDs, appointmentID)
	}

	if len(appointmentIDs) == 0 {
		return []*calendarpb.CalendarServiceInstance{}, nil
	}

	// 通过appointmentIDs查询service instances
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentIDs(ctx, appointmentIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get service instances by appointment ids: %w", err)
	}

	// 转换为CalendarServiceInstance
	calendarServiceInstances := make([]*calendarpb.CalendarServiceInstance, 0, len(serviceInstances))
	for _, si := range serviceInstances {
		calendarSI := l.convertToCalendarServiceInstance(si)
		calendarServiceInstances = append(calendarServiceInstances, calendarSI)
	}

	return calendarServiceInstances, nil
}

// convertTimeToDate 将time.Time转换为date.Date
func (l *Logic) convertTimeToDate(t time.Time) *date.Date {
	return &date.Date{
		Year:  int32(t.Year()),
		Month: int32(t.Month()),
		Day:   int32(t.Day()),
	}
}

// convertToCalendarServiceInstance 将ServiceInstance转换为CalendarServiceInstance
func (l *Logic) convertToCalendarServiceInstance(
	si *serviceinstance.ServiceInstance) *calendarpb.CalendarServiceInstance {
	calendarSI := &calendarpb.CalendarServiceInstance{
		Id:             int64(si.ID),
		IsSplitLodging: si.RootParentID != 0, // 如果有root_parent_id，说明是分割住宿
		DateType:       calendarpb.DateType(si.DateType),
		CareType:       offeringpb.CareCategory(si.CareType),
	}

	// 转换start_date
	if si.StartDate.Valid {
		calendarSI.StartDate = l.convertTimeToDate(si.StartDate.Time)
	}

	// 转换end_date
	if si.EndDate.Valid {
		calendarSI.EndDate = l.convertTimeToDate(si.EndDate.Time)
	}

	// 转换specific_dates
	if len(si.SpecificDates) > 0 {
		calendarSI.SpecificDates = make([]*date.Date, 0, len(si.SpecificDates))
		for _, dateTime := range si.SpecificDates {
			calendarSI.SpecificDates = append(calendarSI.SpecificDates, l.convertTimeToDate(dateTime))
		}
	}

	return calendarSI
}
