package appointment

import (
	"context"

	"github.com/gogo/protobuf/proto"
	"gorm.io/gorm"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	feedingRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/feeding"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/medication"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

// syncPetDetailDef 同步宠物档案的用药和喂养计划
func (l *Logic) syncPetDetailDef(ctx context.Context, companyID int, petSchedules []*pb.CreatePetDetailDef) error {
	if len(petSchedules) == 0 {
		return nil
	}

	// 收集所有宠物的用药和喂养计划
	newPetMedications := make(map[int64][]*pb.AppointmentPetMedicationScheduleDef)
	newPetFeedings := make(map[int64][]*pb.AppointmentPetFeedingScheduleDef)

	for _, petSchedule := range petSchedules {
		petID := petSchedule.GetPetId()

		// 收集用药计划
		var medications []*pb.AppointmentPetMedicationScheduleDef
		for _, service := range petSchedule.GetServices() {
			if service.GetMedications() != nil {
				medications = append(medications, service.GetMedications()...)
			}
		}
		if len(medications) > 0 {
			newPetMedications[petID] = medications
		}

		// 收集喂养计划
		var feedings []*pb.AppointmentPetFeedingScheduleDef
		for _, service := range petSchedule.GetServices() {
			if service.GetFeedings() != nil {
				feedings = append(feedings, service.GetFeedings()...)
			}
		}
		if len(feedings) > 0 {
			newPetFeedings[petID] = feedings
		}
	}

	// 同步用药计划
	if err := l.syncPetProfileMedication(ctx, int64(companyID), newPetMedications); err != nil {
		return err
	}

	// 同步喂养计划
	if err := l.syncPetProfileFeeding(ctx, int64(companyID), newPetFeedings); err != nil {
		return err
	}

	return nil
}

// syncPetProfileMedication 同步宠物档案的用药计划
func (l *Logic) syncPetProfileMedication(ctx context.Context, companyID int64,
	petMedications map[int64][]*pb.AppointmentPetMedicationScheduleDef) error {
	if len(petMedications) == 0 {
		return nil
	}

	// 收集所有宠物ID
	petIDs := make([]int64, 0, len(petMedications))
	for petID := range petMedications {
		petIDs = append(petIDs, petID)
	}

	// 获取现有的用药计划，检查是否已存在
	existPetMedication, err := l.customerCli.ListPetMedicationSchedule(ctx, companyID, petIDs)
	if err != nil {
		log.Errorf("Failed to get existing pet medication schedule: %v", err)
		// 如果获取失败，继续创建，避免阻塞流程
	} else if existPetMedication != nil {
		// 按petId分组现有数据
		existingPetMedicationMap := make(map[int64]bool)
		medications := existPetMedication.GetMedications()
		if len(medications) > 0 {
			for _, medication := range medications {
				existingPetMedicationMap[medication.GetPetId()] = true
			}
		}

		// 只创建不存在的宠物档案
		var toCreate []*businesscustomerpb.BusinessPetMedicationScheduleDef
		for petID, medications := range petMedications {
			if !existingPetMedicationMap[petID] {
				for _, medication := range medications {
					businessMedication := l.convertToBusinessPetMedicationScheduleDef(petID, medication)
					toCreate = append(toCreate, businessMedication)
				}
			}
		}

		if len(toCreate) > 0 {
			_, err := l.customerCli.BatchCreateMedicationSchedule(ctx, companyID, toCreate)

			return err
		}

		return nil
	}

	// 如果获取现有数据失败，直接创建所有数据
	var toCreate []*businesscustomerpb.BusinessPetMedicationScheduleDef
	for petID, medications := range petMedications {
		for _, medication := range medications {
			businessMedication := l.convertToBusinessPetMedicationScheduleDef(petID, medication)
			toCreate = append(toCreate, businessMedication)
		}
	}

	if len(toCreate) > 0 {
		_, err := l.customerCli.BatchCreateMedicationSchedule(ctx, companyID, toCreate)

		return err
	}

	return nil
}

// syncPetProfileFeeding 同步宠物档案的喂养计划
func (l *Logic) syncPetProfileFeeding(ctx context.Context, companyID int64,
	petFeedings map[int64][]*pb.AppointmentPetFeedingScheduleDef) error {
	if len(petFeedings) == 0 {
		return nil
	}

	// 收集所有宠物ID
	petIDs := make([]int64, 0, len(petFeedings))
	for petID := range petFeedings {
		petIDs = append(petIDs, petID)
	}

	// 获取现有的喂养计划，检查是否已存在
	existPetFeeding, err := l.customerCli.ListPetFeedingSchedule(ctx, companyID, petIDs)
	if err != nil {
		log.Errorf("Failed to get existing pet feeding schedule: %v", err)
		// 如果获取失败，继续创建，避免阻塞流程
	} else if existPetFeeding != nil {
		// 按petId分组现有数据
		existingPetFeedingMap := make(map[int64]bool)
		feedings := existPetFeeding.GetFeedings()
		if len(feedings) > 0 {
			for _, feeding := range feedings {
				existingPetFeedingMap[feeding.GetPetId()] = true
			}
		}

		// 只创建不存在的宠物档案
		var toCreate []*businesscustomerpb.BusinessPetFeedingScheduleDef
		for petID, feedings := range petFeedings {
			if !existingPetFeedingMap[petID] {
				for _, feeding := range feedings {
					businessFeeding := l.convertToBusinessPetFeedingScheduleDef(petID, feeding)
					toCreate = append(toCreate, businessFeeding)
				}
			}
		}

		if len(toCreate) > 0 {
			_, err := l.customerCli.BatchCreateFeedingSchedule(ctx, companyID, toCreate)

			return err
		}

		return nil
	}

	// 如果获取现有数据失败，直接创建所有数据
	var toCreate []*businesscustomerpb.BusinessPetFeedingScheduleDef
	for petID, feedings := range petFeedings {
		for _, feeding := range feedings {
			businessFeeding := l.convertToBusinessPetFeedingScheduleDef(petID, feeding)
			toCreate = append(toCreate, businessFeeding)
		}
	}

	if len(toCreate) > 0 {
		_, err := l.customerCli.BatchCreateFeedingSchedule(ctx, companyID, toCreate)

		return err
	}

	return nil
}

// convertToBusinessPetMedicationScheduleDef 转换用药计划
func (l *Logic) convertToBusinessPetMedicationScheduleDef(petID int64,
	medication *pb.AppointmentPetMedicationScheduleDef) *businesscustomerpb.BusinessPetMedicationScheduleDef {
	return &businesscustomerpb.BusinessPetMedicationScheduleDef{
		PetId:            petID,
		MedicationAmount: medication.GetMedicationAmount(),
		MedicationUnit:   medication.GetMedicationUnit(),
		MedicationName:   medication.GetMedicationName(),
		MedicationNote:   medication.GetMedicationNote(),
		MedicationTimes:  l.convertToBusinessPetScheduleTimeDefs(medication.GetMedicationTimes()),
		// TODO: 需要根据selected_date设置相应的字段
	}
}

// convertToBusinessPetFeedingScheduleDef 转换喂养计划
func (l *Logic) convertToBusinessPetFeedingScheduleDef(petID int64,
	feeding *pb.AppointmentPetFeedingScheduleDef) *businesscustomerpb.BusinessPetFeedingScheduleDef {
	return &businesscustomerpb.BusinessPetFeedingScheduleDef{
		PetId:              petID,
		FeedingAmount:      feeding.GetFeedingAmount(),
		FeedingUnit:        feeding.GetFeedingUnit(),
		FeedingType:        feeding.GetFeedingType(),
		FeedingSource:      feeding.GetFeedingSource(),
		FeedingInstruction: proto.String(feeding.GetFeedingInstruction()),
		FeedingTimes:       l.convertToBusinessPetScheduleTimeDefs(feeding.GetFeedingTimes()),
		FeedingNote:        proto.String(feeding.GetFeedingNote()),
	}
}

// convertToBusinessPetScheduleTimeDefs 转换时间定义
func (l *Logic) convertToBusinessPetScheduleTimeDefs(
	times []*pb.BusinessPetScheduleTimeDef) []*businesscustomerpb.BusinessPetScheduleTimeDef {
	var result []*businesscustomerpb.BusinessPetScheduleTimeDef
	for _, time := range times {
		result = append(result, &businesscustomerpb.BusinessPetScheduleTimeDef{
			ScheduleTime: time.GetScheduleTime(),
			ExtraJson:    time.GetExtraJson(),
		})
	}

	return result
}

// savePetScheduleDataInTransaction 在事务中保存宠物的喂养和用药计划数据
func (l *Logic) savePetScheduleDataInTransaction(
	ctx context.Context, tx *gorm.DB, serviceInstances []*ServiceInstanceData) error {
	if len(serviceInstances) == 0 {
		return nil
	}

	var medications []*medication.AppointmentPetMedication
	var feedings []*feedingRepo.AppointmentPetFeeding

	// 遍历所有服务实例，提取用药和喂养计划
	for _, serviceData := range serviceInstances {
		if serviceData.CreateServiceInstanceDef == nil {
			continue
		}

		def := serviceData.CreateServiceInstanceDef
		serviceInstanceID := serviceData.ServiceInstanceID

		// 处理用药计划
		for _, medDef := range def.GetMedications() {
			medication := l.convertMedicationDefToEntity(medDef, serviceInstanceID, serviceData.ServiceInstance)
			if medication != nil {
				medications = append(medications, medication)
			}
		}

		// 处理喂养计划
		for _, feedingDef := range def.GetFeedings() {
			feeding := l.convertFeedingDefToEntity(feedingDef, serviceInstanceID, serviceData.ServiceInstance)
			if feeding != nil {
				feedings = append(feedings, feeding)
			}
		}
	}

	// 批量保存用药计划 - 使用事务连接
	if len(medications) > 0 {
		if err := l.medicationCli.BatchCreateWithTx(ctx, tx, medications); err != nil {
			log.Errorf("Failed to batch create medications: %v", err)

			return err
		}
	}

	// 批量保存喂养计划 - 使用事务连接
	if len(feedings) > 0 {
		if err := l.feedingCli.BatchCreateWithTx(ctx, tx, feedings); err != nil {
			log.Errorf("Failed to batch create feedings: %v", err)

			return err
		}
	}

	return nil
}

// convertMedicationDefToEntity 将用药计划定义转换为数据库实体
func (l *Logic) convertMedicationDefToEntity(medDef *pb.AppointmentPetMedicationScheduleDef,
	serviceInstanceID int64, serviceInstance *serviceinstance.ServiceInstance) *medication.AppointmentPetMedication {
	if medDef == nil || serviceInstance == nil {
		return nil
	}

	medication := &medication.AppointmentPetMedication{
		CompanyID:        int64(serviceInstance.CompanyID),
		AppointmentID:    int64(serviceInstance.AppointmentID),
		PetDetailID:      serviceInstanceID, // TODO: 需要从pet detail中获取
		PetID:            int64(serviceInstance.PetID),
		MedicationAmount: medDef.GetMedicationAmount(),
		MedicationUnit:   medDef.GetMedicationUnit(),
		MedicationName:   medDef.GetMedicationName(),
		MedicationNote:   medDef.GetMedicationNote(),
		DateType:         int32(pb.FeedingMedicationScheduleDateType_EVERYDAY_INCLUDE_CHECKOUT_DATE), // 默认值
		SpecificDates:    "[]",                                                                       // 默认值
	}

	// 处理特定日期配置
	if selectedDate := medDef.GetSelectedDate(); selectedDate != nil {
		medication.DateType = int32(selectedDate.GetDateType())
		if len(selectedDate.GetSpecificDates()) > 0 {
			// 将特定日期转换为JSON字符串存储
			datesJSON := "["
			for i, date := range selectedDate.GetSpecificDates() {
				if i > 0 {
					datesJSON += ","
				}
				datesJSON += "\"" + date + "\""
			}
			datesJSON += "]"
			medication.SpecificDates = datesJSON
		}
	}

	return medication
}

// convertFeedingDefToEntity 将喂养计划定义转换为数据库实体
func (l *Logic) convertFeedingDefToEntity(feedingDef *pb.AppointmentPetFeedingScheduleDef,
	serviceInstanceID int64, serviceInstance *serviceinstance.ServiceInstance) *feedingRepo.AppointmentPetFeeding {
	if feedingDef == nil || serviceInstance == nil {
		return nil
	}

	feeding := &feedingRepo.AppointmentPetFeeding{
		CompanyID:          int64(serviceInstance.CompanyID),
		AppointmentID:      int64(serviceInstance.AppointmentID),
		PetDetailID:        serviceInstanceID, // TODO: 需要从pet detail中获取
		PetID:              int64(serviceInstance.PetID),
		FeedingAmount:      feedingDef.GetFeedingAmount(),
		FeedingUnit:        feedingDef.GetFeedingUnit(),
		FeedingType:        feedingDef.GetFeedingType(),
		FeedingSource:      feedingDef.GetFeedingSource(),
		FeedingInstruction: feedingDef.GetFeedingInstruction(),
		FeedingNote:        feedingDef.GetFeedingNote(),
	}

	return feeding
}
