package appointment

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	timeofday "google.golang.org/genproto/googleapis/type/timeofday"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	feedingRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/feeding"
	fulfillmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/medication"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// Mock interfaces
type MockAppointmentClient struct {
	mock.Mock
}

func (m *MockAppointmentClient) Create(ctx context.Context, appointment *appointment.Appointment) error {
	args := m.Called(ctx, appointment)
	appointment.ID = 123 // 模拟创建成功后的ID
	return args.Error(0)
}

func (m *MockAppointmentClient) GetByID(ctx context.Context, id int) (*appointment.Appointment, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*appointment.Appointment), args.Error(1)
}

func (m *MockAppointmentClient) GetByIDs(ctx context.Context, ids []int64) ([]*appointment.Appointment, error) {
	args := m.Called(ctx, ids)
	return args.Get(0).([]*appointment.Appointment), args.Error(1)
}

func (m *MockAppointmentClient) List(ctx context.Context, param *appointment.BaseParam, filter *appointment.Filter) ([]*appointment.Appointment, error) {
	args := m.Called(ctx, param, filter)
	return args.Get(0).([]*appointment.Appointment), args.Error(1)
}

func (m *MockAppointmentClient) Count(ctx context.Context, param *appointment.BaseParam, filter *appointment.Filter) (int64, error) {
	args := m.Called(ctx, param, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockAppointmentClient) Update(ctx context.Context, appointment *appointment.Appointment) error {
	args := m.Called(ctx, appointment)
	return args.Error(0)
}

func (m *MockAppointmentClient) Delete(ctx context.Context, id int) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockAppointmentClient) BatchCreate(ctx context.Context, appointments []*appointment.Appointment) error {
	args := m.Called(ctx, appointments)
	return args.Error(0)
}

type MockFulfillmentClient struct {
	mock.Mock
}

func (m *MockFulfillmentClient) BatchCreate(ctx context.Context, fulfillments []*fulfillmentRepo.Fulfillment) error {
	args := m.Called(ctx, fulfillments)
	return args.Error(0)
}

func (m *MockFulfillmentClient) GetByServiceInstanceID(ctx context.Context, serviceInstanceID int64) ([]*fulfillmentRepo.Fulfillment, error) {
	args := m.Called(ctx, serviceInstanceID)
	return args.Get(0).([]*fulfillmentRepo.Fulfillment), args.Error(1)
}

func (m *MockFulfillmentClient) DeleteByServiceInstanceID(ctx context.Context, serviceInstanceID int64) error {
	args := m.Called(ctx, serviceInstanceID)
	return args.Error(0)
}

func (m *MockFulfillmentClient) List(ctx context.Context, param *fulfillmentRepo.BaseParam, filter *fulfillmentRepo.Filter) ([]*fulfillmentRepo.Fulfillment, error) {
	args := m.Called(ctx, param, filter)
	return args.Get(0).([]*fulfillmentRepo.Fulfillment), args.Error(1)
}

func (m *MockFulfillmentClient) Count(ctx context.Context, param *fulfillmentRepo.BaseParam, filter *fulfillmentRepo.Filter) (int64, error) {
	args := m.Called(ctx, param, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockFulfillmentClient) Update(ctx context.Context, f *fulfillmentRepo.Fulfillment) error {
	args := m.Called(ctx, f)
	return args.Error(0)
}

func (m *MockFulfillmentClient) GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*fulfillmentRepo.Fulfillment, error) {
	args := m.Called(ctx, appointmentIDs)
	return args.Get(0).([]*fulfillmentRepo.Fulfillment), args.Error(1)
}

type MockServiceInstanceClient struct {
	mock.Mock
}

func (m *MockServiceInstanceClient) Create(ctx context.Context, si *serviceinstance.ServiceInstance) (int, error) {
	args := m.Called(ctx, si)
	return args.Int(0), args.Error(1)
}

func (m *MockServiceInstanceClient) GetByID(ctx context.Context, id int) (*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceClient) GetByIDs(ctx context.Context, ids []int64) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, ids)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceClient) GetByAppointmentID(ctx context.Context, appointmentID int) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, appointmentID)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceClient) GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, appointmentIDs)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceClient) List(ctx context.Context, baseParam *serviceinstance.BaseParam, filter *serviceinstance.Filter) ([]*serviceinstance.ServiceInstance, error) {
	args := m.Called(ctx, baseParam, filter)
	return args.Get(0).([]*serviceinstance.ServiceInstance), args.Error(1)
}

func (m *MockServiceInstanceClient) BatchCreate(ctx context.Context, serviceInstances []*serviceinstance.ServiceInstance) error {
	args := m.Called(ctx, serviceInstances)
	return args.Error(0)
}

func (m *MockServiceInstanceClient) Update(ctx context.Context, si *serviceinstance.ServiceInstance) error {
	args := m.Called(ctx, si)
	return args.Error(0)
}

func (m *MockServiceInstanceClient) Delete(ctx context.Context, id int) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

type MockOfferingServiceClient struct {
	mock.Mock
}

func (m *MockOfferingServiceClient) GetService(ctx context.Context, id int64) (*offeringpb.Service, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*offeringpb.Service), args.Error(1)
}

type MockOfferingCareTypeClient struct {
	mock.Mock
}

func (m *MockOfferingCareTypeClient) GetCareType(ctx context.Context, id int64) (*offeringpb.CareType, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*offeringpb.CareType), args.Error(1)
}

type MockCustomerClient struct {
	mock.Mock
}

func (m *MockCustomerClient) GetCustomerInfo(ctx context.Context, companyID, customerID int64) (*businesscustomerpb.BusinessCustomerModel, error) {
	args := m.Called(ctx, companyID, customerID)
	return args.Get(0).(*businesscustomerpb.BusinessCustomerModel), args.Error(1)
}

func (m *MockCustomerClient) BatchCreateFeedingSchedule(ctx context.Context, companyID int64, feedingSchedules []*businesscustomerpb.BusinessPetFeedingScheduleDef) (*businesscustomersvcpb.BatchCreateFeedingScheduleResponse, error) {
	args := m.Called(ctx, companyID, feedingSchedules)
	return args.Get(0).(*businesscustomersvcpb.BatchCreateFeedingScheduleResponse), args.Error(1)
}

func (m *MockCustomerClient) BatchCreateMedicationSchedule(ctx context.Context, companyID int64, medicationSchedules []*businesscustomerpb.BusinessPetMedicationScheduleDef) (*businesscustomersvcpb.BatchCreateMedicationScheduleResponse, error) {
	args := m.Called(ctx, companyID, medicationSchedules)
	return args.Get(0).(*businesscustomersvcpb.BatchCreateMedicationScheduleResponse), args.Error(1)
}

func (m *MockCustomerClient) ListPetFeedingSchedule(ctx context.Context, companyID int64, petIDs []int64) (*businesscustomersvcpb.ListPetFeedingScheduleResponse, error) {
	args := m.Called(ctx, companyID, petIDs)
	return args.Get(0).(*businesscustomersvcpb.ListPetFeedingScheduleResponse), args.Error(1)
}

func (m *MockCustomerClient) ListPetMedicationSchedule(ctx context.Context, companyID int64, petIDs []int64) (*businesscustomersvcpb.ListPetMedicationScheduleResponse, error) {
	args := m.Called(ctx, companyID, petIDs)
	return args.Get(0).(*businesscustomersvcpb.ListPetMedicationScheduleResponse), args.Error(1)
}

type MockFeedingClient struct {
	mock.Mock
}

func (m *MockFeedingClient) Create(ctx context.Context, feeding *feedingRepo.AppointmentPetFeeding) error {
	args := m.Called(ctx, feeding)
	return args.Error(0)
}

func (m *MockFeedingClient) BatchCreate(ctx context.Context, feedings []*feedingRepo.AppointmentPetFeeding) error {
	args := m.Called(ctx, feedings)
	return args.Error(0)
}

type MockMedicationClient struct {
	mock.Mock
}

func (m *MockMedicationClient) Create(ctx context.Context, medication *medication.AppointmentPetMedication) error {
	args := m.Called(ctx, medication)
	return args.Error(0)
}

func (m *MockMedicationClient) BatchCreate(ctx context.Context, medications []*medication.AppointmentPetMedication) error {
	args := m.Called(ctx, medications)
	return args.Error(0)
}

type MockTransactionManager struct {
	mock.Mock
}

func (m *MockTransactionManager) ExecuteInTransaction(ctx context.Context, operations []func(context.Context, *gorm.DB) error) error {
	args := m.Called(ctx, operations)
	return args.Error(0)
}

func (m *MockTransactionManager) Tx(ctx context.Context, op func(context.Context, *gorm.DB) error) error {
	args := m.Called(ctx, op)
	return args.Error(0)
}

// Helper functions
func createTestLogic() *Logic {
	return &Logic{
		appointmentCli:      &MockAppointmentClient{},
		fulfillmentCli:      &MockFulfillmentClient{},
		serviceInstanceCli:  &MockServiceInstanceClient{},
		offeringServiceCli:  &MockOfferingServiceClient{},
		offeringCareTypeCli: &MockOfferingCareTypeClient{},
		customerCli:         &MockCustomerClient{},
		feedingCli:          &MockFeedingClient{},
		medicationCli:       &MockMedicationClient{},
		tx:                  &MockTransactionManager{},
	}
}

func createValidCreateAppointmentRequest() *pb.CreateAppointmentRequest {
	now := time.Now()
	return &pb.CreateAppointmentRequest{
		BusinessId: 1,
		CompanyId:  1,
		CustomerId: 1,
		StartTime:  timestamppb.New(now),
		EndTime:    timestamppb.New(now.Add(24 * time.Hour)),
		ColorCode:  "#FF0000",
		Pets: []*pb.CreatePetDetailDef{
			{
				PetId: 1,
				Services: []*pb.CreateServiceInstanceDef{
					{
						Id: 1,
						Time: &pb.CreateServiceInstanceDef_TimeConfig{
							TimeConfig: &pb.TimeConfig{
								StartTime: timestamppb.New(now),
								EndTime:   timestamppb.New(now.Add(24 * time.Hour)),
							},
						},
					},
				},
			},
		},
	}
}

func createValidServiceInfo() *ServiceInfo {
	return &ServiceInfo{
		CareType: &offeringpb.CareType{
			CareCategory: offeringpb.CareCategory_BOARDING,
		},
		Service: &offeringpb.Service{
			Id:         1,
			Name:       "Test Service",
			Type:       offeringpb.Service_SERVICE,
			CareTypeId: 1,
		},
	}
}

// Test New function
func TestNew(t *testing.T) {
	// 由于New()函数会尝试连接真实数据库，我们直接测试Logic结构体的创建
	// 而不是调用New()函数
	logic := &Logic{
		appointmentCli:      &MockAppointmentClient{},
		fulfillmentCli:      &MockFulfillmentClient{},
		serviceInstanceCli:  &MockServiceInstanceClient{},
		offeringServiceCli:  &MockOfferingServiceClient{},
		offeringCareTypeCli: &MockOfferingCareTypeClient{},
		customerCli:         &MockCustomerClient{},
		feedingCli:          &MockFeedingClient{},
		medicationCli:       &MockMedicationClient{},
		tx:                  &MockTransactionManager{},
	}

	assert.NotNil(t, logic)
	assert.NotNil(t, logic.appointmentCli)
	assert.NotNil(t, logic.fulfillmentCli)
	assert.NotNil(t, logic.serviceInstanceCli)
	assert.NotNil(t, logic.offeringServiceCli)
	assert.NotNil(t, logic.offeringCareTypeCli)
	assert.NotNil(t, logic.customerCli)
	assert.NotNil(t, logic.feedingCli)
	assert.NotNil(t, logic.medicationCli)
	assert.NotNil(t, logic.tx)
}

// Test NewByParams function
func TestNewByParams(t *testing.T) {
	mockAppointmentCli := &MockAppointmentClient{}
	mockFulfillmentCli := &MockFulfillmentClient{}
	mockServiceInstanceCli := &MockServiceInstanceClient{}
	mockOfferingServiceCli := &MockOfferingServiceClient{}
	mockOfferingCareTypeCli := &MockOfferingCareTypeClient{}
	mockCustomerCli := &MockCustomerClient{}
	mockFeedingCli := &MockFeedingClient{}
	mockMedicationCli := &MockMedicationClient{}
	mockTx := &MockTransactionManager{}

	logic := NewByParams(
		mockAppointmentCli,
		mockFulfillmentCli,
		mockServiceInstanceCli,
		mockOfferingServiceCli,
		mockOfferingCareTypeCli,
		mockCustomerCli,
		mockFeedingCli,
		mockMedicationCli,
		mockTx,
	)

	assert.NotNil(t, logic)
	assert.Equal(t, mockAppointmentCli, logic.appointmentCli)
	assert.Equal(t, mockFulfillmentCli, logic.fulfillmentCli)
	assert.Equal(t, mockServiceInstanceCli, logic.serviceInstanceCli)
	assert.Equal(t, mockOfferingServiceCli, logic.offeringServiceCli)
	assert.Equal(t, mockOfferingCareTypeCli, logic.offeringCareTypeCli)
	assert.Equal(t, mockCustomerCli, logic.customerCli)
	assert.Equal(t, mockFeedingCli, logic.feedingCli)
	assert.Equal(t, mockMedicationCli, logic.medicationCli)
	assert.Equal(t, mockTx, logic.tx)
}

// Test UpdateAppointment function
func TestLogic_UpdateAppointment_Success(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
		AppointmentOperation: &pb.UpdateAppointmentRequest_AppointmentOperation{
			StartTime: timestamppb.New(time.Now()),
			EndTime:   timestamppb.New(time.Now().Add(24 * time.Hour)),
			ColorCode: proto.String("#FF0000"),
			NewStatus: pb.AppointmentState_APPOINTMENT_STATE_CONFIRMED.Enum(),
		},
	}

	mockAppointmentCli := logic.appointmentCli.(*MockAppointmentClient)
	mockServiceInstanceCli := logic.serviceInstanceCli.(*MockServiceInstanceClient)
	mockTx := logic.tx.(*MockTransactionManager)

	appointmentEntity := &appointment.Appointment{
		ID:         1,
		BusinessID: 1,
		CompanyID:  1,
		CustomerID: 1,
		Status:     int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
		StartTime:  time.Now(),
		EndTime:    time.Now().Add(24 * time.Hour),
		ColorCode:  "#000000",
	}

	mockAppointmentCli.On("GetByID", ctx, 1).Return(appointmentEntity, nil)
	mockAppointmentCli.On("Update", ctx, mock.Anything).Return(nil)
	mockServiceInstanceCli.On("GetByAppointmentID", mock.Anything, 1).Return([]*serviceinstance.ServiceInstance{}, nil)
	mockTx.On("ExecuteInTransaction", ctx, mock.Anything).Run(func(args mock.Arguments) {
		// Simulate the transaction operations
		operations := args.Get(1).([]func(context.Context, *gorm.DB) error)
		for _, op := range operations {
			op(context.Background(), nil)
		}
	}).Return(nil)

	resp, err := logic.UpdateAppointment(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.Success)
	assert.Equal(t, "更新成功", resp.Message)

	mockAppointmentCli.AssertExpectations(t)
	mockServiceInstanceCli.AssertExpectations(t)
	mockTx.AssertExpectations(t)
}

func TestLogic_UpdateAppointment_ValidationError(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	tests := []struct {
		name string
		req  *pb.UpdateAppointmentRequest
	}{
		{
			name: "invalid appointment_id",
			req: &pb.UpdateAppointmentRequest{
				AppointmentId: 0,
			},
		},
		{
			name: "nil request",
			req:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := logic.UpdateAppointment(ctx, tt.req)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.Equal(t, codes.InvalidArgument, status.Code(err))
		})
	}
}

func TestLogic_UpdateAppointment_NotFound(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	req := &pb.UpdateAppointmentRequest{
		AppointmentId: 1,
	}

	mockAppointmentCli := logic.appointmentCli.(*MockAppointmentClient)

	mockAppointmentCli.On("GetByID", ctx, 1).Return((*appointment.Appointment)(nil), errors.New("not found"))

	resp, err := logic.UpdateAppointment(ctx, req)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Equal(t, codes.NotFound, status.Code(err))

	mockAppointmentCli.AssertExpectations(t)
}

// Test GetAppointmentByIDs function
func TestLogic_GetAppointmentByIDs_Success(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	req := &pb.GetAppointmentByIDsRequest{
		AppointmentIds: []int64{1, 2},
	}

	mockAppointmentCli := logic.appointmentCli.(*MockAppointmentClient)
	mockServiceInstanceCli := logic.serviceInstanceCli.(*MockServiceInstanceClient)

	appointments := []*appointment.Appointment{
		{
			ID:              1,
			BusinessID:      1,
			CompanyID:       1,
			CustomerID:      1,
			Status:          int(pb.AppointmentState_APPOINTMENT_STATE_CONFIRMED),
			ServiceItemType: 1,
			ColorCode:       "#FF0000",
			StartTime:       time.Now(),
			EndTime:         time.Now().Add(24 * time.Hour),
		},
	}

	serviceInstances := []*serviceinstance.ServiceInstance{
		{
			ID:               1,
			PetID:            1,
			ServiceFactoryID: 1,
			ParentID:         0,
			RootParentID:     0,
			StartDate:        serviceinstance.TimeToPgDate(time.Now()),
			EndDate:          serviceinstance.TimeToPgDate(time.Now().Add(24 * time.Hour)),
		},
	}

	mockAppointmentCli.On("GetByIDs", ctx, []int64{1, 2}).Return(appointments, nil)
	mockServiceInstanceCli.On("GetByAppointmentID", ctx, 1).Return(serviceInstances, nil)

	resp, err := logic.GetAppointmentByIDs(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Appointments, 1)

	mockAppointmentCli.AssertExpectations(t)
	mockServiceInstanceCli.AssertExpectations(t)
}

func TestLogic_GetAppointmentByIDs_EmptyRequest(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	req := &pb.GetAppointmentByIDsRequest{
		AppointmentIds: []int64{},
	}

	resp, err := logic.GetAppointmentByIDs(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Empty(t, resp.Appointments)
}

func TestLogic_GetAppointmentByIDs_ValidationError(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	tests := []struct {
		name string
		req  *pb.GetAppointmentByIDsRequest
	}{
		{
			name: "nil request",
			req:  nil,
		},
		{
			name: "invalid appointment_id",
			req: &pb.GetAppointmentByIDsRequest{
				AppointmentIds: []int64{0, -1},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := logic.GetAppointmentByIDs(ctx, tt.req)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.Equal(t, codes.InvalidArgument, status.Code(err))
		})
	}
}

// Test ListAppointment function
func TestLogic_ListAppointment_Success(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	req := &pb.ListAppointmentRequest{
		BusinessId: 1,
		CompanyId:  1,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	mockAppointmentCli := logic.appointmentCli.(*MockAppointmentClient)
	mockServiceInstanceCli := logic.serviceInstanceCli.(*MockServiceInstanceClient)

	appointments := []*appointment.Appointment{
		{
			ID:              1,
			BusinessID:      1,
			CompanyID:       1,
			CustomerID:      1,
			Status:          int(pb.AppointmentState_APPOINTMENT_STATE_CONFIRMED),
			ServiceItemType: 1,
			ColorCode:       "#FF0000",
			StartTime:       time.Now(),
			EndTime:         time.Now().Add(24 * time.Hour),
		},
	}

	serviceInstances := []*serviceinstance.ServiceInstance{
		{
			ID:               1,
			PetID:            1,
			ServiceFactoryID: 1,
			ParentID:         0,
			RootParentID:     0,
			StartDate:        serviceinstance.TimeToPgDate(time.Now()),
			EndDate:          serviceinstance.TimeToPgDate(time.Now().Add(24 * time.Hour)),
		},
	}

	mockAppointmentCli.On("Count", ctx, mock.Anything, mock.Anything).Return(int64(1), nil)
	mockAppointmentCli.On("List", ctx, mock.Anything, mock.Anything).Return(appointments, nil)
	mockServiceInstanceCli.On("GetByAppointmentID", ctx, 1).Return(serviceInstances, nil)

	resp, err := logic.ListAppointment(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Appointments, 1)
	assert.Equal(t, int32(1), resp.Total)
	assert.True(t, resp.IsEnd)

	mockAppointmentCli.AssertExpectations(t)
	mockServiceInstanceCli.AssertExpectations(t)
}

func TestLogic_ListAppointment_ValidationError(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	tests := []struct {
		name string
		req  *pb.ListAppointmentRequest
	}{
		{
			name: "nil request",
			req:  nil,
		},
		{
			name: "invalid company_id",
			req: &pb.ListAppointmentRequest{
				CompanyId:  0,
				BusinessId: 1,
			},
		},
		{
			name: "invalid business_id",
			req: &pb.ListAppointmentRequest{
				CompanyId:  1,
				BusinessId: 0,
			},
		},
		{
			name: "start_time after end_time",
			req: &pb.ListAppointmentRequest{
				CompanyId:  1,
				BusinessId: 1,
				StartTime:  timestamppb.New(time.Now()),
				EndTime:    timestamppb.New(time.Now().Add(-24 * time.Hour)),
			},
		},
		{
			name: "negative offset",
			req: &pb.ListAppointmentRequest{
				CompanyId:  1,
				BusinessId: 1,
				Pagination: &pb.PaginationRef{
					Offset: -1,
					Limit:  10,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := logic.ListAppointment(ctx, tt.req)
			assert.Error(t, err)
			assert.Nil(t, resp)
			assert.Equal(t, codes.InvalidArgument, status.Code(err))
		})
	}
}

// Test buildPaginationInfo function
func Test_buildPaginationInfo(t *testing.T) {
	tests := []struct {
		name     string
		req      *pb.ListAppointmentRequest
		expected *appointment.PaginationInfo
	}{
		{
			name: "with pagination",
			req: &pb.ListAppointmentRequest{
				Pagination: &pb.PaginationRef{
					Offset: 10,
					Limit:  20,
				},
			},
			expected: &appointment.PaginationInfo{
				Offset: 10,
				Limit:  20,
			},
		},
		{
			name: "without pagination",
			req:  &pb.ListAppointmentRequest{},
			expected: &appointment.PaginationInfo{
				Offset: 0,
				Limit:  200,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := buildPaginationInfo(tt.req)
			assert.Equal(t, tt.expected.Offset, result.Offset)
			assert.Equal(t, tt.expected.Limit, result.Limit)
		})
	}
}

// Test buildFilter function
func TestLogic_buildFilter(t *testing.T) {
	logic := createTestLogic()

	req := &pb.ListAppointmentRequest{
		Filter: &pb.AppointmentFilter{
			Statuses: []pb.AppointmentState{
				pb.AppointmentState_APPOINTMENT_STATE_CONFIRMED,
				pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED,
			},
		},
	}

	result, err := logic.buildFilter(req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Statuses, 2)
	assert.Contains(t, result.Statuses, int32(pb.AppointmentState_APPOINTMENT_STATE_CONFIRMED))
	assert.Contains(t, result.Statuses, int32(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED))
}

func TestLogic_buildFilter_NoFilter(t *testing.T) {
	logic := createTestLogic()

	req := &pb.ListAppointmentRequest{}

	result, err := logic.buildFilter(req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Empty(t, result.Statuses)
}

// Test verifyUpdateAppointmentRequest function
func Test_verifyUpdateAppointmentRequest(t *testing.T) {
	tests := []struct {
		name        string
		req         *pb.UpdateAppointmentRequest
		expectError bool
		errorCode   codes.Code
	}{
		{
			name: "valid request",
			req: &pb.UpdateAppointmentRequest{
				AppointmentId: 1,
			},
			expectError: false,
		},
		{
			name: "invalid appointment_id",
			req: &pb.UpdateAppointmentRequest{
				AppointmentId: 0,
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name:        "nil request",
			req:         nil,
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := verifyUpdateAppointmentRequest(tt.req)
			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.errorCode, status.Code(err))
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test verifyListAppointmentRequest function
func Test_verifyListAppointmentRequest(t *testing.T) {
	tests := []struct {
		name        string
		req         *pb.ListAppointmentRequest
		expectError bool
		errorCode   codes.Code
	}{
		{
			name: "valid request",
			req: &pb.ListAppointmentRequest{
				CompanyId:  1,
				BusinessId: 1,
			},
			expectError: false,
		},
		{
			name:        "nil request",
			req:         nil,
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "invalid company_id",
			req: &pb.ListAppointmentRequest{
				CompanyId:  0,
				BusinessId: 1,
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "invalid business_id",
			req: &pb.ListAppointmentRequest{
				CompanyId:  1,
				BusinessId: 0,
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "start_time after end_time",
			req: &pb.ListAppointmentRequest{
				CompanyId:  1,
				BusinessId: 1,
				StartTime:  timestamppb.New(time.Now()),
				EndTime:    timestamppb.New(time.Now().Add(-24 * time.Hour)),
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "negative offset",
			req: &pb.ListAppointmentRequest{
				CompanyId:  1,
				BusinessId: 1,
				Pagination: &pb.PaginationRef{
					Offset: -1,
					Limit:  10,
				},
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := verifyListAppointmentRequest(tt.req)
			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.errorCode, status.Code(err))
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test verifyGetAppointmentByIDsRequest function
func Test_verifyGetAppointmentByIDsRequest(t *testing.T) {
	tests := []struct {
		name        string
		req         *pb.GetAppointmentByIDsRequest
		expectError bool
		errorCode   codes.Code
	}{
		{
			name: "valid request",
			req: &pb.GetAppointmentByIDsRequest{
				AppointmentIds: []int64{1, 2, 3},
			},
			expectError: false,
		},
		{
			name:        "nil request",
			req:         nil,
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "invalid appointment_id",
			req: &pb.GetAppointmentByIDsRequest{
				AppointmentIds: []int64{0, -1, 2},
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := verifyGetAppointmentByIDsRequest(tt.req)
			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.errorCode, status.Code(err))
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test generateInstanceFromDateScheduleConfig function
func TestLogic_generateInstanceFromDateScheduleConfig(t *testing.T) {
	logic := createTestLogic()

	tests := []struct {
		name               string
		generator          *ServiceInstanceGenerator
		dateScheduleConfig *pb.DateScheduleConfig
		expectError        bool
	}{
		{
			name: "specific date type",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID: 1,
				PetID:         1,
				CareType:      offeringpb.CareCategory_BOARDING,
				ParentID:      0,
				RootParentID:  0,
				DateType:      int32(pb.DateType_DATE_TYPE_SPECIFIC_DATE),
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_SPECIFIC_DATE,
				SpecificDates: []*timestamppb.Timestamp{
					timestamppb.New(time.Now()),
					timestamppb.New(time.Now().Add(24 * time.Hour)),
				},
			},
			expectError: false,
		},
		{
			name: "regular schedule type",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour),
				DateType:             int32(pb.DateType_DATE_TYPE_DATE_EVERYDAY),
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_DATE_EVERYDAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := logic.generateInstanceFromDateScheduleConfig(tt.generator, tt.dateScheduleConfig)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.generator.AppointmentID, result.AppointmentID)
				assert.Equal(t, tt.generator.PetID, result.PetID)
				assert.Equal(t, int(tt.generator.CareType), result.CareType)
			}
		})
	}
}

// Test generateInstanceFromSpecificDates function
func TestLogic_generateInstanceFromSpecificDates(t *testing.T) {
	logic := createTestLogic()

	generator := &ServiceInstanceGenerator{
		Req: &pb.CreateAppointmentRequest{
			BusinessId: 1,
			CustomerId: 1,
			CompanyId:  1,
		},
		AppointmentID: 1,
		PetID:         1,
		CareType:      offeringpb.CareCategory_BOARDING,
		ParentID:      0,
		RootParentID:  0,
		DateType:      int32(pb.DateType_DATE_TYPE_SPECIFIC_DATE),
	}

	specificDates := []*timestamppb.Timestamp{
		timestamppb.New(time.Now()),
		timestamppb.New(time.Now().Add(24 * time.Hour)),
	}

	result, err := logic.generateInstanceFromSpecificDates(generator, specificDates)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, generator.AppointmentID, result.AppointmentID)
	assert.Equal(t, generator.PetID, result.PetID)
	assert.Equal(t, int(generator.CareType), result.CareType)
	assert.Equal(t, int(generator.DateType), result.DateType)
	assert.NotNil(t, result.SpecificDates)
}

// Test generateInstanceFromRegularSchedule function
func TestLogic_generateInstanceFromRegularSchedule(t *testing.T) {
	logic := createTestLogic()

	tests := []struct {
		name               string
		generator          *ServiceInstanceGenerator
		dateScheduleConfig *pb.DateScheduleConfig
		dateType           int32
		expectError        bool
	}{
		{
			name: "everyday schedule",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour),
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_DATE_EVERYDAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_DATE_EVERYDAY),
			expectError: false,
		},
		{
			name: "first day schedule",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour),
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_FIRST_DAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_FIRST_DAY),
			expectError: false,
		},
		{
			name: "last day schedule",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour),
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_LAST_DAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_LAST_DAY),
			expectError: false,
		},
		{
			name: "missing duration",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour),
				ServiceInstance:      &pb.CreateServiceInstanceDef{
					// Missing Duration
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_DATE_EVERYDAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_DATE_EVERYDAY),
			expectError: true,
		},
		{
			name: "everyday except checkout day - same day",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now(), // Same day
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY),
			expectError: false,
		},
		{
			name: "everyday except checkout day - different days",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour), // Different days
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY),
			expectError: false,
		},
		{
			name: "everyday except checkin day - same day",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now(), // Same day
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY),
			expectError: false,
		},
		{
			name: "everyday except checkin day - different days",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour), // Different days
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY,
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY),
			expectError: false,
		},
		{
			name: "default case",
			generator: &ServiceInstanceGenerator{
				Req: &pb.CreateAppointmentRequest{
					BusinessId: 1,
					CustomerId: 1,
					CompanyId:  1,
				},
				AppointmentID:        1,
				PetID:                1,
				CareType:             offeringpb.CareCategory_BOARDING,
				ParentID:             0,
				RootParentID:         0,
				MainServiceStartTime: time.Now(),
				MainServiceEndTime:   time.Now().Add(48 * time.Hour),
				ServiceInstance: &pb.CreateServiceInstanceDef{
					Duration: &durationpb.Duration{
						Seconds: 3600,
					},
				},
			},
			dateScheduleConfig: &pb.DateScheduleConfig{
				DateType: pb.DateType_DATE_TYPE_UNSPECIFIED, // Default case
				RegularTime: &timeofday.TimeOfDay{
					Hours:   9,
					Minutes: 0,
					Seconds: 0,
				},
			},
			dateType:    int32(pb.DateType_DATE_TYPE_UNSPECIFIED),
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := logic.generateInstanceFromRegularSchedule(tt.generator, tt.dateScheduleConfig, tt.dateType)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.generator.AppointmentID, result.AppointmentID)
				assert.Equal(t, tt.generator.PetID, result.PetID)
				assert.Equal(t, int(tt.generator.CareType), result.CareType)
				assert.Equal(t, int(tt.dateType), result.DateType)
			}
		})
	}
}

// Test processServiceOperations function
func TestLogic_processServiceOperations(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	tests := []struct {
		name              string
		serviceOperations []*pb.UpdateAppointmentRequest_ServiceOperation
		appointmentID     int
		req               *pb.UpdateAppointmentRequest
		appointmentEntity *appointment.Appointment
		expectError       bool
	}{
		{
			name:              "empty service operations",
			serviceOperations: []*pb.UpdateAppointmentRequest_ServiceOperation{},
			appointmentID:     1,
			req: &pb.UpdateAppointmentRequest{
				AppointmentId: 1,
			},
			appointmentEntity: &appointment.Appointment{
				ID: 1,
			},
			expectError: false,
		},
		{
			name: "create operation",
			serviceOperations: []*pb.UpdateAppointmentRequest_ServiceOperation{
				{
					OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
					Id:            proto.Int64(1),
				},
			},
			appointmentID: 1,
			req: &pb.UpdateAppointmentRequest{
				AppointmentId: 1,
			},
			appointmentEntity: &appointment.Appointment{
				ID: 1,
			},
			expectError: false, // Mock is set up correctly
		},
		{
			name: "update operation",
			serviceOperations: []*pb.UpdateAppointmentRequest_ServiceOperation{
				{
					OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
					ServiceInstanceId: proto.Int64(1),
				},
			},
			appointmentID: 1,
			req: &pb.UpdateAppointmentRequest{
				AppointmentId: 1,
			},
			appointmentEntity: &appointment.Appointment{
				ID: 1,
			},
			expectError: false, // Mock is set up correctly
		},
		{
			name: "delete operation",
			serviceOperations: []*pb.UpdateAppointmentRequest_ServiceOperation{
				{
					OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
					ServiceInstanceId: proto.Int64(1),
				},
			},
			appointmentID: 1,
			req: &pb.UpdateAppointmentRequest{
				AppointmentId: 1,
			},
			appointmentEntity: &appointment.Appointment{
				ID: 1,
			},
			expectError: false, // Mock is set up correctly
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock the required clients
			mockServiceInstanceCli := logic.serviceInstanceCli.(*MockServiceInstanceClient)
			mockFulfillmentCli := logic.fulfillmentCli.(*MockFulfillmentClient)

			// Setup mocks for different operations
			for _, op := range tt.serviceOperations {
				switch op.GetOperationMode() {
				case pb.OperationMode_OPERATION_MODE_CREATE:
					// Mock for create operation
					mockServiceInstanceCli.On("Create", ctx, mock.Anything).Return(1, nil)
					mockFulfillmentCli.On("BatchCreate", ctx, mock.Anything).Return(nil)
				case pb.OperationMode_OPERATION_MODE_UPDATE:
					// Mock for update operation
					mockServiceInstanceCli.On("GetByID", ctx, int(op.GetServiceInstanceId())).Return(&serviceinstance.ServiceInstance{ID: int(op.GetServiceInstanceId())}, nil)
					mockServiceInstanceCli.On("Update", ctx, mock.Anything).Return(nil)
				case pb.OperationMode_OPERATION_MODE_DELETE:
					// Mock for delete operation
					mockFulfillmentCli.On("DeleteByServiceInstanceID", ctx, op.GetServiceInstanceId()).Return(nil)
					mockServiceInstanceCli.On("Delete", ctx, int(op.GetServiceInstanceId())).Return(nil)
				}
			}

			err := logic.processServiceOperations(ctx, tt.serviceOperations, tt.appointmentID, tt.req, tt.appointmentEntity)

			if tt.expectError {
				// For now, we expect errors due to incomplete mocking
				// In a real test, we would set up proper mocks
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test verifyServiceOperation function
func Test_verifyServiceOperation(t *testing.T) {
	tests := []struct {
		name        string
		operation   *pb.UpdateAppointmentRequest_ServiceOperation
		expectError bool
		errorCode   codes.Code
	}{
		{
			name: "valid create operation",
			operation: &pb.UpdateAppointmentRequest_ServiceOperation{
				OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
				Id:            proto.Int64(1),
			},
			expectError: true, // Will fail because TimeConfig is required
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "valid update operation",
			operation: &pb.UpdateAppointmentRequest_ServiceOperation{
				OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
				ServiceInstanceId: proto.Int64(1),
			},
			expectError: false,
		},
		{
			name: "valid delete operation",
			operation: &pb.UpdateAppointmentRequest_ServiceOperation{
				OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
				ServiceInstanceId: proto.Int64(1),
			},
			expectError: false,
		},
		{
			name: "create operation without service_id",
			operation: &pb.UpdateAppointmentRequest_ServiceOperation{
				OperationMode: pb.OperationMode_OPERATION_MODE_CREATE,
				Id:            proto.Int64(0),
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "update operation without service_instance_id",
			operation: &pb.UpdateAppointmentRequest_ServiceOperation{
				OperationMode:     pb.OperationMode_OPERATION_MODE_UPDATE,
				ServiceInstanceId: proto.Int64(0),
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
		{
			name: "delete operation without service_instance_id",
			operation: &pb.UpdateAppointmentRequest_ServiceOperation{
				OperationMode:     pb.OperationMode_OPERATION_MODE_DELETE,
				ServiceInstanceId: proto.Int64(0),
			},
			expectError: true,
			errorCode:   codes.InvalidArgument,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := verifyServiceOperation(tt.operation)
			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.errorCode, status.Code(err))
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
