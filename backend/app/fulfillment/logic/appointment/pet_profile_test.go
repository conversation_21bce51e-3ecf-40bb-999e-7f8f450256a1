package appointment

import (
	"context"
	"errors"
	"testing"

	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	feedingRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/feeding"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/medication"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

// Test syncPetDetailDef function
func TestLogic_syncPetDetailDef(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	tests := []struct {
		name         string
		companyID    int
		petSchedules []*pb.CreatePetDetailDef
		expectError  bool
	}{
		{
			name:         "empty pet schedules",
			companyID:    1,
			petSchedules: []*pb.CreatePetDetailDef{},
			expectError:  false,
		},
		{
			name:         "nil pet schedules",
			companyID:    1,
			petSchedules: nil,
			expectError:  false,
		},
		{
			name:      "valid pet schedules with medications and feedings",
			companyID: 1,
			petSchedules: []*pb.CreatePetDetailDef{
				{
					PetId: 1,
					Services: []*pb.CreateServiceInstanceDef{
						{
							Id: 1,
							Medications: []*pb.AppointmentPetMedicationScheduleDef{
								{
									MedicationName:   "Test Medication",
									MedicationAmount: "10mg",
									MedicationUnit:   "mg",
									MedicationNote:   proto.String("Test note"),
								},
							},
							Feedings: []*pb.AppointmentPetFeedingScheduleDef{
								{
									FeedingType:        "Dry Food",
									FeedingAmount:      "100g",
									FeedingUnit:        "g",
									FeedingSource:      "Owner",
									FeedingInstruction: proto.String("Mix with water"),
									FeedingNote:        proto.String("Test feeding note"),
								},
							},
						},
					},
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerCli := logic.customerCli.(*MockCustomerClient)

			// Only mock if there are pet schedules with medications or feedings
			if tt.petSchedules != nil && len(tt.petSchedules) > 0 {
				hasMedications := false
				hasFeedings := false
				for _, pet := range tt.petSchedules {
					for _, service := range pet.GetServices() {
						if len(service.GetMedications()) > 0 {
							hasMedications = true
						}
						if len(service.GetFeedings()) > 0 {
							hasFeedings = true
						}
					}
				}

				if hasMedications {
					mockCustomerCli.On("ListPetMedicationSchedule", ctx, int64(tt.companyID), mock.Anything).Return(&businesscustomersvcpb.ListPetMedicationScheduleResponse{}, nil)
					mockCustomerCli.On("BatchCreateMedicationSchedule", ctx, int64(tt.companyID), mock.Anything).Return(&businesscustomersvcpb.BatchCreateMedicationScheduleResponse{}, nil)
				}
				if hasFeedings {
					mockCustomerCli.On("ListPetFeedingSchedule", ctx, int64(tt.companyID), mock.Anything).Return(&businesscustomersvcpb.ListPetFeedingScheduleResponse{}, nil)
					mockCustomerCli.On("BatchCreateFeedingSchedule", ctx, int64(tt.companyID), mock.Anything).Return(&businesscustomersvcpb.BatchCreateFeedingScheduleResponse{}, nil)
				}
			}

			err := logic.syncPetDetailDef(ctx, tt.companyID, tt.petSchedules)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mockCustomerCli.AssertExpectations(t)
		})
	}
}

// Test syncPetProfileMedication function
func TestLogic_syncPetProfileMedication(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	tests := []struct {
		name           string
		companyID      int64
		petMedications map[int64][]*pb.AppointmentPetMedicationScheduleDef
		mockResponse   *businesscustomersvcpb.ListPetMedicationScheduleResponse
		mockError      error
		expectError    bool
	}{
		{
			name:           "empty pet medications",
			companyID:      1,
			petMedications: map[int64][]*pb.AppointmentPetMedicationScheduleDef{},
			expectError:    false,
		},
		{
			name:           "nil pet medications",
			companyID:      1,
			petMedications: nil,
			expectError:    false,
		},
		{
			name:      "successful sync with no existing medications",
			companyID: 1,
			petMedications: map[int64][]*pb.AppointmentPetMedicationScheduleDef{
				1: {
					{
						MedicationName:   "Test Medication",
						MedicationAmount: "10mg",
						MedicationUnit:   "mg",
						MedicationNote:   proto.String("Test note"),
					},
				},
			},
			mockResponse: &businesscustomersvcpb.ListPetMedicationScheduleResponse{},
			expectError:  false,
		},
		{
			name:      "sync with existing medications",
			companyID: 1,
			petMedications: map[int64][]*pb.AppointmentPetMedicationScheduleDef{
				1: {
					{
						MedicationName:   "Test Medication",
						MedicationAmount: "10mg",
						MedicationUnit:   "mg",
						MedicationNote:   proto.String("Test note"),
					},
				},
			},
			mockResponse: &businesscustomersvcpb.ListPetMedicationScheduleResponse{},
			expectError:  false,
		},
		{
			name:      "error getting existing medications",
			companyID: 1,
			petMedications: map[int64][]*pb.AppointmentPetMedicationScheduleDef{
				1: {
					{
						MedicationName:   "Test Medication",
						MedicationAmount: "10mg",
						MedicationUnit:   "mg",
						MedicationNote:   proto.String("Test note"),
					},
				},
			},
			mockError:   errors.New("database error"),
			expectError: false, // Should continue and create all medications
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerCli := logic.customerCli.(*MockCustomerClient)

			if tt.petMedications != nil && len(tt.petMedications) > 0 {
				mockCustomerCli.On("ListPetMedicationSchedule", ctx, tt.companyID, mock.Anything).Return(tt.mockResponse, tt.mockError)
				if tt.mockError == nil && (tt.mockResponse == nil || len(tt.mockResponse.GetMedications()) == 0) {
					mockCustomerCli.On("BatchCreateMedicationSchedule", ctx, tt.companyID, mock.Anything).Return(&businesscustomersvcpb.BatchCreateMedicationScheduleResponse{}, nil)
				}
			}

			err := logic.syncPetProfileMedication(ctx, tt.companyID, tt.petMedications)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mockCustomerCli.AssertExpectations(t)
		})
	}
}

// Test syncPetProfileFeeding function
func TestLogic_syncPetProfileFeeding(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	tests := []struct {
		name         string
		companyID    int64
		petFeedings  map[int64][]*pb.AppointmentPetFeedingScheduleDef
		mockResponse *businesscustomersvcpb.ListPetFeedingScheduleResponse
		mockError    error
		expectError  bool
	}{
		{
			name:        "empty pet feedings",
			companyID:   1,
			petFeedings: map[int64][]*pb.AppointmentPetFeedingScheduleDef{},
			expectError: false,
		},
		{
			name:        "nil pet feedings",
			companyID:   1,
			petFeedings: nil,
			expectError: false,
		},
		{
			name:      "successful sync with no existing feedings",
			companyID: 1,
			petFeedings: map[int64][]*pb.AppointmentPetFeedingScheduleDef{
				1: {
					{
						FeedingType:        "Dry Food",
						FeedingAmount:      "100g",
						FeedingUnit:        "g",
						FeedingSource:      "Owner",
						FeedingInstruction: proto.String("Mix with water"),
						FeedingNote:        proto.String("Test feeding note"),
					},
				},
			},
			mockResponse: &businesscustomersvcpb.ListPetFeedingScheduleResponse{},
			expectError:  false,
		},
		{
			name:      "sync with existing feedings",
			companyID: 1,
			petFeedings: map[int64][]*pb.AppointmentPetFeedingScheduleDef{
				1: {
					{
						FeedingType:        "Dry Food",
						FeedingAmount:      "100g",
						FeedingUnit:        "g",
						FeedingSource:      "Owner",
						FeedingInstruction: proto.String("Mix with water"),
						FeedingNote:        proto.String("Test feeding note"),
					},
				},
			},
			mockResponse: &businesscustomersvcpb.ListPetFeedingScheduleResponse{},
			expectError:  false,
		},
		{
			name:      "error getting existing feedings",
			companyID: 1,
			petFeedings: map[int64][]*pb.AppointmentPetFeedingScheduleDef{
				1: {
					{
						FeedingType:        "Dry Food",
						FeedingAmount:      "100g",
						FeedingUnit:        "g",
						FeedingSource:      "Owner",
						FeedingInstruction: proto.String("Mix with water"),
						FeedingNote:        proto.String("Test feeding note"),
					},
				},
			},
			mockError:   errors.New("database error"),
			expectError: false, // Should continue and create all feedings
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerCli := logic.customerCli.(*MockCustomerClient)

			if tt.petFeedings != nil && len(tt.petFeedings) > 0 {
				mockCustomerCli.On("ListPetFeedingSchedule", ctx, tt.companyID, mock.Anything).Return(tt.mockResponse, tt.mockError)
				if tt.mockError == nil && (tt.mockResponse == nil || len(tt.mockResponse.GetFeedings()) == 0) {
					mockCustomerCli.On("BatchCreateFeedingSchedule", ctx, tt.companyID, mock.Anything).Return(&businesscustomersvcpb.BatchCreateFeedingScheduleResponse{}, nil)
				}
			}

			err := logic.syncPetProfileFeeding(ctx, tt.companyID, tt.petFeedings)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mockCustomerCli.AssertExpectations(t)
		})
	}
}

// Test convertToBusinessPetMedicationScheduleDef function
func TestLogic_convertToBusinessPetMedicationScheduleDef(t *testing.T) {
	logic := createTestLogic()

	petID := int64(1)
	medication := &pb.AppointmentPetMedicationScheduleDef{
		MedicationName:   "Test Medication",
		MedicationAmount: "10mg",
		MedicationUnit:   "mg",
		MedicationNote:   proto.String("Test note"),
		MedicationTimes: []*pb.BusinessPetScheduleTimeDef{
			{
				ScheduleTime: 900,
				ExtraJson:    map[string]string{},
			},
		},
	}

	result := logic.convertToBusinessPetMedicationScheduleDef(petID, medication)

	assert.NotNil(t, result)
	assert.Equal(t, petID, result.PetId)
	assert.Equal(t, medication.GetMedicationName(), result.MedicationName)
	assert.Equal(t, medication.GetMedicationAmount(), result.MedicationAmount)
	assert.Equal(t, medication.GetMedicationUnit(), result.MedicationUnit)
	assert.Equal(t, medication.GetMedicationNote(), result.MedicationNote)
	assert.Len(t, result.MedicationTimes, 1)
	assert.Equal(t, medication.GetMedicationTimes()[0].GetScheduleTime(), result.MedicationTimes[0].ScheduleTime)
	assert.Equal(t, medication.GetMedicationTimes()[0].GetExtraJson(), result.MedicationTimes[0].ExtraJson)
}

// Test convertToBusinessPetFeedingScheduleDef function
func TestLogic_convertToBusinessPetFeedingScheduleDef(t *testing.T) {
	logic := createTestLogic()

	petID := int64(1)
	feeding := &pb.AppointmentPetFeedingScheduleDef{
		FeedingType:        "Dry Food",
		FeedingAmount:      "100g",
		FeedingUnit:        "g",
		FeedingSource:      "Owner",
		FeedingInstruction: proto.String("Mix with water"),
		FeedingNote:        proto.String("Test feeding note"),
		FeedingTimes: []*pb.BusinessPetScheduleTimeDef{
			{
				ScheduleTime: 800,
				ExtraJson:    map[string]string{},
			},
		},
	}

	result := logic.convertToBusinessPetFeedingScheduleDef(petID, feeding)

	assert.NotNil(t, result)
	assert.Equal(t, petID, result.PetId)
	assert.Equal(t, feeding.GetFeedingType(), result.FeedingType)
	assert.Equal(t, feeding.GetFeedingAmount(), result.FeedingAmount)
	assert.Equal(t, feeding.GetFeedingUnit(), result.FeedingUnit)
	assert.Equal(t, feeding.GetFeedingSource(), result.FeedingSource)
	assert.Equal(t, feeding.GetFeedingInstruction(), *result.FeedingInstruction)
	assert.Equal(t, feeding.GetFeedingNote(), *result.FeedingNote)
	assert.Len(t, result.FeedingTimes, 1)
	assert.Equal(t, feeding.GetFeedingTimes()[0].GetScheduleTime(), result.FeedingTimes[0].ScheduleTime)
	assert.Equal(t, feeding.GetFeedingTimes()[0].GetExtraJson(), result.FeedingTimes[0].ExtraJson)
}

// Test convertToBusinessPetScheduleTimeDefs function
func TestLogic_convertToBusinessPetScheduleTimeDefs(t *testing.T) {
	logic := createTestLogic()

	times := []*pb.BusinessPetScheduleTimeDef{
		{
			ScheduleTime: 800,
			ExtraJson:    map[string]string{},
		},
		{
			ScheduleTime: 1800,
			ExtraJson:    map[string]string{"note": "evening feeding"},
		},
	}

	result := logic.convertToBusinessPetScheduleTimeDefs(times)

	assert.Len(t, result, 2)
	assert.Equal(t, times[0].GetScheduleTime(), result[0].ScheduleTime)
	assert.Equal(t, times[0].GetExtraJson(), result[0].ExtraJson)
	assert.Equal(t, times[1].GetScheduleTime(), result[1].ScheduleTime)
	assert.Equal(t, times[1].GetExtraJson(), result[1].ExtraJson)
}

// Test savePetScheduleData function
func TestLogic_savePetScheduleDataInTransaction(t *testing.T) {
	logic := createTestLogic()
	ctx := context.Background()

	// Create a mock transaction
	mockTx := &gorm.DB{}

	tests := []struct {
		name             string
		serviceInstances []*ServiceInstanceData
		expectError      bool
	}{
		{
			name:             "empty service instances",
			serviceInstances: []*ServiceInstanceData{},
			expectError:      false,
		},
		{
			name:             "nil service instances",
			serviceInstances: nil,
			expectError:      false,
		},
		{
			name: "service instances with medications and feedings",
			serviceInstances: []*ServiceInstanceData{
				{
					ServiceInstanceID: 1,
					ServiceInstance: &serviceinstance.ServiceInstance{
						ID:            1,
						CompanyID:     1,
						AppointmentID: 1,
						PetID:         1,
					},
					CreateServiceInstanceDef: &pb.CreateServiceInstanceDef{
						Id: 1,
						Medications: []*pb.AppointmentPetMedicationScheduleDef{
							{
								MedicationName:   "Test Medication",
								MedicationAmount: "10mg",
								MedicationUnit:   "mg",
								MedicationNote:   proto.String("Test note"),
							},
						},
						Feedings: []*pb.AppointmentPetFeedingScheduleDef{
							{
								FeedingType:        "Dry Food",
								FeedingAmount:      "100g",
								FeedingUnit:        "g",
								FeedingSource:      "Owner",
								FeedingInstruction: proto.String("Mix with water"),
								FeedingNote:        proto.String("Test feeding note"),
							},
						},
					},
				},
			},
			expectError: false,
		},
		{
			name: "service instance with nil CreateServiceInstanceDef",
			serviceInstances: []*ServiceInstanceData{
				{
					ServiceInstanceID: 1,
					ServiceInstance: &serviceinstance.ServiceInstance{
						ID:            1,
						CompanyID:     1,
						AppointmentID: 1,
						PetID:         1,
					},
					CreateServiceInstanceDef: nil,
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockMedicationCli := logic.medicationCli.(*MockMedicationClient)
			mockFeedingCli := logic.feedingCli.(*MockFeedingClient)

			// Only mock if there are service instances with medications or feedings
			if tt.serviceInstances != nil && len(tt.serviceInstances) > 0 {
				hasMedications := false
				hasFeedings := false
				for _, si := range tt.serviceInstances {
					if si.CreateServiceInstanceDef != nil {
						if len(si.CreateServiceInstanceDef.GetMedications()) > 0 {
							hasMedications = true
						}
						if len(si.CreateServiceInstanceDef.GetFeedings()) > 0 {
							hasFeedings = true
						}
					}
				}

				if hasMedications {
					mockMedicationCli.On("BatchCreateWithTx", ctx, mockTx, mock.Anything).Return(nil)
				}
				if hasFeedings {
					mockFeedingCli.On("BatchCreateWithTx", ctx, mockTx, mock.Anything).Return(nil)
				}
			}

			err := logic.savePetScheduleDataInTransaction(ctx, mockTx, tt.serviceInstances)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mockMedicationCli.AssertExpectations(t)
			mockFeedingCli.AssertExpectations(t)
		})
	}
}

// Test convertMedicationDefToEntity function
func TestLogic_convertMedicationDefToEntity(t *testing.T) {
	logic := createTestLogic()

	tests := []struct {
		name              string
		medDef            *pb.AppointmentPetMedicationScheduleDef
		serviceInstanceID int64
		serviceInstance   *serviceinstance.ServiceInstance
		expectedResult    *medication.AppointmentPetMedication
	}{
		{
			name:              "nil medication definition",
			medDef:            nil,
			serviceInstanceID: 1,
			serviceInstance: &serviceinstance.ServiceInstance{
				ID:            1,
				CompanyID:     1,
				AppointmentID: 1,
				PetID:         1,
			},
			expectedResult: nil,
		},
		{
			name: "nil service instance",
			medDef: &pb.AppointmentPetMedicationScheduleDef{
				MedicationName:   "Test Medication",
				MedicationAmount: "10mg",
				MedicationUnit:   "mg",
				MedicationNote:   proto.String("Test note"),
			},
			serviceInstanceID: 1,
			serviceInstance:   nil,
			expectedResult:    nil,
		},
		{
			name: "valid medication definition without selected date",
			medDef: &pb.AppointmentPetMedicationScheduleDef{
				MedicationName:   "Test Medication",
				MedicationAmount: "10mg",
				MedicationUnit:   "mg",
				MedicationNote:   proto.String("Test note"),
			},
			serviceInstanceID: 1,
			serviceInstance: &serviceinstance.ServiceInstance{
				ID:            1,
				CompanyID:     1,
				AppointmentID: 1,
				PetID:         1,
			},
			expectedResult: &medication.AppointmentPetMedication{
				CompanyID:        1,
				AppointmentID:    1,
				PetDetailID:      1,
				PetID:            1,
				MedicationAmount: "10mg",
				MedicationUnit:   "mg",
				MedicationName:   "Test Medication",
				MedicationNote:   "Test note",
				DateType:         int32(pb.FeedingMedicationScheduleDateType_EVERYDAY_INCLUDE_CHECKOUT_DATE),
				SpecificDates:    "[]",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.convertMedicationDefToEntity(tt.medDef, tt.serviceInstanceID, tt.serviceInstance)

			if tt.expectedResult == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.CompanyID, result.CompanyID)
				assert.Equal(t, tt.expectedResult.AppointmentID, result.AppointmentID)
				assert.Equal(t, tt.expectedResult.PetDetailID, result.PetDetailID)
				assert.Equal(t, tt.expectedResult.PetID, result.PetID)
				assert.Equal(t, tt.expectedResult.MedicationAmount, result.MedicationAmount)
				assert.Equal(t, tt.expectedResult.MedicationUnit, result.MedicationUnit)
				assert.Equal(t, tt.expectedResult.MedicationName, result.MedicationName)
				assert.Equal(t, tt.expectedResult.MedicationNote, result.MedicationNote)
				assert.Equal(t, tt.expectedResult.DateType, result.DateType)
				assert.Equal(t, tt.expectedResult.SpecificDates, result.SpecificDates)
			}
		})
	}
}

// Test convertFeedingDefToEntity function
func TestLogic_convertFeedingDefToEntity(t *testing.T) {
	logic := createTestLogic()

	tests := []struct {
		name              string
		feedingDef        *pb.AppointmentPetFeedingScheduleDef
		serviceInstanceID int64
		serviceInstance   *serviceinstance.ServiceInstance
		expectedResult    *feedingRepo.AppointmentPetFeeding
	}{
		{
			name:              "nil feeding definition",
			feedingDef:        nil,
			serviceInstanceID: 1,
			serviceInstance: &serviceinstance.ServiceInstance{
				ID:            1,
				CompanyID:     1,
				AppointmentID: 1,
				PetID:         1,
			},
			expectedResult: nil,
		},
		{
			name: "nil service instance",
			feedingDef: &pb.AppointmentPetFeedingScheduleDef{
				FeedingType:        "Dry Food",
				FeedingAmount:      "100g",
				FeedingUnit:        "g",
				FeedingSource:      "Owner",
				FeedingInstruction: proto.String("Mix with water"),
				FeedingNote:        proto.String("Test feeding note"),
			},
			serviceInstanceID: 1,
			serviceInstance:   nil,
			expectedResult:    nil,
		},
		{
			name: "valid feeding definition",
			feedingDef: &pb.AppointmentPetFeedingScheduleDef{
				FeedingType:        "Dry Food",
				FeedingAmount:      "100g",
				FeedingUnit:        "g",
				FeedingSource:      "Owner",
				FeedingInstruction: proto.String("Mix with water"),
				FeedingNote:        proto.String("Test feeding note"),
			},
			serviceInstanceID: 1,
			serviceInstance: &serviceinstance.ServiceInstance{
				ID:            1,
				CompanyID:     1,
				AppointmentID: 1,
				PetID:         1,
			},
			expectedResult: &feedingRepo.AppointmentPetFeeding{
				CompanyID:          1,
				AppointmentID:      1,
				PetDetailID:        1,
				PetID:              1,
				FeedingType:        "Dry Food",
				FeedingAmount:      "100g",
				FeedingUnit:        "g",
				FeedingSource:      "Owner",
				FeedingInstruction: "Mix with water",
				FeedingNote:        "Test feeding note",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.convertFeedingDefToEntity(tt.feedingDef, tt.serviceInstanceID, tt.serviceInstance)

			if tt.expectedResult == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.CompanyID, result.CompanyID)
				assert.Equal(t, tt.expectedResult.AppointmentID, result.AppointmentID)
				assert.Equal(t, tt.expectedResult.PetDetailID, result.PetDetailID)
				assert.Equal(t, tt.expectedResult.PetID, result.PetID)
				assert.Equal(t, tt.expectedResult.FeedingType, result.FeedingType)
				assert.Equal(t, tt.expectedResult.FeedingAmount, result.FeedingAmount)
				assert.Equal(t, tt.expectedResult.FeedingUnit, result.FeedingUnit)
				assert.Equal(t, tt.expectedResult.FeedingSource, result.FeedingSource)
				assert.Equal(t, tt.expectedResult.FeedingInstruction, result.FeedingInstruction)
				assert.Equal(t, tt.expectedResult.FeedingNote, result.FeedingNote)
			}
		})
	}
}
