package appointment

import (
	"context"
	"testing"

	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"

	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	customerMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/customer/mock"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

func TestLogic_syncPetDetailDef(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	customerCli := customerMock.NewMockReadWriter(ctrl)
	logic := &Logic{customerCli: customerCli}

	ctx := context.Background()
	companyID := 123

	// 创建测试数据
	petSchedules := []*pb.CreatePetDetailDef{
		{
			PetId: 1,
			Services: []*pb.CreateServiceInstanceDef{
				{
					Id: 100,
					Medications: []*pb.AppointmentPetMedicationScheduleDef{
						{
							MedicationAmount: "1",
							MedicationUnit:   "tablet",
							MedicationName:   "Vitamin C",
							MedicationTimes: []*pb.BusinessPetScheduleTimeDef{
								{
									ScheduleTime: 540, // 9:00 AM
									ExtraJson:    map[string]string{"label": "morning"},
								},
							},
						},
					},
					Feedings: []*pb.AppointmentPetFeedingScheduleDef{
						{
							FeedingAmount: "1/2",
							FeedingUnit:   "cup",
							FeedingType:   "dry food",
							FeedingSource: "pet store",
							FeedingTimes: []*pb.BusinessPetScheduleTimeDef{
								{
									ScheduleTime: 480, // 8:00 AM
									ExtraJson:    map[string]string{"label": "breakfast"},
								},
							},
						},
					},
				},
			},
		},
		{
			PetId: 2,
			Services: []*pb.CreateServiceInstanceDef{
				{
					Id: 200,
					Medications: []*pb.AppointmentPetMedicationScheduleDef{
						{
							MedicationAmount: "2",
							MedicationUnit:   "ml",
							MedicationName:   "Fish Oil",
							MedicationTimes: []*pb.BusinessPetScheduleTimeDef{
								{
									ScheduleTime: 1080, // 6:00 PM
									ExtraJson:    map[string]string{"label": "evening"},
								},
							},
						},
					},
				},
			},
		},
	}

	// 设置mock期望 - 先检查是否存在
	customerCli.EXPECT().
		ListPetMedicationSchedule(ctx, int64(companyID), gomock.Any()).
		Return(&businesscustomersvcpb.ListPetMedicationScheduleResponse{}, nil)

	customerCli.EXPECT().
		ListPetFeedingSchedule(ctx, int64(companyID), gomock.Any()).
		Return(&businesscustomersvcpb.ListPetFeedingScheduleResponse{}, nil)

	// 然后创建新的记录
	customerCli.EXPECT().
		BatchCreateMedicationSchedule(ctx, int64(companyID), gomock.Any()).
		Return(&businesscustomersvcpb.BatchCreateMedicationScheduleResponse{}, nil)

	customerCli.EXPECT().
		BatchCreateFeedingSchedule(ctx, int64(companyID), gomock.Any()).
		Return(&businesscustomersvcpb.BatchCreateFeedingScheduleResponse{}, nil)

	// 执行测试
	err := logic.syncPetDetailDef(ctx, companyID, petSchedules)

	// 验证结果
	assert.NoError(t, err)
}

func TestLogic_syncPetDetailDef_Empty(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	customerCli := customerMock.NewMockReadWriter(ctrl)
	logic := &Logic{customerCli: customerCli}

	ctx := context.Background()
	companyID := 123

	// 测试空数据
	err := logic.syncPetDetailDef(ctx, companyID, []*pb.CreatePetDetailDef{})
	assert.NoError(t, err)

	// 测试nil数据
	err = logic.syncPetDetailDef(ctx, companyID, nil)
	assert.NoError(t, err)
}

func TestLogic_syncPetDetailDef_NoMedicationsOrFeedings(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	customerCli := customerMock.NewMockReadWriter(ctrl)
	logic := &Logic{customerCli: customerCli}

	ctx := context.Background()
	companyID := 123

	// 创建没有用药和喂养计划的测试数据
	petSchedules := []*pb.CreatePetDetailDef{
		{
			PetId: 1,
			Services: []*pb.CreateServiceInstanceDef{
				{
					Id: 100,
					// 没有Medications和Feedings
				},
			},
		},
	}

	// 执行测试 - 不应该调用任何mock方法
	err := logic.syncPetDetailDef(ctx, companyID, petSchedules)
	assert.NoError(t, err)
}

func TestLogic_savePetScheduleData(t *testing.T) {
	logic := &Logic{}

	ctx := context.Background()

	// 测试空数据
	err := logic.savePetScheduleData(ctx, []*ServiceInstanceData{})
	assert.NoError(t, err)

	// 测试nil数据
	err = logic.savePetScheduleData(ctx, nil)
	assert.NoError(t, err)

	// 测试没有CreateServiceInstanceDef的情况
	serviceInstanceData := []*ServiceInstanceData{
		{
			ServiceInstanceID:        1,
			ServiceInstance:          &serviceinstance.ServiceInstance{ID: 1},
			CreateServiceInstanceDef: nil,
		},
	}
	err = logic.savePetScheduleData(ctx, serviceInstanceData)
	assert.NoError(t, err)
}

func TestLogic_savePetScheduleData_Empty(t *testing.T) {
	// 跳过需要mock的测试，因为mock包不存在
	t.Skip("skip savePetScheduleData_Empty test as mock packages are not available")
}

func TestLogic_savePetScheduleData_NoMedicationsOrFeedings(t *testing.T) {
	// 跳过需要mock的测试，因为mock包不存在
	t.Skip("skip savePetScheduleData_NoMedicationsOrFeedings test as mock packages are not available")
}

func TestLogic_savePetScheduleData_MedicationError(t *testing.T) {
	// 跳过需要mock的测试，因为mock包不存在
	t.Skip("skip savePetScheduleData_MedicationError test as mock packages are not available")
}

func TestLogic_savePetScheduleData_FeedingError(t *testing.T) {
	// 跳过需要mock的测试，因为mock包不存在
	t.Skip("skip savePetScheduleData_FeedingError test as mock packages are not available")
}

func TestLogic_convertMedicationDefToEntity(t *testing.T) {
	logic := &Logic{}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID:            1,
		CompanyID:     123,
		AppointmentID: 456,
		PetID:         789,
	}

	// 测试基本转换
	medDef := &pb.AppointmentPetMedicationScheduleDef{
		MedicationAmount: "1",
		MedicationUnit:   "tablet",
		MedicationName:   "Vitamin C",
		MedicationNote:   proto.String("Take with food"),
	}

	result := logic.convertMedicationDefToEntity(medDef, 1, serviceInstance)

	assert.NotNil(t, result)
	assert.Equal(t, int64(123), result.CompanyID)
	assert.Equal(t, int64(456), result.AppointmentID)
	assert.Equal(t, int64(1), result.PetDetailID)
	assert.Equal(t, int64(789), result.PetID)
	assert.Equal(t, "1", result.MedicationAmount)
	assert.Equal(t, "tablet", result.MedicationUnit)
	assert.Equal(t, "Vitamin C", result.MedicationName)
	assert.Equal(t, "Take with food", result.MedicationNote)
	assert.Equal(t, int32(fulfillmentpb.FeedingMedicationScheduleDateType_EVERYDAY_INCLUDE_CHECKOUT_DATE), result.DateType)
	assert.Equal(t, "[]", result.SpecificDates)
}

func TestLogic_convertMedicationDefToEntity_WithSelectedDate(t *testing.T) {
	logic := &Logic{}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID:            1,
		CompanyID:     123,
		AppointmentID: 456,
		PetID:         789,
	}

	// 测试带特定日期的转换
	medDef := &pb.AppointmentPetMedicationScheduleDef{
		MedicationAmount: "2",
		MedicationUnit:   "ml",
		MedicationName:   "Fish Oil",
		MedicationNote:   proto.String("Evening dose"),
		SelectedDate: &pb.AppointmentPetMedicationScheduleDef_SelectedDateDef{
			DateType:      fulfillmentpb.FeedingMedicationScheduleDateType_SPECIFIC_DATE,
			SpecificDates: []string{"2024-01-01", "2024-01-02", "2024-01-03"},
		},
	}

	result := logic.convertMedicationDefToEntity(medDef, 1, serviceInstance)

	assert.NotNil(t, result)
	assert.Equal(t, int32(fulfillmentpb.FeedingMedicationScheduleDateType_SPECIFIC_DATE), result.DateType)
	assert.Equal(t, `["2024-01-01","2024-01-02","2024-01-03"]`, result.SpecificDates)
}

func TestLogic_convertMedicationDefToEntity_NilInput(t *testing.T) {
	logic := &Logic{}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID: 1,
	}

	// 测试nil输入
	result := logic.convertMedicationDefToEntity(nil, 1, serviceInstance)
	assert.Nil(t, result)

	result = logic.convertMedicationDefToEntity(&pb.AppointmentPetMedicationScheduleDef{}, 1, nil)
	assert.Nil(t, result)
}

func TestLogic_convertFeedingDefToEntity(t *testing.T) {
	logic := &Logic{}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID:            1,
		CompanyID:     123,
		AppointmentID: 456,
		PetID:         789,
	}

	// 测试基本转换
	feedingDef := &pb.AppointmentPetFeedingScheduleDef{
		FeedingAmount:      "1/2",
		FeedingUnit:        "cup",
		FeedingType:        "dry food",
		FeedingSource:      "pet store",
		FeedingInstruction: proto.String("Mix with water"),
		FeedingNote:        proto.String("Morning feeding"),
	}

	result := logic.convertFeedingDefToEntity(feedingDef, 1, serviceInstance)

	assert.NotNil(t, result)
	assert.Equal(t, int64(123), result.CompanyID)
	assert.Equal(t, int64(456), result.AppointmentID)
	assert.Equal(t, int64(1), result.PetDetailID)
	assert.Equal(t, int64(789), result.PetID)
	assert.Equal(t, "1/2", result.FeedingAmount)
	assert.Equal(t, "cup", result.FeedingUnit)
	assert.Equal(t, "dry food", result.FeedingType)
	assert.Equal(t, "pet store", result.FeedingSource)
	assert.Equal(t, "Mix with water", result.FeedingInstruction)
	assert.Equal(t, "Morning feeding", result.FeedingNote)
}

func TestLogic_convertFeedingDefToEntity_NilInput(t *testing.T) {
	logic := &Logic{}

	serviceInstance := &serviceinstance.ServiceInstance{
		ID: 1,
	}

	// 测试nil输入
	result := logic.convertFeedingDefToEntity(nil, 1, serviceInstance)
	assert.Nil(t, result)

	result = logic.convertFeedingDefToEntity(&pb.AppointmentPetFeedingScheduleDef{}, 1, nil)
	assert.Nil(t, result)
}

func TestLogic_syncPetProfileMedication_ErrorHandling(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	customerCli := customerMock.NewMockReadWriter(ctrl)
	logic := &Logic{customerCli: customerCli}

	ctx := context.Background()
	companyID := int64(123)
	petMedications := map[int64][]*pb.AppointmentPetMedicationScheduleDef{
		1: {
			{
				MedicationAmount: "1",
				MedicationUnit:   "tablet",
				MedicationName:   "Vitamin C",
			},
		},
	}

	// 测试ListPetMedicationSchedule返回错误的情况
	customerCli.EXPECT().
		ListPetMedicationSchedule(ctx, companyID, []int64{1}).
		Return(nil, assert.AnError)

	// 测试BatchCreateMedicationSchedule返回错误的情况
	customerCli.EXPECT().
		BatchCreateMedicationSchedule(ctx, companyID, gomock.Any()).
		Return(nil, assert.AnError)

	err := logic.syncPetProfileMedication(ctx, companyID, petMedications)
	assert.Error(t, err)
}

func TestLogic_syncPetProfileMedication_ExistingData(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	customerCli := customerMock.NewMockReadWriter(ctrl)
	logic := &Logic{customerCli: customerCli}

	ctx := context.Background()
	companyID := int64(123)
	petMedications := map[int64][]*pb.AppointmentPetMedicationScheduleDef{
		1: {
			{
				MedicationAmount: "1",
				MedicationUnit:   "tablet",
				MedicationName:   "Vitamin C",
			},
		},
	}

	// 模拟已存在的数据 - 返回空响应表示没有现有数据
	customerCli.EXPECT().
		ListPetMedicationSchedule(ctx, companyID, []int64{1}).
		Return(&businesscustomersvcpb.ListPetMedicationScheduleResponse{}, nil)

	// 测试创建新数据
	customerCli.EXPECT().
		BatchCreateMedicationSchedule(ctx, companyID, gomock.Any()).
		Return(&businesscustomersvcpb.BatchCreateMedicationScheduleResponse{}, nil)

	err := logic.syncPetProfileMedication(ctx, companyID, petMedications)
	assert.NoError(t, err)
}

func TestLogic_syncPetProfileFeeding_ErrorHandling(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	customerCli := customerMock.NewMockReadWriter(ctrl)
	logic := &Logic{customerCli: customerCli}

	ctx := context.Background()
	companyID := int64(123)
	petFeedings := map[int64][]*pb.AppointmentPetFeedingScheduleDef{
		1: {
			{
				FeedingAmount: "1/2",
				FeedingUnit:   "cup",
				FeedingType:   "dry food",
			},
		},
	}

	// 测试ListPetFeedingSchedule返回错误的情况
	customerCli.EXPECT().
		ListPetFeedingSchedule(ctx, companyID, []int64{1}).
		Return(nil, assert.AnError)

	// 测试BatchCreateFeedingSchedule返回错误的情况
	customerCli.EXPECT().
		BatchCreateFeedingSchedule(ctx, companyID, gomock.Any()).
		Return(nil, assert.AnError)

	err := logic.syncPetProfileFeeding(ctx, companyID, petFeedings)
	assert.Error(t, err)
}

func TestLogic_syncPetProfileFeeding_ExistingData(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	customerCli := customerMock.NewMockReadWriter(ctrl)
	logic := &Logic{customerCli: customerCli}

	ctx := context.Background()
	companyID := int64(123)
	petFeedings := map[int64][]*pb.AppointmentPetFeedingScheduleDef{
		1: {
			{
				FeedingAmount: "1/2",
				FeedingUnit:   "cup",
				FeedingType:   "dry food",
			},
		},
	}

	// 模拟已存在的数据 - 返回空响应表示没有现有数据
	customerCli.EXPECT().
		ListPetFeedingSchedule(ctx, companyID, []int64{1}).
		Return(&businesscustomersvcpb.ListPetFeedingScheduleResponse{}, nil)

	// 测试创建新数据
	customerCli.EXPECT().
		BatchCreateFeedingSchedule(ctx, companyID, gomock.Any()).
		Return(&businesscustomersvcpb.BatchCreateFeedingScheduleResponse{}, nil)

	err := logic.syncPetProfileFeeding(ctx, companyID, petFeedings)
	assert.NoError(t, err)
}
