package appointment

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	fulfillmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func (l *Logic) CreateAppointment(ctx context.Context, req *pb.CreateAppointmentRequest) (
	*pb.CreateAppointmentResponse, error) {
	// 校验请求
	if err := verifyCreateAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 获取所有serviceTemplateID并调用第三方服务获取careType信息
	serviceTemplateIDs := l.getAllServiceIDs(req.GetPets())
	serviceInfoMap, err := l.getCareTypesByServiceIDs(ctx, serviceTemplateIDs)
	if err != nil {
		return nil, err
	}
	// 计算ServiceItemType（所有careType去重后相加的值）
	serviceItemType := l.calculateServiceItemType(serviceInfoMap)

	// 从所有service中计算appointment的时间范围
	appointmentStartTime, appointmentEndTime, err := l.calculateAppointmentTimeRange(req.GetPets())
	if err != nil {
		return nil, err
	}

	// 组包
	appointmentEntity := &appointment.Appointment{
		BusinessID:           int(req.GetBusinessId()),
		CustomerID:           int(req.GetCustomerId()),
		CompanyID:            int(req.GetCompanyId()),
		Status:               int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
		AllPetsStartSameTime: req.GetAllPetsStartSameTime(),
		ServiceItemType:      int(serviceItemType),
		StartTime:            appointmentStartTime,
		EndTime:              appointmentEndTime,
		ColorCode:            req.GetColorCode(),
	}
	appointmentID, err := l.doCreateAppointment(ctx, req,
		appointmentEntity, serviceInfoMap)
	if err != nil {
		return nil, err
	}
	// 同步宠物档案的用药和喂养计划
	if err := l.syncPetDetailDef(ctx, int(req.GetCompanyId()), req.GetPets()); err != nil {
		log.Errorf("Failed to sync pet detail def: %v", err)
		// 注意：这里不返回错误，因为同步失败不应该影响预约创建.
	}

	return &pb.CreateAppointmentResponse{
		Id: int64(appointmentID),
	}, nil
}

func (l *Logic) doCreateAppointment(ctx context.Context, req *pb.CreateAppointmentRequest,
	appointmentEntity *appointment.Appointment,
	serviceInfoMap map[int64]*ServiceInfo) (int, error) {
	var appointmentID int
	var serviceInstances []*ServiceInstanceData

	// 使用事务处理
	err := l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, tx *gorm.DB) error {
			instances, err := l.createAppointmentInTransaction(opCtx, tx, req,
				appointmentEntity, serviceInfoMap, &appointmentID)
			if err != nil {
				log.Errorf("Failed to create appointment: %v", err)
				return err
			}
			serviceInstances = instances

			// 在事务中保存宠物的喂养和用药计划
			if err := l.savePetScheduleDataInTransaction(opCtx, tx, serviceInstances); err != nil {
				log.Errorf("Failed to save pet schedule data: %v", err)
				return err
			}

			return nil
		},
	})
	if err != nil {
		return 0, err
	}

	return appointmentID, nil
}

// createAppointmentInTransaction 在事务中创建预约
func (l *Logic) createAppointmentInTransaction(ctx context.Context, tx *gorm.DB,
	req *pb.CreateAppointmentRequest,
	appointmentEntity *appointment.Appointment,
	serviceInfoMap map[int64]*ServiceInfo,
	appointmentID *int) ([]*ServiceInstanceData, error) {
	// 创建 appointment - 使用事务连接
	if err := tx.WithContext(ctx).Create(appointmentEntity).Error; err != nil {
		return nil, err
	}
	*appointmentID = appointmentEntity.ID

	// 检查 appointmentID 是否有效
	if *appointmentID <= 0 {
		return nil, fmt.Errorf("invalid appointment ID: %d", *appointmentID)
	}

	serviceInstances, err := l.createServiceInstancesInTransaction(ctx, tx, req, *appointmentID, serviceInfoMap)
	if err != nil {
		return nil, err
	}

	return serviceInstances, nil
}

// createServiceInstances 创建服务实例并收集喂养和用药数据
func (l *Logic) createServiceInstances(ctx context.Context, req *pb.CreateAppointmentRequest,
	appointmentID int, serviceInfoMap map[int64]*ServiceInfo) ([]*ServiceInstanceData, error) {
	var allServiceInstances []*ServiceInstanceData

	for _, petDetail := range req.GetPets() {
		for _, serviceInstance := range petDetail.GetServices() {
			// 获取主服务时间范围（只有使用DateScheduleConfig的服务才需要）
			var mainServiceStartTime, mainServiceEndTime time.Time
			// 递归创建服务实例，传入0作为parentID（顶层服务实例），level=1
			instances, err := l.createServiceInstanceRecursively(ctx, req, appointmentID,
				int(petDetail.GetPetId()), serviceInstance, serviceInfoMap, 0, 0, 1,
				mainServiceStartTime, mainServiceEndTime)
			if err != nil {
				return nil, err
			}
			allServiceInstances = append(allServiceInstances, instances...)
		}
	}

	return allServiceInstances, nil
}

// createServiceInstancesInTransaction 在事务中创建服务实例并收集喂养和用药数据
func (l *Logic) createServiceInstancesInTransaction(ctx context.Context, tx *gorm.DB, req *pb.CreateAppointmentRequest,
	appointmentID int, serviceInfoMap map[int64]*ServiceInfo) ([]*ServiceInstanceData, error) {
	var allServiceInstances []*ServiceInstanceData

	for _, petDetail := range req.GetPets() {
		for _, serviceInstance := range petDetail.GetServices() {
			// 获取主服务时间范围（只有使用DateScheduleConfig的服务才需要）
			var mainServiceStartTime, mainServiceEndTime time.Time
			// 递归创建服务实例，传入0作为parentID（顶层服务实例），level=1
			instances, err := l.createServiceInstanceRecursivelyInTransaction(ctx, tx, req, appointmentID,
				int(petDetail.GetPetId()), serviceInstance, serviceInfoMap, 0, 0, 1,
				mainServiceStartTime, mainServiceEndTime)
			if err != nil {
				return nil, err
			}
			allServiceInstances = append(allServiceInstances, instances...)
		}
	}

	return allServiceInstances, nil
}

// createServiceInstanceRecursivelyInTransaction 在事务中递归创建服务实例和子服务实例，支持三层结构
func (l *Logic) createServiceInstanceRecursivelyInTransaction(ctx context.Context, tx *gorm.DB, req *pb.CreateAppointmentRequest,
	appointmentID int, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	serviceInfoMap map[int64]*ServiceInfo, parentID int,
	rootParentID int, level int, mainServiceStartTime time.Time,
	mainServiceEndTime time.Time) ([]*ServiceInstanceData, error) {

	var allInstances = make([]*ServiceInstanceData, 0)
	// 获取当前服务信息
	serviceInfo := serviceInfoMap[serviceInstance.GetId()]
	if serviceInfo == nil {
		return nil, status.Errorf(codes.InvalidArgument,
			"service info not found for service ID %d", serviceInstance.GetId())
	}
	// 验证服务类型和层级规则
	if err := l.validateServiceTypeAndLevel(serviceInfo, level, serviceInstance); err != nil {
		return nil, err
	}
	if serviceInstance.GetTimeConfig() != nil {
		mainServiceStartTime = serviceInstance.GetTimeConfig().GetStartTime().AsTime()
		mainServiceEndTime = serviceInstance.GetTimeConfig().GetEndTime().AsTime()
	}

	generator := &ServiceInstanceGenerator{
		Req:                  req,
		AppointmentID:        appointmentID,
		PetID:                petID,
		ServiceInstance:      serviceInstance,
		CareType:             serviceInfo.CareType.GetCareCategory(),
		ParentID:             parentID,
		RootParentID:         rootParentID,
		MainServiceStartTime: mainServiceStartTime,
		MainServiceEndTime:   mainServiceEndTime,
		DateType:             int32(serviceInstance.GetDateScheduleConfig().GetDateType()),
	}

	// 生成当前服务实例
	si, err := l.generateSingleServiceInstance(generator)
	if err != nil {
		return nil, err
	}

	// 创建当前服务实例到数据库，获取生成的ID - 使用事务连接
	if err := tx.WithContext(ctx).Create(si).Error; err != nil {
		return nil, err
	}
	currentServiceInstanceID := si.ID

	// 更新服务实例的ID
	allInstances = append(allInstances, &ServiceInstanceData{
		ServiceInstanceID:        int64(currentServiceInstanceID),
		CreateServiceInstanceDef: serviceInstance,
		ServiceInstance:          si,
	})

	// 立即生成 fulfillment 记录 - 使用事务连接
	careType := offeringpb.CareCategory(si.CareType)
	fulfillments := l.generateFulfillmentRecordsFromCreateDef(req,
		appointmentID, petID, serviceInstance, careType, si.ID,
		mainServiceStartTime, mainServiceEndTime)
	if len(fulfillments) > 0 {
		if err := tx.WithContext(ctx).CreateInBatches(fulfillments, 100).Error; err != nil {
			log.ErrorContextf(ctx, "BatchCreateFulfillment err, err:%+v", err)
			return nil, err
		}
	}

	// 确定根父实例ID
	currentRootID := rootParentID
	if currentRootID == 0 {
		// 如果这是顶层服务实例，它本身就是根父实例
		currentRootID = currentServiceInstanceID
	}

	// 递归处理子服务实例
	for _, subServiceInstance := range serviceInstance.GetSubServiceInstances() {
		subInstances, err := l.createServiceInstanceRecursivelyInTransaction(ctx, tx, req, appointmentID,
			petID, subServiceInstance, serviceInfoMap, currentServiceInstanceID, currentRootID, level+1,
			mainServiceStartTime, mainServiceEndTime)
		if err != nil {
			return nil, err
		}
		allInstances = append(allInstances, subInstances...)
	}

	return allInstances, nil
}

// generateFulfillmentRecordsFromCreateDef 从 CreateServiceInstanceDef 生成履约记录
func (l *Logic) generateFulfillmentRecordsFromCreateDef(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int,
	mainStartTime, mainEndTime time.Time) []*fulfillmentRepo.Fulfillment {
	// 根据时间配置类型生成不同的 fulfillment 记录
	switch t := serviceInstance.GetTime().(type) {
	case *pb.CreateServiceInstanceDef_TimeConfig:
		return l.generateFulfillmentFromTimeConfig(req, appointmentID,
			petID, serviceInstance, careType, serviceInstanceID, t.TimeConfig)
	case *pb.CreateServiceInstanceDef_DateScheduleConfig:
		return l.generateFulfillmentFromDateScheduleConfig(req,
			appointmentID, petID, serviceInstance, careType,
			serviceInstanceID, t.DateScheduleConfig, mainStartTime, mainEndTime)
	default:
		// 默认情况，返回空记录
		return []*fulfillmentRepo.Fulfillment{}
	}
}

// generateFulfillmentFromTimeConfig 从 TimeConfig 生成履约记录
func (l *Logic) generateFulfillmentFromTimeConfig(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int, timeConfig *pb.TimeConfig) []*fulfillmentRepo.Fulfillment {
	startTime := time.Unix(timeConfig.GetStartTime().GetSeconds(), int64(timeConfig.GetStartTime().GetNanos()))
	endTime := time.Unix(timeConfig.GetEndTime().GetSeconds(), int64(timeConfig.GetEndTime().GetNanos()))

	switch careType {
	case offeringpb.CareCategory_BOARDING:
		// Boarding 类型按天维度进行履约
		return l.generateDailyFulfillmentRecordsFromTimeRange(req, appointmentID,
			petID, serviceInstance, careType, serviceInstanceID, startTime, endTime)
	case offeringpb.CareCategory_GROOMING, offeringpb.CareCategory_DAYCARE,
		offeringpb.CareCategory_EVALUATION, offeringpb.CareCategory_DOG_WALKING,
		offeringpb.CareCategory_GROUP_CLASS:
		// 其他类型按服务维度创建一条记录
		return l.generateSingleFulfillmentRecordFromTimeRange(req, appointmentID,
			petID, serviceInstance, careType, serviceInstanceID, startTime, endTime)
	default:
		// 默认按服务维度创建
		return l.generateSingleFulfillmentRecordFromTimeRange(req, appointmentID,
			petID, serviceInstance, careType, serviceInstanceID, startTime, endTime)
	}
}

// generateFulfillmentFromDateScheduleConfig 从 DateScheduleConfig 生成履约记录
func (l *Logic) generateFulfillmentFromDateScheduleConfig(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int,
	dateScheduleConfig *pb.DateScheduleConfig, mainStartTime,
	mainEndTime time.Time) []*fulfillmentRepo.Fulfillment {
	dateType := dateScheduleConfig.GetDateType()

	switch dateType {
	case pb.DateType_DATE_TYPE_SPECIFIC_DATE:
		// 特定日期：从 specific_dates 中获取时间
		return l.generateFulfillmentFromSpecificDates(req, appointmentID,
			petID, serviceInstance, careType, serviceInstanceID,
			dateScheduleConfig.GetSpecificDates())
	case pb.DateType_DATE_TYPE_FIRST_DAY, pb.DateType_DATE_TYPE_LAST_DAY, pb.DateType_DATE_TYPE_DATE_EVERYDAY:
		// 常规调度：根据 DateType 和 Duration 生成
		return l.generateFulfillmentFromRegularSchedule(req, appointmentID,
			petID, serviceInstance, careType, serviceInstanceID, dateScheduleConfig,
			mainStartTime, mainEndTime)
	default:
		// 默认情况，返回空记录
		return []*fulfillmentRepo.Fulfillment{}
	}
}

// generateDailyFulfillmentRecordsFromTimeRange 从时间范围按天生成履约记录
func (l *Logic) generateDailyFulfillmentRecordsFromTimeRange(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int, startTime,
	endTime time.Time) []*fulfillmentRepo.Fulfillment {
	var fulfillments []*fulfillmentRepo.Fulfillment

	// 按天循环生成履约记录
	for currentDate := startTime; !currentDate.After(endTime); currentDate = currentDate.AddDate(0, 0, 1) {
		dayStart := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			0, 0, 0, 0, currentDate.Location())
		dayEnd := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			23, 59, 59, 0, currentDate.Location())

		// 如果是第一天，使用实际开始时间
		if currentDate.Year() == startTime.Year() &&
			currentDate.Month() == startTime.Month() &&
			currentDate.Day() == startTime.Day() {
			dayStart = startTime
		}

		// 如果是最后一天，使用实际结束时间，并转换为UTC
		if currentDate.Year() == endTime.Year() &&
			currentDate.Month() == endTime.Month() &&
			currentDate.Day() == endTime.Day() {
			dayEnd = endTime
		}

		fulfillment := &fulfillmentRepo.Fulfillment{
			BusinessID:        req.GetBusinessId(),
			CompanyID:         req.GetCompanyId(),
			AppointmentID:     int64(appointmentID),
			CustomerID:        req.GetCustomerId(),
			PetID:             int64(petID),
			ServiceInstanceID: int64(serviceInstanceID),
			LodgingID:         int64(serviceInstance.GetLodgingId()),
			ServiceFactoryID:  serviceInstance.GetId(),
			CareType:          int32(careType),
			StartTime:         dayStart,
			EndTime:           dayEnd,
			State:             int32(pb.State_STATE_UNSPECIFIED),
		}
		fulfillments = append(fulfillments, fulfillment)
	}

	return fulfillments
}

// generateSingleFulfillmentRecordFromTimeRange 从时间范围生成单条履约记录
func (l *Logic) generateSingleFulfillmentRecordFromTimeRange(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int,
	startTime, endTime time.Time) []*fulfillmentRepo.Fulfillment {
	fulfillment := &fulfillmentRepo.Fulfillment{
		BusinessID:        req.GetBusinessId(),
		CompanyID:         req.GetCompanyId(),
		CustomerID:        req.GetCustomerId(),
		PetID:             int64(petID),
		AppointmentID:     int64(appointmentID),
		LodgingID:         int64(serviceInstance.GetLodgingId()),
		ServiceInstanceID: int64(serviceInstanceID),
		ServiceFactoryID:  serviceInstance.GetId(),
		CareType:          int32(careType),
		StartTime:         startTime,
		EndTime:           endTime.UTC(),
		State:             int32(pb.State_STATE_UNSPECIFIED),
	}

	return []*fulfillmentRepo.Fulfillment{fulfillment}
}

// generateFulfillmentFromSpecificDates 从特定日期生成履约记录
func (l *Logic) generateFulfillmentFromSpecificDates(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int,
	specificDates []*timestamppb.Timestamp) []*fulfillmentRepo.Fulfillment {
	var fulfillments []*fulfillmentRepo.Fulfillment

	for _, timestamp := range specificDates {
		date := timestamp.AsTime()

		switch careType {
		case offeringpb.CareCategory_BOARDING:
			// Boarding 类型：按天生成，从当天 00:00:00 到 23:59:59
			dayStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
			dayEnd := time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 0, date.Location())

			fulfillment := &fulfillmentRepo.Fulfillment{
				BusinessID:        req.GetBusinessId(),
				CompanyID:         req.GetCompanyId(),
				AppointmentID:     int64(appointmentID),
				CustomerID:        req.GetCustomerId(),
				PetID:             int64(petID),
				ServiceInstanceID: int64(serviceInstanceID),
				LodgingID:         int64(serviceInstance.GetLodgingId()),
				ServiceFactoryID:  serviceInstance.GetId(),
				CareType:          int32(careType),
				StartTime:         dayStart,
				EndTime:           dayEnd,
				State:             int32(pb.State_STATE_UNSPECIFIED),
			}
			fulfillments = append(fulfillments, fulfillment)
		default:
			// 其他类型：使用具体的时间戳
			fulfillment := &fulfillmentRepo.Fulfillment{
				BusinessID:        req.GetBusinessId(),
				CompanyID:         req.GetCompanyId(),
				CustomerID:        req.GetCustomerId(),
				PetID:             int64(petID),
				AppointmentID:     int64(appointmentID),
				ServiceInstanceID: int64(serviceInstanceID),
				ServiceFactoryID:  serviceInstance.GetId(),
				CareType:          int32(careType),
				StartTime:         date,
				EndTime:           date, // 对于特定时间点，开始和结束时间相同
				State:             int32(pb.State_STATE_UNSPECIFIED),
			}
			fulfillments = append(fulfillments, fulfillment)
		}
	}

	return fulfillments
}

// generateFulfillmentFromRegularSchedule 从常规调度生成履约记录
func (l *Logic) generateFulfillmentFromRegularSchedule(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int,
	dateScheduleConfig *pb.DateScheduleConfig,
	mainStartTime, mainEndTime time.Time) []*fulfillmentRepo.Fulfillment {
	var fulfillments []*fulfillmentRepo.Fulfillment

	// 获取常规时间和持续时间
	regularTime := dateScheduleConfig.GetRegularTime()
	duration := serviceInstance.GetDuration()

	// 计算常规时间点（小时:分钟:秒）
	regularHour := int(regularTime.GetHours())
	regularMinute := int(regularTime.GetMinutes())
	regularSecond := int(regularTime.GetSeconds())

	// 计算持续时间（秒）
	durationSeconds := int(duration.GetSeconds())

	// 根据 DateType 确定在哪些日期生成 fulfillment 记录
	dateType := dateScheduleConfig.GetDateType()

	switch dateType {
	case pb.DateType_DATE_TYPE_FIRST_DAY:
		// 只在第一天生成
		fulfillments = append(fulfillments, l.generateFulfillmentForDate(
			req, appointmentID, petID, serviceInstance, careType, serviceInstanceID,
			mainStartTime, regularHour, regularMinute, regularSecond, durationSeconds))

	case pb.DateType_DATE_TYPE_LAST_DAY:
		// 只在最后一天生成
		fulfillments = append(fulfillments, l.generateFulfillmentForDate(
			req, appointmentID, petID, serviceInstance, careType, serviceInstanceID,
			mainEndTime, regularHour, regularMinute, regularSecond, durationSeconds))

	case pb.DateType_DATE_TYPE_DATE_EVERYDAY:
		// 在主服务的每一天都生成
		for currentDate :=
			mainStartTime; !currentDate.After(mainEndTime); currentDate =
			currentDate.AddDate(0, 0, 1) {
			fulfillments = append(fulfillments, l.generateFulfillmentForDate(
				req, appointmentID, petID, serviceInstance, careType, serviceInstanceID,
				currentDate, regularHour, regularMinute, regularSecond, durationSeconds))
		}

	case pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY:
		// 除了第一天，其他天都生成
		for currentDate :=
			mainStartTime.AddDate(0, 0, 1); !currentDate.After(mainEndTime); currentDate =
			currentDate.AddDate(0, 0, 1) {
			fulfillments = append(fulfillments, l.generateFulfillmentForDate(
				req, appointmentID, petID, serviceInstance, careType, serviceInstanceID,
				currentDate, regularHour, regularMinute, regularSecond, durationSeconds))
		}

	case pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY:
		// 除了最后一天，其他天都生成
		for currentDate := mainStartTime; currentDate.Before(mainEndTime); currentDate =
			currentDate.AddDate(0, 0, 1) {
			fulfillments = append(fulfillments, l.generateFulfillmentForDate(
				req, appointmentID, petID, serviceInstance, careType, serviceInstanceID,
				currentDate, regularHour, regularMinute, regularSecond, durationSeconds))
		}
	}

	return fulfillments
}

// generateFulfillmentForDate 为指定日期生成 fulfillment 记录
func (l *Logic) generateFulfillmentForDate(req *pb.CreateAppointmentRequest,
	appointmentID, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careType offeringpb.CareCategory, serviceInstanceID int, date time.Time,
	regularHour, regularMinute, regularSecond,
	durationSeconds int) *fulfillmentRepo.Fulfillment {
	// 构建指定日期的常规时间点
	startTime := time.Date(date.Year(), date.Month(), date.Day(),
		regularHour, regularMinute, regularSecond, 0, date.Location())

	// 计算结束时间（开始时间 + 持续时间）
	endTime := startTime.Add(time.Duration(durationSeconds) * time.Second)

	return &fulfillmentRepo.Fulfillment{
		BusinessID:        req.GetBusinessId(),
		CompanyID:         req.GetCompanyId(),
		AppointmentID:     int64(appointmentID),
		CustomerID:        req.GetCustomerId(),
		PetID:             int64(petID),
		ServiceInstanceID: int64(serviceInstanceID),
		LodgingID:         int64(serviceInstance.GetLodgingId()),
		ServiceFactoryID:  serviceInstance.GetId(),
		CareType:          int32(careType),
		StartTime:         startTime,
		EndTime:           endTime,
		State:             int32(pb.State_STATE_UNSPECIFIED),
	}
}

func verifyCreateAppointmentRequest(req *pb.CreateAppointmentRequest) error {
	if req.GetBusinessId() <= 0 {
		return status.Error(codes.InvalidArgument, "business_id must be greater than 0")
	}
	if req.GetCompanyId() <= 0 {
		return status.Error(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetCustomerId() <= 0 {
		return status.Error(codes.InvalidArgument, "customer_id must be greater than 0")
	}
	startTime := req.GetStartTime().AsTime()
	endTime := req.GetEndTime().AsTime()
	if startTime.After(endTime) {
		return status.Error(codes.InvalidArgument, "start_time must be before end_time")
	}
	if len(req.GetPets()) == 0 {
		return status.Error(codes.InvalidArgument, "pets cannot be empty")
	}
	// 验证第一层服务实例必须使用 TimeConfig 且 StartTime 和 EndTime 必须有值
	for _, petDetail := range req.GetPets() {
		for _, serviceInstance := range petDetail.GetServices() {
			if err := verifyFirstLevelServiceInstance(serviceInstance); err != nil {
				return err
			}
		}
	}

	return nil
}

// verifyFirstLevelServiceInstance 验证第一层服务实例的配置
func verifyFirstLevelServiceInstance(serviceInstance *pb.CreateServiceInstanceDef) error {
	switch t := serviceInstance.GetTime().(type) {
	case *pb.CreateServiceInstanceDef_TimeConfig:
		// 验证 TimeConfig 的 StartTime 和 EndTime 必须有值
		if t.TimeConfig.GetStartTime() == nil {
			return status.Error(codes.InvalidArgument,
				"first level service instance TimeConfig start_time is required")
		}
		if t.TimeConfig.GetEndTime() == nil {
			return status.Error(codes.InvalidArgument,
				"first level service instance TimeConfig end_time is required")
		}

		return nil
	case *pb.CreateServiceInstanceDef_DateScheduleConfig:
		// 第一层服务实例不能使用 DateScheduleConfig
		return status.Error(codes.InvalidArgument,
			"first level service instance cannot use DateScheduleConfig, only TimeConfig is allowed")
	default:
		// 没有配置时间信息
		return status.Error(codes.InvalidArgument,
			"first level service instance must configure time information")
	}
}

// validateServiceTypeAndLevel 验证服务类型和层级规则
func (l *Logic) validateServiceTypeAndLevel(serviceInfo *ServiceInfo, level int,
	serviceInstance *pb.CreateServiceInstanceDef) error {
	serviceType := serviceInfo.Service.GetType()

	// 第一层：只能是Boarding服务，不能是addon
	if level == 1 {
		if serviceType == offeringpb.Service_ADD_ON {
			return status.Errorf(codes.InvalidArgument, "第一层服务不能是addon类型，只能是service类型")
		}
		// 第一层service不能有DateScheduleConfig
		if serviceInstance.GetDateScheduleConfig() != nil {
			return status.Errorf(codes.InvalidArgument, "第一层服务不能使用DateScheduleConfig，只能使用TimeConfig")
		}
	}
	// 检查第一层服务是否跨天，如果不跨天，子服务不能使用DateScheduleConfig
	if serviceInstance.GetTimeConfig() != nil {
		startTime := serviceInstance.GetTimeConfig().GetStartTime().AsTime()
		endTime := serviceInstance.GetTimeConfig().GetEndTime().AsTime()

		if !l.isCrossDayService(startTime, endTime) {
			// 如果不跨天，检查子服务是否使用了DateScheduleConfig
			if err := l.validateSubServiceInstancesForNonCrossDay(
				serviceInstance.GetSubServiceInstances()); err != nil {
				return err
			}
		}
	}

	return nil
}

// isCrossDayService 检查服务是否是跨天的
func (l *Logic) isCrossDayService(startTime, endTime time.Time) bool {
	// 比较开始时间和结束时间的日期部分
	startDate := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, startTime.Location())
	endDate := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, endTime.Location())

	// 如果开始日期和结束日期不同，则是跨天服务
	return !startDate.Equal(endDate)
}

// validateSubServiceInstancesForNonCrossDay 验证非跨天服务的子服务实例
func (l *Logic) validateSubServiceInstancesForNonCrossDay(subServiceInstances []*pb.CreateServiceInstanceDef) error {
	for _, subService := range subServiceInstances {
		// 检查子服务是否使用了DateScheduleConfig
		if subService.GetDateScheduleConfig() != nil {
			return status.Errorf(codes.InvalidArgument, "非跨天的主服务不能添加使用DateScheduleConfig的子服务")
		}
	}

	return nil
}

// generateSingleServiceInstance 为单个服务生成一个服务实例
func (l *Logic) generateSingleServiceInstance(
	generator *ServiceInstanceGenerator) (*serviceinstance.ServiceInstance, error) {
	serviceInstance := generator.ServiceInstance

	// 根据时间配置类型生成服务实例
	switch t := serviceInstance.GetTime().(type) {
	case *pb.CreateServiceInstanceDef_TimeConfig:
		return l.generateInstanceFromTimeConfig(generator, t.TimeConfig)
	case *pb.CreateServiceInstanceDef_DateScheduleConfig:
		return l.generateInstanceFromDateScheduleConfig(generator, t.DateScheduleConfig)
	default:
		return nil, status.Errorf(codes.InvalidArgument, "服务实例必须配置时间信息")
	}
}

// generateInstanceFromTimeConfig 从TimeConfig生成服务实例
func (l *Logic) generateInstanceFromTimeConfig(generator *ServiceInstanceGenerator,
	timeConfig *pb.TimeConfig) (*serviceinstance.ServiceInstance, error) {
	startTime := time.Unix(timeConfig.GetStartTime().GetSeconds(), int64(timeConfig.GetStartTime().GetNanos()))
	endTime := time.Unix(timeConfig.GetEndTime().GetSeconds(), int64(timeConfig.GetEndTime().GetNanos()))

	return &serviceinstance.ServiceInstance{
		BusinessID:       int(generator.Req.GetBusinessId()),
		CustomerID:       int(generator.Req.GetCustomerId()),
		CompanyID:        int(generator.Req.GetCompanyId()),
		AppointmentID:    generator.AppointmentID,
		PetID:            generator.PetID,
		CareType:         int(generator.CareType),
		DateType:         0, // TimeConfig 不设置 DateType
		ServiceFactoryID: int(generator.ServiceInstance.GetId()),
		StartDate:        serviceinstance.TimeToPgDate(startTime),
		EndDate:          serviceinstance.TimeToPgDate(endTime),
		ParentID:         generator.ParentID,
		RootParentID:     generator.RootParentID,
	}, nil
}

// generateInstanceFromDateScheduleConfig 从DateScheduleConfig生成服务实例
func (l *Logic) generateInstanceFromDateScheduleConfig(generator *ServiceInstanceGenerator,
	dateScheduleConfig *pb.DateScheduleConfig) (*serviceinstance.ServiceInstance, error) {
	dateType := int32(dateScheduleConfig.GetDateType())

	switch dateScheduleConfig.GetDateType() {
	case pb.DateType_DATE_TYPE_SPECIFIC_DATE:
		return l.generateInstanceFromSpecificDates(generator, dateScheduleConfig.GetSpecificDates())
	default:
		return l.generateInstanceFromRegularSchedule(generator, dateScheduleConfig, dateType)
	}
}

func (l *Logic) generateInstanceFromSpecificDates(generator *ServiceInstanceGenerator,
	specificDates []*timestamppb.Timestamp) (*serviceinstance.ServiceInstance, error) {
	// 特定日期：从specific_dates中获取时间
	if len(specificDates) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "DATE_TYPE_SPECIFIC_DATE必须提供specific_dates")
	}
	specificDatesMap := make([]time.Time, len(specificDates))
	for i, date := range specificDates {
		specificDatesMap[i] = date.AsTime()
	}

	return &serviceinstance.ServiceInstance{
		BusinessID:       int(generator.Req.GetBusinessId()),
		CustomerID:       int(generator.Req.GetCustomerId()),
		CompanyID:        int(generator.Req.GetCompanyId()),
		AppointmentID:    generator.AppointmentID,
		PetID:            generator.PetID,
		CareType:         int(generator.CareType),
		DateType:         int(generator.DateType),
		ServiceFactoryID: int(generator.ServiceInstance.GetId()),
		ParentID:         generator.ParentID,
		RootParentID:     generator.RootParentID,
		SpecificDates:    serviceinstance.DateSlice(specificDatesMap),
	}, nil
}

// generateInstanceFromRegularSchedule 从常规调度生成服务实例
func (l *Logic) generateInstanceFromRegularSchedule(generator *ServiceInstanceGenerator,
	dateScheduleConfig *pb.DateScheduleConfig,
	dateType int32) (*serviceinstance.ServiceInstance, error) {
	// 检查Duration参数是否存在
	duration := generator.ServiceInstance.GetDuration()
	if duration == nil {
		return nil, status.Errorf(codes.InvalidArgument, "DateScheduleConfig类型必须提供Duration参数")
	}

	serviceStartTime := generator.MainServiceStartTime
	serviceEndTime := generator.MainServiceEndTime
	var startTime, endTime time.Time

	switch dateScheduleConfig.GetDateType() {
	case pb.DateType_DATE_TYPE_DATE_EVERYDAY:
		// 每日：使用整个主服务时间范围
		startTime = serviceStartTime
		endTime = serviceEndTime
	case pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY:
		// 排除退房日：从开始时间到结束时间的前一天
		startTime = serviceStartTime
		if serviceStartTime.Before(serviceEndTime) {
			// 检查是否是同一天
			if serviceStartTime.Year() == serviceEndTime.Year() &&
				serviceStartTime.Month() == serviceEndTime.Month() &&
				serviceStartTime.Day() == serviceEndTime.Day() {
				// 如果是同一天，则开始时间和结束时间都设为开始时间
				endTime = serviceStartTime
			} else {
				endTime = serviceEndTime.AddDate(0, 0, -1)
			}
		} else {
			endTime = serviceStartTime
		}
	case pb.DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY:
		// 排除入住日：从第二天开始到结束时间
		if serviceStartTime.Before(serviceEndTime) {
			// 检查是否是同一天
			if serviceStartTime.Year() == serviceEndTime.Year() &&
				serviceStartTime.Month() == serviceEndTime.Month() &&
				serviceStartTime.Day() == serviceEndTime.Day() {
				// 如果是同一天，则开始时间和结束时间都设为开始时间
				startTime = serviceStartTime
				endTime = serviceStartTime
			} else {
				startTime = serviceStartTime.AddDate(0, 0, 1)
				endTime = serviceEndTime
			}
		} else {
			startTime = serviceStartTime
			endTime = serviceEndTime
		}
	case pb.DateType_DATE_TYPE_FIRST_DAY:
		// 第一天：只使用主服务的第一天
		startTime = serviceStartTime
		endTime = serviceStartTime
	case pb.DateType_DATE_TYPE_LAST_DAY:
		// 最后一天：只使用主服务的最后一天
		startTime = serviceEndTime
		endTime = serviceEndTime
	default:
		// 默认：使用主服务开始时间
		startTime = serviceStartTime
		endTime = serviceStartTime
	}

	return &serviceinstance.ServiceInstance{
		BusinessID:       int(generator.Req.GetBusinessId()),
		CustomerID:       int(generator.Req.GetCustomerId()),
		CompanyID:        int(generator.Req.GetCompanyId()),
		AppointmentID:    generator.AppointmentID,
		PetID:            generator.PetID,
		CareType:         int(generator.CareType),
		DateType:         int(dateType),
		ServiceFactoryID: int(generator.ServiceInstance.GetId()),
		StartDate:        serviceinstance.TimeToPgDate(startTime),
		EndDate:          serviceinstance.TimeToPgDate(endTime),
		ParentID:         generator.ParentID,
		RootParentID:     generator.RootParentID,
	}, nil
}

// getAllServiceIDs 获取pets下所有serviceTemplateID（递归处理SubServiceInstance）
func (l *Logic) getAllServiceIDs(pets []*pb.CreatePetDetailDef) []int64 {
	var serviceTemplateIDs []int64
	result := make(map[int64]bool) // 用于去重

	for _, petDetail := range pets {
		for _, serviceInstance := range petDetail.GetServices() {
			l.collectServiceTemplateIDsRecursively(serviceInstance, result)
		}
	}

	// 将map中的key转换为slice
	for templateID := range result {
		serviceTemplateIDs = append(serviceTemplateIDs, templateID)
	}

	// 确保返回空slice而不是nil
	if len(serviceTemplateIDs) == 0 {
		return []int64{}
	}

	return serviceTemplateIDs
}

// collectServiceTemplateIDsRecursively 递归收集所有serviceTemplateID
func (l *Logic) collectServiceTemplateIDsRecursively(serviceInstance *pb.CreateServiceInstanceDef,
	result map[int64]bool) {
	// 添加当前服务实例的templateID
	templateID := serviceInstance.GetId()
	if templateID > 0 {
		result[templateID] = true
	}

	// 递归处理子服务实例
	for _, subServiceInstance := range serviceInstance.GetSubServiceInstances() {
		l.collectServiceTemplateIDsRecursively(subServiceInstance, result)
	}
}

type ServiceInfo struct {
	CareType *offeringpb.CareType
	Service  *offeringpb.Service
}

func (l *Logic) getCareTypesByServiceIDs(ctx context.Context,
	serviceTemplateIDs []int64) (map[int64]*ServiceInfo, error) {
	result := make(map[int64]*ServiceInfo)
	for _, templateID := range serviceTemplateIDs {
		// 调用offering服务获取service信息
		service, err := l.offeringServiceCli.GetService(ctx, templateID)
		if err != nil {
			log.Errorf("failed to get service %d: %v", templateID, err)

			return nil, err
		}
		// 从service中获取careTypeID
		careTypeID := service.GetCareTypeId()
		if careTypeID == 0 {
			result[templateID] = &ServiceInfo{
				Service: service,
			}

			continue
		}
		// 调用offering服务获取careType信息
		careType, err := l.offeringCareTypeCli.GetCareType(ctx, careTypeID)
		if err != nil {
			return nil, err
		}
		// 直接使用offering的CareCategory
		result[templateID] = &ServiceInfo{
			CareType: careType,
			Service:  service,
		}
	}

	return result, nil
}

func (l *Logic) calculateServiceItemType(serviceInfoMap map[int64]*ServiceInfo) int32 {
	// 使用map来去重careType
	uniqueCareTypes := make(map[offeringpb.CareCategory]bool)
	// 收集所有唯一的careType
	for _, serviceInfo := range serviceInfoMap {
		careType := serviceInfo.CareType.GetCareCategory()
		uniqueCareTypes[careType] = true
	}
	var serviceItemType int32
	for careType := range uniqueCareTypes {
		serviceItemType += int32(careType)
	}

	return serviceItemType
}

// generateFulfillmentRecords 根据 CareCategory 生成履约记录（不直接创建，只返回记录列表）
func (l *Logic) generateFulfillmentRecords(si *serviceinstance.ServiceInstance,
	careType offeringpb.CareCategory) []*fulfillmentRepo.Fulfillment {
	switch careType {
	case offeringpb.CareCategory_BOARDING:
		// Boarding 类型按天维度进行履约
		return l.generateDailyFulfillmentRecords(si)
	case offeringpb.CareCategory_GROOMING, offeringpb.CareCategory_DAYCARE,
		offeringpb.CareCategory_EVALUATION, offeringpb.CareCategory_DOG_WALKING,
		offeringpb.CareCategory_GROUP_CLASS:
		// 其他类型按服务维度创建一条记录
		return l.generateSingleFulfillmentRecord(si)
	default:
		// 默认按服务维度创建
		return l.generateSingleFulfillmentRecord(si)
	}
}

// generateDailyFulfillmentRecords 按天生成履约记录（用于 Boarding 等跨天服务）
func (l *Logic) generateDailyFulfillmentRecords(si *serviceinstance.ServiceInstance) []*fulfillmentRepo.Fulfillment {
	var fulfillments []*fulfillmentRepo.Fulfillment
	startDate := serviceinstance.PgDateToTime(si.StartDate)
	endDate := serviceinstance.PgDateToTime(si.EndDate)
	// 按天循环生成履约记录
	for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {
		dayStart := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			0, 0, 0, 0, currentDate.Location())
		dayEnd := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			23, 59, 59, 0, currentDate.Location())
		// 如果是第一天，使用实际开始时间
		if currentDate.Equal(startDate) {
			dayStart = startDate
		}
		// 如果是最后一天，使用实际结束时间
		if currentDate.Year() == endDate.Year() &&
			currentDate.Month() == endDate.Month() &&
			currentDate.Day() == endDate.Day() {
			dayEnd = endDate
		}
		fulfillment := &fulfillmentRepo.Fulfillment{
			BusinessID:        int64(si.BusinessID),
			CompanyID:         int64(si.CompanyID),
			AppointmentID:     int64(si.AppointmentID),
			CustomerID:        int64(si.CustomerID),
			PetID:             int64(si.PetID),
			ServiceInstanceID: int64(si.ID),
			ServiceFactoryID:  int64(si.ServiceFactoryID),
			CareType:          int32(si.CareType),
			StartTime:         dayStart,
			EndTime:           dayEnd,
			State:             int32(pb.State_STATE_UNSPECIFIED),
		}
		fulfillments = append(fulfillments, fulfillment)
	}

	return fulfillments
}

// generateSingleFulfillmentRecord 生成单条履约记录（用于 Grooming 等不跨天服务）
func (l *Logic) generateSingleFulfillmentRecord(si *serviceinstance.ServiceInstance) []*fulfillmentRepo.Fulfillment {
	fulfillment := &fulfillmentRepo.Fulfillment{
		BusinessID:        int64(si.BusinessID),
		CompanyID:         int64(si.CompanyID),
		CustomerID:        int64(si.CustomerID),
		PetID:             int64(si.PetID),
		ServiceInstanceID: int64(si.ID),
		ServiceFactoryID:  int64(si.ServiceFactoryID),
		CareType:          int32(si.CareType),
		StartTime:         serviceinstance.PgDateToTime(si.StartDate),
		EndTime:           serviceinstance.PgDateToTime(si.EndDate),
		State:             int32(pb.State_STATE_UNSPECIFIED),
	}

	return []*fulfillmentRepo.Fulfillment{fulfillment}
}

// calculateAppointmentTimeRange 从所有service中计算appointment的时间范围
// 返回最早开始时间和最晚结束时间
func (l *Logic) calculateAppointmentTimeRange(pets []*pb.CreatePetDetailDef) (time.Time, time.Time, error) {
	var earliestStartTime time.Time
	var latestEndTime time.Time
	hasValidService := false

	for _, petDetail := range pets {
		for _, serviceInstance := range petDetail.GetServices() {
			startTime, endTime, err := l.extractServiceTimeRange(serviceInstance)
			if err != nil {
				return time.Time{}, time.Time{}, err
			}

			// 跳过没有时间配置的service
			if startTime.IsZero() || endTime.IsZero() {
				continue
			}

			if !hasValidService {
				// 第一个有效的service
				earliestStartTime = startTime
				latestEndTime = endTime
				hasValidService = true
			} else {
				// 更新最早开始时间和最晚结束时间
				if startTime.Before(earliestStartTime) {
					earliestStartTime = startTime
				}
				if endTime.After(latestEndTime) {
					latestEndTime = endTime
				}
			}
		}
	}

	if !hasValidService {
		return time.Time{}, time.Time{}, status.Error(codes.InvalidArgument,
			"no valid service with time configuration found")
	}

	return earliestStartTime, latestEndTime, nil
}

// extractServiceTimeRange 从service实例中提取时间范围
// 递归处理子服务实例，找到最早开始时间和最晚结束时间
func (l *Logic) extractServiceTimeRange(serviceInstance *pb.CreateServiceInstanceDef) (time.Time, time.Time, error) {
	var earliestStartTime time.Time
	var latestEndTime time.Time
	hasValidTime := false

	// 检查当前service的时间配置
	switch t := serviceInstance.GetTime().(type) {
	case *pb.CreateServiceInstanceDef_TimeConfig:
		if t.TimeConfig.GetStartTime() != nil && t.TimeConfig.GetEndTime() != nil {
			startTime := time.Unix(t.TimeConfig.GetStartTime().GetSeconds(),
				int64(t.TimeConfig.GetStartTime().GetNanos()))
			endTime := time.Unix(t.TimeConfig.GetEndTime().GetSeconds(),
				int64(t.TimeConfig.GetEndTime().GetNanos()))

			earliestStartTime = startTime
			latestEndTime = endTime
			hasValidTime = true
		}
	case *pb.CreateServiceInstanceDef_DateScheduleConfig:
		// DateScheduleConfig类型的service不直接提供具体时间，跳过
		// 这类service的时间会通过DateScheduleConfig的逻辑在后续处理中确定
	}

	// 递归处理子服务实例
	for _, subServiceInstance := range serviceInstance.GetSubServiceInstances() {
		subStartTime, subEndTime, err := l.extractServiceTimeRange(subServiceInstance)
		if err != nil {
			return time.Time{}, time.Time{}, err
		}

		// 跳过没有时间配置的子service
		if subStartTime.IsZero() || subEndTime.IsZero() {
			continue
		}

		if !hasValidTime {
			// 第一个有效的时间配置
			earliestStartTime = subStartTime
			latestEndTime = subEndTime
			hasValidTime = true
		} else {
			// 更新最早开始时间和最晚结束时间
			if subStartTime.Before(earliestStartTime) {
				earliestStartTime = subStartTime
			}
			if subEndTime.After(latestEndTime) {
				latestEndTime = subEndTime
			}
		}
	}

	return earliestStartTime, latestEndTime, nil
}

// createServiceInstanceRecursivelyInTransaction 在事务中递归创建服务实例和子服务实例，支持三层结构
func (l *Logic) createServiceInstanceRecursivelyInTransaction(ctx context.Context, tx *gorm.DB, req *pb.CreateAppointmentRequest,
	appointmentID int, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	serviceInfoMap map[int64]*ServiceInfo, parentID int,
	rootParentID int, level int, mainServiceStartTime time.Time,
	mainServiceEndTime time.Time) ([]*ServiceInstanceData, error) {

	var allInstances = make([]*ServiceInstanceData, 0)
	// 获取当前服务信息
	serviceInfo := serviceInfoMap[serviceInstance.GetId()]
	if serviceInfo == nil {
		return nil, status.Errorf(codes.InvalidArgument,
			"service info not found for service ID %d", serviceInstance.GetId())
	}
	// 验证服务类型和层级规则
	if err := l.validateServiceTypeAndLevel(serviceInfo, level, serviceInstance); err != nil {
		return nil, err
	}
	if serviceInstance.GetTimeConfig() != nil {
		mainServiceStartTime = serviceInstance.GetTimeConfig().GetStartTime().AsTime()
		mainServiceEndTime = serviceInstance.GetTimeConfig().GetEndTime().AsTime()
	}

	generator := &ServiceInstanceGenerator{
		Req:                  req,
		AppointmentID:        appointmentID,
		PetID:                petID,
		ServiceInstance:      serviceInstance,
		CareType:             serviceInfo.CareType.GetCareCategory(),
		ParentID:             parentID,
		RootParentID:         rootParentID,
		MainServiceStartTime: mainServiceStartTime,
		MainServiceEndTime:   mainServiceEndTime,
		DateType:             int32(serviceInstance.GetDateScheduleConfig().GetDateType()),
	}

	// 生成当前服务实例
	si, err := l.generateSingleServiceInstance(generator)
	if err != nil {
		return nil, err
	}

	// 创建当前服务实例到数据库，获取生成的ID - 使用事务连接
	if err := tx.WithContext(ctx).Create(si).Error; err != nil {
		return nil, err
	}
	currentServiceInstanceID := si.ID

	// 更新服务实例的ID
	allInstances = append(allInstances, &ServiceInstanceData{
		ServiceInstanceID:        int64(currentServiceInstanceID),
		CreateServiceInstanceDef: serviceInstance,
		ServiceInstance:          si,
	})

	// 立即生成 fulfillment 记录 - 使用事务连接
	careType := offeringpb.CareCategory(si.CareType)
	fulfillments := l.generateFulfillmentRecordsFromCreateDef(req,
		appointmentID, petID, serviceInstance, careType, si.ID,
		mainServiceStartTime, mainServiceEndTime)
	if len(fulfillments) > 0 {
		if err := tx.WithContext(ctx).CreateInBatches(fulfillments, 100).Error; err != nil {
			return nil, err
		}
	}

	// 确定根父实例ID
	currentRootID := rootParentID
	if currentRootID == 0 {
		// 如果这是顶层服务实例，它本身就是根父实例
		currentRootID = currentServiceInstanceID
	}

	// 递归处理子服务实例
	for _, subServiceInstance := range serviceInstance.GetSubServiceInstances() {
		subInstances, err := l.createServiceInstanceRecursivelyInTransaction(ctx, tx, req, appointmentID,
			petID, subServiceInstance, serviceInfoMap, currentServiceInstanceID, currentRootID, level+1,
			mainServiceStartTime, mainServiceEndTime)
		if err != nil {
			return nil, err
		}
		allInstances = append(allInstances, subInstances...)
	}

	return allInstances, nil
}
