package report

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/config"
	reportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
	sendrecordrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func (l *Logic) SendSmsMessage(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 查询 report
	report, err := l.GetFulfillmentReport(ctx, &GetFulfillmentReport{
		ID: req.GetFulfillmentReportId(),
	})
	if err != nil {
		return nil, err
	}
	if report == nil {
		return nil, status.Errorf(codes.NotFound, "report not found")
	}

	// 查询历史发送记录
	sendRecordHistory, err := l.sendRecordRepo.FindByReportIDAndSendMethod(
		ctx, report.ID, int32(fulfillmentpb.SendMethod_SMS))
	if err != nil {
		return nil, err
	}
	// 初始化或获取发送记录
	var sendRecord *sendrecordrepo.SendRecord
	if sendRecordHistory == nil {
		// 创建新的发送记录
		sendRecord = &sendrecordrepo.SendRecord{
			ReportID:      report.ID,
			CompanyID:     report.CompanyID,
			BusinessID:    report.BusinessID,
			AppointmentID: report.AppointmentID,
			PetID:         report.PetID,
			SendMethod:    int32(fulfillmentpb.SendMethod_SMS),
			SentBy:        req.GetStaffId(),
		}
	} else {
		// 使用现有记录
		sendRecord = &sendrecordrepo.SendRecord{
			ID:            sendRecordHistory.ID,
			ReportID:      report.ID,
			CompanyID:     report.CompanyID,
			BusinessID:    report.BusinessID,
			AppointmentID: report.AppointmentID,
			PetID:         report.PetID,
			SendMethod:    int32(fulfillmentpb.SendMethod_SMS),
			SentBy:        req.GetStaffId(),
		}
	}

	// 设置发送时间和内容
	sendRecord.SentTime = time.Now().UTC()
	contentJSON, _ := json.Marshal(report.Content)
	sendRecord.ContentJSON = string(contentJSON)

	// 构建短信内容
	messageBody, err := l.BuildSmsSendContent(summaryInfo)
	if err != nil {
		return nil, err
	}

	// 发送短信
	sendResult := false
	var errorMessage string
	var targetType int32
	if report.CareType == offeringpb.CareCategory_GROOMING {
		targetType = message.MessageTargetTypeGroomingReport
	} else {
		targetType = message.MessageTargetTypeDailyReport
	}

	// 调用实际的短信发送服务
	sendMessages := &message.SendMessages{
		BusinessID: int32(report.BusinessID),
		StaffID:    int32(req.GetStaffId()),
		Customer: &message.SendMessageCustomerParams{
			CustomerID: int32(report.CustomerID),
		},
		Method:      message.MessageMethodMsg,
		TargetType:  targetType,
		TargetID:    int32(report.AppointmentID),
		MessageBody: messageBody,
	}
	result, err := l.messageRepo.SendServicesMessageToCustomer(ctx, sendMessages)
	if err != nil {
		errorMessage = defaultSendSingleFailed
		sendResult = false
	} else if result.Code != 0 {
		sendResult = false
		errorMessage = result.Message
	} else {
		sendResult = true
	}

	// 更新发送记录
	sendRecord.IsSentSuccess = &sendResult
	sendRecord.ErrorMessage = &errorMessage

	// 保存或更新发送记录
	if sendRecordHistory == nil {
		err = l.sendRecordRepo.Create(ctx, sendRecord)
	} else {
		err = l.sendRecordRepo.Update(ctx, sendRecord)
	}
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save send record: %v", err)
	}

	// 更新报告状态为已发送
	if sendResult {
		_, err = l.updateReport(ctx, report, &Report{Status: fulfillmentpb.ReportStatus_SENT})
		if err != nil {
			log.ErrorContextf(ctx, "failed to update report status: %v", err)
		}
	}

	return &fulfillmentpb.SendFulfillmentReportResponse{
		SendResult: &fulfillmentpb.FulfillmentReportSendResult{
			FulfillmentReportId: report.ID,
			SendMethod:          fulfillmentpb.SendMethod_SMS,
			IsSentSuccess:       sendResult,
			ErrorMessage:        errorMessage,
		},
	}, nil
}

func (l *Logic) listSendReportRecords(ctx context.Context, req *fulfillmentpb.ListSendReportRecordsRequest) (
	[]*SendRecord, int64, error) {

	// 校验请求
	if err := l.verifyListSendReportRecordsRequest(req); err != nil {
		return nil, 0, err
	}

	// 构建查询参数
	baseParam := &sendrecordrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}

	// 设置分页信息
	baseParam.PaginationInfo = l.buildSendRecordPaginationInfo(req)

	// 构建过滤条件
	filter := l.buildSendRecordFilter(req.GetFilter())

	// 查询总数
	total, err := l.sendRecordRepo.Count(ctx, baseParam, filter)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to count send records: %v", err)
	}

	// 查询列表
	sendRecords, err := l.sendRecordRepo.List(ctx, baseParam, filter)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to list send records: %v", err)
	}

	sendRecordLogics, err := ConvertSendRecordRepoToLogics(ctx, sendRecords)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to convert send records to logic: %v", err)
	}

	return sendRecordLogics, total, nil
}

func (l *Logic) listSendReportRecord(ctx context.Context, companyID, businessID int64, filter *sendrecordrepo.Filter) (
	[]*SendRecord, error) {

	// 构建查询参数
	baseParam := &sendrecordrepo.BaseParam{
		CompanyID:  companyID,
		BusinessID: businessID,
	}

	// 查询列表
	sendRecords, err := l.sendRecordRepo.List(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list send records: %v", err)
	}

	sendRecordLogics, err := ConvertSendRecordRepoToLogics(ctx, sendRecords)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to convert send records to logic: %v", err)
	}

	return sendRecordLogics, nil
}

func (l *Logic) ListSendReportRecords(ctx context.Context, req *fulfillmentpb.ListSendReportRecordsRequest) (
	*fulfillmentpb.ListSendReportRecordsResponse, error) {

	sendRecords, total, err := l.listSendReportRecords(ctx, req)
	if err != nil {
		return nil, err
	}

	if len(sendRecords) == 0 {
		response := &fulfillmentpb.ListSendReportRecordsResponse{
			SendRecords: []*fulfillmentpb.FulfillmentReportSendRecord{},
			Pagination:  req.GetPagination(),
			Total:       0,
		}

		return response, nil
	}

	// get reports
	reportIDs := lo.Map(sendRecords, func(record *SendRecord, _ int) int64 {
		return record.ReportID
	})
	var reports []*Report
	if len(reportIDs) > 0 {
		reports, _, err = l.listFulfillmentReport(ctx, &fulfillmentpb.ListFulfillmentReportRequest{
			CompanyId:  req.CompanyId,
			BusinessId: req.BusinessId,
			Filter: &fulfillmentpb.ListFulfillmentReportConfigFilter{
				ReportIds: reportIDs,
			},
			Pagination: &fulfillmentpb.PaginationRef{
				Offset: defaultOffset,
				Limit:  maxLimit,
			},
		})
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to list report by ids: %v", err)
		}
	}

	pbSendRecords := ConvertFulfillmentReportSendRecords(sendRecords, reports)

	response := &fulfillmentpb.ListSendReportRecordsResponse{
		SendRecords: pbSendRecords,
		Pagination:  req.GetPagination(),
		Total:       int32(total),
	}

	return response, nil
}

// verifyListSendReportRecordsRequest 验证列表发送记录请求
func (l *Logic) verifyListSendReportRecordsRequest(req *fulfillmentpb.ListSendReportRecordsRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	// 当没有指定以下过滤条件时，必须提供 company_id 和 business_id
	if req.GetFilter() == nil ||
		(len(req.GetFilter().GetReportIds()) == 0 &&
			len(req.GetFilter().GetAppointmentIds()) == 0 &&
			len(req.GetFilter().GetPetIds()) == 0) {
		if req.GetCompanyId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
		}
		if req.GetBusinessId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
		}
	}

	// 验证分页参数
	if req.GetPagination() != nil {
		if req.GetPagination().GetOffset() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination offset cannot be negative")
		}
		if req.GetPagination().GetLimit() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination limit cannot be negative")
		}
	}

	return nil
}

// buildSendRecordPaginationInfo 构建发送记录分页信息
func (l *Logic) buildSendRecordPaginationInfo(
	req *fulfillmentpb.ListSendReportRecordsRequest) *sendrecordrepo.PaginationInfo {
	if req.GetPagination() == nil {
		return &sendrecordrepo.PaginationInfo{
			Offset: defaultOffset,
			Limit:  defaultLimit,
		}
	}

	return &sendrecordrepo.PaginationInfo{
		Offset: req.GetPagination().GetOffset(),
		Limit:  req.GetPagination().GetLimit(),
	}
}

// buildSendRecordFilter 构建发送记录过滤条件
func (l *Logic) buildSendRecordFilter(filter *fulfillmentpb.ListSendReportRecordsFilter) *sendrecordrepo.Filter {
	repoFilter := &sendrecordrepo.Filter{}

	if filter != nil {
		// 处理预约ID过滤
		if len(filter.GetAppointmentIds()) > 0 {
			repoFilter.AppointmentIDs = filter.GetAppointmentIds()
		}
		// 处理宠物ID过滤
		if len(filter.GetPetIds()) > 0 {
			repoFilter.PetIDs = filter.GetPetIds()
		}
		// 处理护理类型过滤
		if len(filter.GetCareTypes()) > 0 {
			careTypes := make([]int32, 0, len(filter.GetCareTypes()))
			for _, careType := range filter.GetCareTypes() {
				careTypes = append(careTypes, int32(careType.Number()))
			}
			repoFilter.CareTypes = careTypes
		}
		// 处理发送方式过滤
		if filter.GetSendMethod() != fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED {
			repoFilter.SendMethods = []int32{int32(filter.GetSendMethod().Number())}
		}
		// 处理 report ids 过滤
		if len(filter.ReportIds) > 0 {
			repoFilter.ReportIDs = filter.ReportIds
		}
	}

	return repoFilter
}

func (l *Logic) SendEmailMessage(
	ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	response, err := l.sendEmailMessage(ctx, summaryInfo, req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (l *Logic) sendEmailMessage(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 获取 report
	reportPB := summaryInfo.GetFulfillmentReport()
	logicReport := ConvertFulfillmentReportToReportLogic(reportPB)

	// 查询历史发送记录
	sendRecords, err := l.ListSendReportRecords(ctx, &fulfillmentpb.ListSendReportRecordsRequest{
		CompanyId:  lo.ToPtr(reportPB.GetCompanyId()),
		BusinessId: lo.ToPtr(reportPB.GetBusinessId()),
		Filter: &fulfillmentpb.ListSendReportRecordsFilter{
			ReportIds:  []int64{reportPB.GetId()},
			SendMethod: lo.ToPtr(fulfillmentpb.SendMethod_EMAIL),
		},
	})
	if err != nil {
		return nil, err
	}

	// 初始化或获取发送记录
	var sendRecord *sendrecordrepo.SendRecord
	if len(sendRecords.GetSendRecords()) == 0 {
		// 创建新的发送记录
		sendRecord = &sendrecordrepo.SendRecord{
			ReportID:      reportPB.GetId(),
			CompanyID:     reportPB.GetCompanyId(),
			BusinessID:    reportPB.GetBusinessId(),
			AppointmentID: reportPB.GetAppointmentId(),
			PetID:         reportPB.GetPetId(),
			SendMethod:    int32(fulfillmentpb.SendMethod_EMAIL),
			SentBy:        req.GetStaffId(),
		}
	} else {
		// 使用现有记录
		existingRecord := sendRecords.GetSendRecords()[0]
		sendRecord = &sendrecordrepo.SendRecord{
			ID:            existingRecord.RecordId,
			ReportID:      reportPB.GetId(),
			CompanyID:     reportPB.GetCompanyId(),
			BusinessID:    reportPB.GetBusinessId(),
			AppointmentID: reportPB.GetAppointmentId(),
			PetID:         reportPB.GetPetId(),
			SendMethod:    int32(fulfillmentpb.SendMethod_EMAIL),
			SentBy:        req.GetStaffId(),
		}
	}

	// 设置发送时间和内容
	sendRecord.SentTime = time.Now().UTC()
	contentJSON, _ := json.Marshal(logicReport.Content)
	sendRecord.ContentJSON = string(contentJSON)

	// 发送邮件
	sendResult := false
	var errorMessage string

	// 构建邮件主题
	emailSubject, err := l.BuildEmailSubject(req.GetEmailSubject(), summaryInfo)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to build email subject: %v", err)
	}

	// 构建sendMessages
	sendMessages := &message.SendMessageByEmailParams{
		BusinessID:         reportPB.GetBusinessId(),
		CompanyID:          reportPB.GetCompanyId(),
		ID:                 reportPB.GetId(),
		StaffID:            req.StaffId,
		RecipientEmailList: req.RecipientEmails,
		Subject:            emailSubject,
	}

	// 调用发送服务
	careType := summaryInfo.FulfillmentReport.GetCareType()
	var result *message.SendMessageByEmailResult
	if careType == offeringpb.CareCategory_GROOMING {
		result, err = l.messageRepo.SendGroomingReportMessageByEmail(ctx, sendMessages)
	} else {
		result, err = l.messageRepo.SendDailyReportMessageByEmail(ctx, sendMessages)
	}
	if err != nil {
		errorMessage = defaultSendSingleFailed
		sendResult = false
	} else {
		sendResult = result.SendSucceed
		errorMessage = result.ErrorMessage
	}

	// 更新发送记录
	sendRecord.IsSentSuccess = &sendResult
	sendRecord.ErrorMessage = &errorMessage

	// 保存或更新发送记录
	if len(sendRecords.GetSendRecords()) == 0 {
		err = l.sendRecordRepo.Create(ctx, sendRecord)
	} else {
		err = l.sendRecordRepo.Update(ctx, sendRecord)
	}
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save send record: %v", err)
	}

	// 更新报告状态为已发送
	if sendResult {
		_, err = l.updateReport(ctx, ConvertFulfillmentReportToReportLogic(reportPB),
			&Report{Status: fulfillmentpb.ReportStatus_SENT})
		if err != nil {
			log.ErrorContextf(ctx, "failed to update report status: %v", err)
		}
	}

	return &fulfillmentpb.SendFulfillmentReportResponse{
		SendResult: &fulfillmentpb.FulfillmentReportSendResult{
			FulfillmentReportId: reportPB.GetId(),
			SendMethod:          fulfillmentpb.SendMethod_EMAIL,
			IsSentSuccess:       sendResult,
			ErrorMessage:        errorMessage,
		},
	}, nil
}

// IncreaseFulfillmentOpenedCount 增加履约报告打开次数
func (l *Logic) IncreaseFulfillmentOpenedCount(ctx context.Context,
	req *fulfillmentpb.IncreaseFulfillmentOpenedCountRequest) (
	*fulfillmentpb.IncreaseFulfillmentOpenedCountResponse, error) {

	// 参数验证
	if req == nil || req.GetUuid() == "" {
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	uuid := req.GetUuid()

	// share 模式暂时不统计 opened count
	if strings.HasPrefix(uuid, ShareUUIDPrefix) {
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	// 增加打开次数
	err := l.reportRepo.IncreaseOpenedCount(ctx, uuid)
	if err != nil {
		log.ErrorContextf(ctx, "failed to increase opened count for uuid %s: %v", uuid, err)
		// 不返回错误，避免影响用户体验
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
}

// verifyBatchSendFulfillmentReportRequest 验证批量发送履约报告请求
func (l *Logic) verifyBatchSendFulfillmentReportRequest(req *fulfillmentpb.BatchSendFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}

	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}

	if len(req.GetReportIds()) == 0 {
		return status.Errorf(codes.InvalidArgument, "report_ids cannot be empty")
	}

	// 限制批量发送的数量，避免过载
	const maxBatchSize = 100
	if len(req.GetReportIds()) > maxBatchSize {
		return status.Errorf(codes.InvalidArgument, "report_ids count cannot exceed %d", maxBatchSize)
	}

	if req.GetSendMethod() == fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED {
		return status.Errorf(codes.InvalidArgument, "send_method is required")
	}

	if req.GetStaffId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "staff_id must be greater than 0")
	}

	return nil
}

// BatchSendFulfillmentReport 批量发送履约报告
func (l *Logic) BatchSendFulfillmentReport(ctx context.Context, req *fulfillmentpb.BatchSendFulfillmentReportRequest) (
	*fulfillmentpb.BatchSendFulfillmentReportResponse, error) {
	log.InfoContextf(ctx, "BatchSendFulfillmentReport req:%+v", req)

	// 校验请求
	if err := l.verifyBatchSendFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 批量获取报告信息
	reports, errorMessages, err := l.batchGetReports(ctx, req.GetReportIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to batch get reports: %v", err)
	}

	// 并发发送报告
	sendResults := l.batchSendReports(ctx, reports, req)

	// 为找不到的报告添加失败结果
	for _, reportID := range req.GetReportIds() {
		if errorMsg, exists := errorMessages[reportID]; exists {
			failedResult := &fulfillmentpb.FulfillmentReportSendResult{
				FulfillmentReportId: reportID,
				SendMethod:          req.GetSendMethod(),
				IsSentSuccess:       false,
				ErrorMessage:        errorMsg,
			}
			sendResults = append(sendResults, failedResult)
		}
	}

	// 构建 batch send status
	batchSendState := l.buildBatchSendState(sendResults)

	return &fulfillmentpb.BatchSendFulfillmentReportResponse{
		SendResults:    sendResults,
		BatchSendState: batchSendState,
	}, nil
}

// batchSendReports 批量发送报告
func (l *Logic) batchSendReports(ctx context.Context,
	reports []*reportrepo.Report,
	req *fulfillmentpb.BatchSendFulfillmentReportRequest) []*fulfillmentpb.FulfillmentReportSendResult {
	sendResults := make([]*fulfillmentpb.FulfillmentReportSendResult, 0, len(reports))

	// 使用 goroutine 并发发送，提高效率
	type sendResult struct {
		reportID int64
		result   *fulfillmentpb.FulfillmentReportSendResult
	}

	resultChan := make(chan sendResult, len(reports))

	// 限制并发数量，避免过载
	semaphore := make(chan struct{}, maxSendReportConcurrency)

	for _, report := range reports {
		go func(report *reportrepo.Report) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			result := l.sendSingleReport(ctx, report, req)
			resultChan <- sendResult{
				reportID: report.ID,
				result:   result,
			}
		}(report)
	}

	// 收集结果
	resultMap := make(map[int64]*fulfillmentpb.FulfillmentReportSendResult)
	for i := 0; i < len(reports); i++ {
		result := <-resultChan
		resultMap[result.reportID] = result.result
	}

	// 按原始顺序返回结果
	for _, report := range reports {
		if result, exists := resultMap[report.ID]; exists {
			sendResults = append(sendResults, result)
		}
	}

	return sendResults
}

// sendSingleReport 发送单个报告
func (l *Logic) sendSingleReport(ctx context.Context, report *reportrepo.Report,
	req *fulfillmentpb.BatchSendFulfillmentReportRequest) *fulfillmentpb.FulfillmentReportSendResult {
	// 构建发送结果，默认为失败
	sendResult := &fulfillmentpb.FulfillmentReportSendResult{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		IsSentSuccess:       false,
		ErrorMessage:        "",
	}

	// 获取完整的报告信息
	logicReport, err := l.GetFulfillmentReport(ctx, &GetFulfillmentReport{
		ID:            report.ID,
		CompanyID:     report.CompanyID,
		BusinessID:    report.BusinessID,
		AppointmentID: report.AppointmentID,
		PetID:         report.PetID,
		CareType:      offeringpb.CareCategory(report.CareType),
		ServiceDate:   report.ServiceDate,
		UUID:          report.UUID,
	})
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report: %v", err)

		return sendResult
	}

	// 获取报告摘要信息
	reportSummaryInfo, err := l.GetReportSummaryInfo(ctx, logicReport)
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report summary info: %v", err)

		return sendResult
	}

	// 构建单个发送请求
	sendReq := &fulfillmentpb.SendFulfillmentReportRequest{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		StaffId:             req.GetStaffId(),
	}

	// 根据发送方法发送报告
	var sendResponse *fulfillmentpb.SendFulfillmentReportResponse
	switch req.GetSendMethod() {
	case fulfillmentpb.SendMethod_EMAIL:
		sendResponse, err = l.SendEmailMessage(ctx, reportSummaryInfo, sendReq)
	case fulfillmentpb.SendMethod_SMS:
		sendResponse, err = l.SendSmsMessage(ctx, reportSummaryInfo, sendReq)
	default:
		sendResult.ErrorMessage = "unsupported send method"

		return sendResult
	}

	if err != nil {
		sendResult.ErrorMessage = err.Error()

		return sendResult
	}

	// 更新发送结果
	if sendResponse != nil && sendResponse.SendResult != nil {
		sendResult.IsSentSuccess = sendResponse.SendResult.GetIsSentSuccess()
		sendResult.ErrorMessage = sendResponse.SendResult.GetErrorMessage()
	} else {
		sendResult.IsSentSuccess = true // 如果没有错误，认为发送成功
	}

	return sendResult
}

// buildBatchSendState 根据发送结果构建批量发送状态
func (l *Logic) buildBatchSendState(
	sendResults []*fulfillmentpb.FulfillmentReportSendResult) fulfillmentpb.BatchSendState {
	if len(sendResults) == 0 {
		return fulfillmentpb.BatchSendState_BATCH_SEND_STATE_UNSPECIFIED
	}

	successCount := 0
	totalCount := len(sendResults)

	// 统计成功的发送数量
	for _, result := range sendResults {
		if result.GetIsSentSuccess() {
			successCount++
		}
	}

	// 根据成功率判断批量发送状态
	switch successCount {
	case totalCount:
		// 全部成功
		return fulfillmentpb.BatchSendState_ALL_SUCCESS
	case 0:
		// 全部失败
		return fulfillmentpb.BatchSendState_ALL_FAILED
	default:
		// 部分失败
		return fulfillmentpb.BatchSendState_PARTIAL_FAILED
	}
}

func (l *Logic) BuildEmailSubject(subject string,
	summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) (string, error) {
	// 检查是否为重发
	isResend := summaryInfo.GetFulfillmentReport().GetStatus() == fulfillmentpb.ReportStatus_SENT

	// 判断 subject 是否为空
	if subject == "" {
		// 设置默认主题模板
		if isResend {
			subject = "[Updated] {PetName}'s {Title} at {BusinessName}"
		} else {
			subject = "{PetName}'s {CareType} Report at {BusinessName}"
		}
	}

	// 获取业务信息
	businessName := summaryInfo.GetBusinessInfo().GetBusinessName()
	petName := summaryInfo.GetPetInfo().GetPetName()

	// 获取模板标题
	title := summaryInfo.GetFulfillmentReport().GetTemplate().GetTitle()
	if title == "" {
		title = getDefaultTemplateTitle(summaryInfo.GetFulfillmentReport().GetCareType())
	}

	// 获取主要员工信息
	staffNames := l.getMainStaffNames(summaryInfo)
	mainStaff := strings.Join(staffNames, ", ")

	// 替换占位符
	careTypeStr := "daily"
	if summaryInfo.GetFulfillmentReport().GetCareType() == offeringpb.CareCategory_GROOMING {
		careTypeStr = "grooming"
	}
	subject = strings.ReplaceAll(subject, "{BusinessName}", businessName)
	subject = strings.ReplaceAll(subject, "{PetName}", petName)
	subject = strings.ReplaceAll(subject, "{Title}", title)
	subject = strings.ReplaceAll(subject, "{MainStaff}", mainStaff)
	subject = strings.ReplaceAll(subject, "{CareType}", careTypeStr)

	return subject, nil
}

func (l *Logic) BuildSmsSendContent(summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) (string, error) {
	// 获取业务信息
	businessInfo := summaryInfo.GetBusinessInfo()
	petInfo := summaryInfo.GetPetInfo()
	careType := summaryInfo.GetFulfillmentReport().GetCareType()

	// 生成直接访问 url
	reportClientURL, err := l.getReportClientURL(careType)
	if err != nil {
		return "", err
	}
	directLink := fmt.Sprintf(reportClientURL, summaryInfo.GetFulfillmentReport().GetUuid())

	// 检查是否为重发
	isResend := summaryInfo.GetFulfillmentReport().GetStatus() == fulfillmentpb.ReportStatus_SENT

	// 获取模板标题
	title := summaryInfo.GetFulfillmentReport().GetTemplate().GetTitle()
	if title == "" {
		title = getDefaultTemplateTitle(summaryInfo.GetFulfillmentReport().GetCareType())
	}

	// 设置短信模板
	var sendContent string
	if isResend {
		sendContent = "[Updated] {PetName}’s {Title} at {BusinessName} {DirectAccessLink}"
	} else {
		sendContent = "{PetName}’s {Title} at {BusinessName} {DirectAccessLink}"
	}

	// 获取主要员工信息
	staffNames := l.getMainStaffNames(summaryInfo)
	mainStaff := strings.Join(staffNames, ", ")

	// 替换占位符
	sendContent = strings.ReplaceAll(sendContent, "{BusinessName}", businessInfo.GetBusinessName())
	sendContent = strings.ReplaceAll(sendContent, "{PetName}", petInfo.GetPetName())
	sendContent = strings.ReplaceAll(sendContent, "{Title}", title)
	sendContent = strings.ReplaceAll(sendContent, "{MainStaff}", mainStaff)
	sendContent = strings.ReplaceAll(sendContent, "{DirectAccessLink}", directLink)

	return sendContent, nil
}

func (l *Logic) getReportClientURL(careType offeringpb.CareCategory) (string, error) {
	cfg := config.GetCfg()

	if careType == offeringpb.CareCategory_GROOMING {
		if cfg.ReportClient != nil && cfg.ReportClient.GroomingReportURL != "" {
			return cfg.ReportClient.GroomingReportURL, nil
		}

		return "", status.Errorf(codes.Internal, "grooming report url is not configured")
	}

	if cfg.ReportClient != nil && cfg.ReportClient.DailyReportURL != "" {
		return cfg.ReportClient.DailyReportURL, nil
	}

	return "", status.Errorf(codes.Internal, "daily report url is not configured")
}

// getMainStaffNames 获取主要员工姓名列表
func (l *Logic) getMainStaffNames(summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) []string {
	var staffNames []string
	if appointmentInfo := summaryInfo.GetAppointmentInfo(); appointmentInfo != nil {
		for _, petService := range appointmentInfo.GetPetService() {
			for _, petDetail := range petService.GetPetDetails() {
				if staffInfo := petDetail.GetStaffInfo(); staffInfo != nil {
					firstName := staffInfo.GetStaffFirstName()
					if firstName != "" {
						staffNames = append(staffNames, firstName)
					}
				}
			}
		}
	}

	return staffNames
}
