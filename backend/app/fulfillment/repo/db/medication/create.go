package medication

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) BatchCreate(ctx context.Context, medications []*AppointmentPetMedication) error {
	if len(medications) == 0 {
		return nil
	}
	// 使用事务进行批量插入
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(medications, len(medications)).Error; err != nil {
			log.ErrorContextf(ctx, "BatchCreateAppointmentPetMedication err, err:%+v", err)

			return err
		}

		return nil
	})
}

func (i *impl) Create(ctx context.Context, medication *AppointmentPetMedication) error {
	return i.db.WithContext(ctx).Create(medication).Error
}

func (i *impl) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, medications []*AppointmentPetMedication) error {
	if len(medications) == 0 {
		return nil
	}

	if err := tx.CreateInBatches(medications, len(medications)).Error; err != nil {
		log.ErrorContextf(ctx, "BatchCreateAppointmentPetMedication err, err:%+v", err)

		return err
	}

	return nil
}
