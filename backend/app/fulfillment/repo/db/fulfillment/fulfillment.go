package fulfillment

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

const (
	defaultLimit  = 200
	defaultOffset = 0
)

type ReadWriter interface {
	GetByServiceInstanceID(ctx context.Context, serviceInstanceID int64) ([]*Fulfillment, error)
	GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*Fulfillment, error)
	List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Fulfillment, error)
	Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error)
	BatchCreate(ctx context.Context, fulfillments []*Fulfillment) error
	Update(ctx context.Context, fulfillment *Fulfillment) error
	DeleteByServiceInstanceID(ctx context.Context, serviceInstanceID int64) error
}

type impl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func New() ReadWriter {
	database := db.GetDB()

	return &impl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}
