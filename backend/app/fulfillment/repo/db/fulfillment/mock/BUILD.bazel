load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["fulfillment_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db/fulfillment",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
