package serviceinstance

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) Create(ctx context.Context, si *ServiceInstance) (int, error) {
	if si == nil {
		return 0, nil
	}
	err := i.db.WithContext(ctx).Create(si).Error
	if err != nil {
		return 0, err
	}

	return si.ID, nil
}

func (i *impl) BatchCreate(ctx context.Context, serviceInstances []*ServiceInstance) error {
	if len(serviceInstances) == 0 {
		return nil
	}
	// 使用事务进行批量插入
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(serviceInstances, len(serviceInstances)).Error; err != nil {
			log.ErrorContextf(ctx, "BatchCreateServiceInstance err, err:%+v", err)

			return err
		}

		return nil
	})
}

func (i *impl) CreateWithTx(ctx context.Context, tx *gorm.DB, si *ServiceInstance) (int, error) {
	if si == nil {
		return 0, nil
	}
	err := tx.WithContext(ctx).Create(si).Error
	if err != nil {
		return 0, err
	}

	return si.ID, nil
}
