load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["service_instance_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
