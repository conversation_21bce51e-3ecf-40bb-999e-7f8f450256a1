server:
  filter:
    - opentelemetry
    - recovery
    - debuglog
    - validation
  service:
    - name: backend.proto.customer.v1.CustomerService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.customer.v1.CustomerQueryService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.customer.v2.MetadataService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.customer.v2.ActivityService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.customer.v2.DataMigrationService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: moego.crm.engagement.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=moego.crm.engagement.testing&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.crm.message.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=moego.crm.message.testing&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.erp.appointment.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=moego.erp.appointment.testing&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.erp.online_booking.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=moego.erp.online_booking.testing&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.order.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=moego.order.testing&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_life_cycle.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_life_cycle.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_action_state.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_action_state.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_history_log.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_history_log.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_task.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_task.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_note.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_note.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_tag.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_tag.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_tag_binding.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_tag_binding.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_source.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_source.testing&group=moego-customer-cdc&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_business_customer.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_business_customer.testing&group=moego-customer-cdc-testing&maxRetry=3&initial=oldest
      protocol: kafka
      timeout: 5000
    - name: cdc.mysql.moe_customer.moe_customer_contact.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_contact.testing&group=moego-customer-cdc-testing&maxRetry=3&initial=oldest
      timeout: 5000
      protocol: kafka
    - name: cdc.mysql.moe_customer.moe_customer_address.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=cdc.mysql.moe_customer.moe_customer_address.testing&group=moego-customer-cdc-testing&maxRetry=3&initial=oldest
      timeout: 5000
      protocol: kafka
    - name: moego.crm.customer.merge.consumer
      address: b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?topics=moego.crm.customer.merge.testing&group=moego-customer&maxRetry=3
      timeout: 5000
      protocol: kafka
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: moego-svc-sms
      target: dns://moego-svc-sms:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: mysql.moe_customer
      target: dsn://moego_developer_240310_eff7a0dc:G0MxI7NM_jX_f7Ky73vnrwej97xg1tly@tcp(*************:40106)/moe_customer?charset=utf8mb4&parseTime=true&timeout=30s
      protocol: gorm
      transport: gorm
    - callee: postgres.moego_customer
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40133/moego_customer?sslmode=disable
      protocol: gorm
      transport: gorm
    - callee: moego-search
      target: dns://moego-search:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-pet
      target: dns://moego-pet:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego.crm.customer.producer
      target: kafka://b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?clientid=moego&partitioner=hash&topic=moego.crm.customer.testing
      timeout: 10000
    - callee: moego-svc-appointment
      target: dns://moego-svc-appointment:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-membership
      target: dns://moego-svc-membership:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-order
      target: dns://moego-svc-order:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-payment
      target: dns://moego-svc-payment:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-service-grooming
      target: http://moego-service-grooming:9206
      protocol: http
    - callee: moego.crm.customer.merge.producer
      target: kafka://b-3.kafkatesting.9myagv.c10.kafka.us-west-2.amazonaws.com:9098?clientid=moego&partitioner=hash&topic=moego.crm.customer.merge.testing
      timeout: 10000
    - callee: moego-svc-message-v2
      target: dns://moego-svc-message-v2:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-account
      target: dns://moego-svc-account:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-file
      target: http://moego-file:9000
      protocol: http
      network: tcp
      transport: http
    - callee: moego.redis
      target: redis://redis.t2.moego.dev:40179/0?min_idle_conns=10&tls=true
      password: iMoReGoTdesstingeCache250310_7fec987d
      timeout: 10000
plugins:
  auth:
    validation:
      enable_error_log: false
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: mysql.moego_customer
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
    kafka:
      credential:
        region: us-west-2
        access_key_id: ********************
        secret_access_key: xp6kQXqxwEYqAPU6ukFEci7Zm4nuOPM/fq8a2+cg
  config:
    growth_book:
      host: "https://growthbook.moego.pet/growthbook-api"
      client_key: "sdk-QF3sU88BEtcqVhr"
      timeout: 5s
