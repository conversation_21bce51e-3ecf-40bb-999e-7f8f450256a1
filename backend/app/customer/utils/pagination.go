package customerutils

import (
	"strconv"
	"strings"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
)

// PaginationHelper 分页助手，处理page token的转换
type PaginationHelper struct{}

// NewPaginationHelper 创建新的分页助手实例
func NewPaginationHelper() *PaginationHelper {
	return &PaginationHelper{}
}

// ProcessPageToken 处理page token，支持cursor和page number两种模式
// 如果pageToken是数字字符串，则转换为page number模式
// 如果pageToken是base64编码的cursor，则使用cursor模式
func (p *PaginationHelper) ProcessPageToken(pageToken string, pageSize int32) (*PaginationInfo, error) {
	if pageToken == "" {
		return &PaginationInfo{
			UsePageNumber:     false,
			PageNumber:        0,
			Cursor:            nil,
			Offset:            0,
			ShouldReturnTotal: false,
		}, nil
	}

	// 尝试解析为数字（page number模式）
	if pageNum, err := strconv.ParseInt(strings.TrimSpace(pageToken), 10, 32); err == nil && pageNum > 0 {
		// 这是page number模式
		offset := (pageNum - 1) * int64(pageSize)

		return &PaginationInfo{
			UsePageNumber:     true,
			PageNumber:        int32(pageNum),
			Cursor:            nil,
			Offset:            int32(offset),
			ShouldReturnTotal: true, // page number模式自动返回总数
		}, nil
	}

	// 尝试解析为cursor模式
	cursor := DecodeCursor(pageToken)

	return &PaginationInfo{
		UsePageNumber:     false,
		PageNumber:        0,
		Cursor:            cursor,
		Offset:            0,
		ShouldReturnTotal: false, // cursor模式不自动返回总数
	}, nil
}

// GenerateNextToken 生成下一页的token
// 如果使用page number模式，返回下一页页码
// 如果使用cursor模式，返回编码后的cursor
func (p *PaginationHelper) GenerateNextToken(info *PaginationInfo, hasNext bool, lastCursor *postgres.Cursor) string {
	if !hasNext {
		return ""
	}

	if info.UsePageNumber {
		// page number模式，返回下一页页码
		return strconv.Itoa(int(info.PageNumber + 1))
	}

	// cursor模式，返回编码后的cursor
	if lastCursor != nil {
		return lastCursor.EncodeCursor()
	}

	return ""
}

// PaginationInfo 包含分页信息的结构体
type PaginationInfo struct {
	UsePageNumber     bool             // 是否使用page number模式
	PageNumber        int32            // 页码（从1开始）
	Cursor            *postgres.Cursor // cursor信息
	Offset            int32            // 偏移量（用于page number模式）
	ShouldReturnTotal bool             // 是否应该返回总数（page number模式时自动设置为true）
}

// IsPageNumberMode 判断是否为page number模式
func (info *PaginationInfo) IsPageNumberMode() bool {
	return info.UsePageNumber
}

// IsCursorMode 判断是否为cursor模式
func (info *PaginationInfo) IsCursorMode() bool {
	return !info.UsePageNumber
}

// ShouldReturnTotalSize 判断是否应该返回总数
// page number模式下自动返回true，cursor模式下需要明确设置
func (info *PaginationInfo) ShouldReturnTotalSize(explicitRequest bool) bool {
	if info.UsePageNumber {
		return true // page number模式自动返回总数
	}

	return explicitRequest // cursor模式下根据明确请求决定
}
