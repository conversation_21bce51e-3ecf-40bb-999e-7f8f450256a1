package activityrelation

import (
	"time"
)

// Type 字段枚举类型
//
//revive:disable-next-line exported
type Type string

const (
	TypeTag Type = "TAG"
)

// ActivityRelation GORM entity
type ActivityRelation struct {
	ID           int64      `gorm:"column:id;primary_key"`
	CompanyID    int64      `gorm:"column:company_id"`
	BusinessID   int64      `gorm:"column:business_id"`
	CustomerID   int64      `gorm:"column:customer_id"`
	ActivityID   int64      `gorm:"column:activity_id"`
	ActivityType Type       `gorm:"column:activity_type"`
	CreateBy     int64      `gorm:"column:create_by"`
	UpdateBy     int64      `gorm:"column:update_by"`
	DeleteBy     *int64     `gorm:"column:delete_by"`
	CreateTime   time.Time  `gorm:"column:create_time"`
	UpdateTime   time.Time  `gorm:"column:update_time"`
	DeleteTime   *time.Time `gorm:"column:delete_time"`
}

// TableName sets the insert table name for this struct type
func (m *ActivityRelation) TableName() string {
	return "activity_relation"
}
