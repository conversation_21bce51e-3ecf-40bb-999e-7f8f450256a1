// Code generated by MockGen. DO NOT EDIT.
// Source: ./activity_relation.go
//
// Generated by this command:
//
//	mockgen -source=./activity_relation.go -destination=./mock/activity_relation_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	activityrelation "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_relation"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, rel *activityrelation.ActivityRelation) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, rel)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, rel any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, rel)
}

// CreateBatch mocks base method.
func (m *MockReadWriter) CreateBatch(ctx context.Context, rel []*activityrelation.ActivityRelation) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBatch", ctx, rel)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBatch indicates an expected call of CreateBatch.
func (mr *MockReadWriterMockRecorder) CreateBatch(ctx, rel any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBatch", reflect.TypeOf((*MockReadWriter)(nil).CreateBatch), ctx, rel)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id, staffID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id, staffID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id, staffID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id, staffID)
}

// DeleteCustomerActivityRel mocks base method.
func (m *MockReadWriter) DeleteCustomerActivityRel(ctx context.Context, datum *activityrelation.DeleteCustomerActivityRel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCustomerActivityRel", ctx, datum)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCustomerActivityRel indicates an expected call of DeleteCustomerActivityRel.
func (mr *MockReadWriterMockRecorder) DeleteCustomerActivityRel(ctx, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCustomerActivityRel", reflect.TypeOf((*MockReadWriter)(nil).DeleteCustomerActivityRel), ctx, datum)
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, datum *activityrelation.ListActivityRelDatum) ([]*activityrelation.ActivityRelation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, datum)
	ret0, _ := ret[0].([]*activityrelation.ActivityRelation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, datum)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, rel *activityrelation.ActivityRelation) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, rel)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, rel any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, rel)
}

// UpsertBatch mocks base method.
func (m *MockReadWriter) UpsertBatch(ctx context.Context, rel []*activityrelation.ActivityRelation) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBatch", ctx, rel)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertBatch indicates an expected call of UpsertBatch.
func (mr *MockReadWriterMockRecorder) UpsertBatch(ctx, rel any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBatch", reflect.TypeOf((*MockReadWriter)(nil).UpsertBatch), ctx, rel)
}

// WithTransaction mocks base method.
func (m *MockReadWriter) WithTransaction(arg0 context.Context, arg1 func(activityrelation.ReadWriter) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTransaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithTransaction indicates an expected call of WithTransaction.
func (mr *MockReadWriterMockRecorder) WithTransaction(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTransaction", reflect.TypeOf((*MockReadWriter)(nil).WithTransaction), arg0, arg1)
}
