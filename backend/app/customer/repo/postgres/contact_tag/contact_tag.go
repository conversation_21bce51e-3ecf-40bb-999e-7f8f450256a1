package contacttag

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Repository interface {
	Create(ctx context.Context, contactTag *ContactTag) (*ContactTag, error)
	Get(ctx context.Context, id int64) (*ContactTag, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)
	Update(ctx context.Context, contactTag *ContactTag) (*ContactTag, error)
	Delete(ctx context.Context, id int64) error
	WithTx(tx *gorm.DB) Repository
}

func (i *impl) WithTx(tx *gorm.DB) Repository {
	return &impl{db: tx}
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, contactTag *ContactTag) (*ContactTag, error) {
	err := i.db.WithContext(ctx).Create(contactTag).Error
	if err != nil {
		return nil, err
	}

	return contactTag, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*ContactTag, error) {
	var contactTag ContactTag
	err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("state = ?", customerpb.ContactTag_ACTIVE.String()).
		First(&contactTag).Error
	if err != nil {
		return nil, err
	}

	return &contactTag, nil
}

func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&ContactTag{})
	query = i.applyFilter(query, filter)

	// 如果使用offset分页，则不应用cursor
	if pagination.Offset != nil && *pagination.Offset > 0 {
		query = query.Offset(int(*pagination.Offset))
	} else {
		query = i.applyCursor(query, pagination.Cursor, orderBy)
	}

	query = i.applyOrderBy(query, orderBy)

	// 当 PageSize=0 时不限制数量，否则多查一条用于判断是否有下一页
	if pagination.PageSize > 0 {
		query = query.Limit(int(pagination.PageSize + 1))
	}

	var tags []*ContactTag
	if err := query.Find(&tags).Error; err != nil {
		return nil, err
	}

	hasNext := len(tags) > int(pagination.PageSize)
	if hasNext {
		tags = tags[:pagination.PageSize]
	}

	return &CursorResult{
		Data:       tags,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if filter.IDs != nil {
		query = query.Where("id IN (?)", filter.IDs)
	}

	if len(filter.Organizations) > 0 {
		var orgConditions [][]any
		for _, org := range filter.Organizations {
			orgConditions = append(orgConditions, []any{org.Id, org.Type.String()})
		}
		query = query.Where("(organization_id, organization_type) IN ?", orgConditions)
	}

	if len(filter.Types) > 0 {
		types := make([]string, 0, len(filter.Types))
		for _, t := range filter.Types {
			types = append(types, t.String())
		}
		query = query.Where("type IN (?)", types)
	}

	if filter.Name != "" {
		query = query.Where("name LIKE ?", "%"+filter.Name+"%")
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == 2 { // DESC
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {
		query = query.
			Order(orderBy.Field.String() + " " + orderBy.Direction.String() + ", id " + orderBy.Direction.String())
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&ContactTag{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *impl) Update(ctx context.Context, contactTag *ContactTag) (*ContactTag, error) {
	err := i.db.WithContext(ctx).Updates(contactTag).Error
	if err != nil {
		return nil, err
	}

	return contactTag, nil
}

func (i *impl) Delete(ctx context.Context, id int64) error {
	now := time.Now().UTC()

	return i.db.WithContext(ctx).Model(&ContactTag{}).
		Where("id = ?", id).
		Updates(&ContactTag{
			State:       customerpb.ContactTag_DELETED,
			DeletedTime: &now,
		}).Error
}
