package contacttag

import (
	"time"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type ContactTag struct {
	ID               int64                           `gorm:"primaryKey;column:id"`
	OrganizationType customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	OrganizationID   int64                           `gorm:"column:organization_id;not null"`
	Name             string                          `gorm:"column:name;type:varchar(255);not null"`
	Color            string                          `gorm:"column:color;type:varchar(7);not null"`
	SortOrder        int32                           `gorm:"column:sort_order;not null;default:0"`
	Description      string                          `gorm:"column:description;type:text"`
	State            customerpb.ContactTag_State     `gorm:"column:state;serializer:proto_enum"`
	Type             customerpb.ContactTag_Type      `gorm:"column:type;serializer:proto_enum"`
	CreatedTime      time.Time                       `gorm:"column:created_time"`
	UpdatedTime      time.Time                       `gorm:"column:updated_time"`
	DeletedTime      *time.Time                      `gorm:"column:deleted_time"`
}

func (ContactTag) TableName() string {
	return "contact_tag"
}

type ListFilter struct {
	IDs           []int64
	Organizations []*customerpb.OrganizationRef
	Types         []customerpb.ContactTag_Type
	Name          string
	States        []customerpb.ContactTag_State
}

type Pagination struct {
	PageSize        int32
	Cursor          *postgres.Cursor
	ReturnTotalSize bool
	// 支持offset分页（深分页场景）
	Offset *int32
}

type OrderBy struct {
	Field     customerpb.ListContactTagsRequest_Sorting_Field
	Direction customerpb.Direction
}

// CursorResult 游标分页结果
type CursorResult struct {
	Data       []*ContactTag `json:"data"`     // 数据列表
	HasNext    bool          `json:"has_next"` // 是否有下一页
	TotalCount *int64        `json:"total_count"`
}
