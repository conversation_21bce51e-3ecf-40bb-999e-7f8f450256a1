load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_related_data",
    srcs = [
        "customer_related_data.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
