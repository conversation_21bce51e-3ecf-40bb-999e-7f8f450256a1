load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["contact_tag_relation_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_relation/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/contact_tag_relation",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
