package contacttagrel

//go:generate mockgen -source=./contact_tag_relation.go -destination=./mock/contact_tag_relation_mock.go -package=mock

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
)

type Repository interface {
	Create(ctx context.Context, contactTagRel *ContactTagRelation) (*ContactTagRelation, error)
	Save(ctx context.Context, contactTagRel *ContactTagRelation) (*ContactTagRelation, error)
	Get(ctx context.Context, contactID, tagID int64) (*ContactTagRelation, error)
	List(ctx context.Context, filter *ListFilter) ([]*ContactTagRelation, error)
	ListWithLock(ctx context.Context, filter *ListFilter) ([]*ContactTagRelation, error)
	Delete(ctx context.Context, contactID, tagID int64) error
	WithTx(tx *gorm.DB) Repository
}

func (r *impl) WithTx(tx *gorm.DB) Repository {
	return &impl{db: tx}
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (r *impl) Create(ctx context.Context, contactTagRel *ContactTagRelation) (*ContactTagRelation, error) {
	err := r.db.WithContext(ctx).Create(contactTagRel).Error
	if err != nil {
		return nil, err
	}

	return contactTagRel, nil
}

func (r *impl) Save(ctx context.Context, contactTagRel *ContactTagRelation) (*ContactTagRelation, error) {
	err := r.db.WithContext(ctx).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Save(contactTagRel).Error
	if err != nil {
		return nil, err
	}

	return contactTagRel, nil
}

func (r *impl) Get(ctx context.Context, contactID, tagID int64) (*ContactTagRelation, error) {
	var rel ContactTagRelation
	err := r.db.WithContext(ctx).
		Where("contact_id = ? AND tag_id = ? AND deleted_time IS NULL", contactID, tagID).
		First(&rel).Error
	if err != nil {
		return nil, err
	}

	return &rel, nil
}

func (r *impl) List(ctx context.Context, filter *ListFilter) ([]*ContactTagRelation, error) {
	var rels []*ContactTagRelation
	db := r.db.WithContext(ctx).Where("deleted_time IS NULL")
	if filter.ContactID != 0 {
		db = db.Where("contact_id = ?", filter.ContactID)
	}
	if filter.TagID != 0 {
		db = db.Where("tag_id = ?", filter.TagID)
	}
	err := db.Find(&rels).Error
	if err != nil {
		return nil, err
	}

	return rels, nil
}

func (r *impl) ListWithLock(ctx context.Context, filter *ListFilter) ([]*ContactTagRelation, error) {
	var rels []*ContactTagRelation
	db := r.db.WithContext(ctx).Where("deleted_time IS NULL")
	if filter.ContactID != 0 {
		db = db.Where("contact_id = ?", filter.ContactID)
	}
	if filter.TagID != 0 {
		db = db.Where("tag_id = ?", filter.TagID)
	}
	err := db.Clauses(clause.Locking{Strength: "UPDATE"}).Find(&rels).Error
	if err != nil {
		return nil, err
	}

	return rels, nil
}

func (r *impl) Delete(ctx context.Context, contactID, tagID int64) error {
	now := time.Now()

	return r.db.WithContext(ctx).
		Model(&ContactTagRelation{}).
		Where("contact_id = ? AND tag_id = ? AND deleted_time IS NULL", contactID, tagID).
		Update("deleted_time", now).Error
}
