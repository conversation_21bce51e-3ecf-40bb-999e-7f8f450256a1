package service

import (
	"context"
	"time"

	funk "github.com/thoas/go-funk"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/companysetting"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	customfieldlogic "github.com/MoeGolibrary/moego/backend/app/customer/logic/customfield"
	metadatalogic "github.com/MoeGolibrary/moego/backend/app/customer/logic/metadata"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type MetadataService struct {
	contactLogic             *contact.Logic
	addressLogic             *address.Logic
	contactTagLogic          *contacttag.Logic
	customFieldLogic         *customfieldlogic.Logic
	customerRelatedDataLogic *customerrelateddata.Logic
	metadataLogic            *metadatalogic.Logic
	companySettingLogic      *companysetting.Logic
	txManager                postgres.TransactionManager
	customerpb.UnimplementedMetadataServiceServer
}

func NewMetadataService() *MetadataService {
	return &MetadataService{
		contactLogic:             contact.New(),
		addressLogic:             address.New(),
		contactTagLogic:          contacttag.New(),
		customFieldLogic:         customfieldlogic.New(),
		customerRelatedDataLogic: customerrelateddata.New(),
		metadataLogic:            metadatalogic.New(),
		companySettingLogic:      companysetting.New(),
		txManager:                postgres.NewTxManager(),
	}
}

func (s *MetadataService) WithTx(tx *gorm.DB) *MetadataService {
	return &MetadataService{
		contactLogic:             s.contactLogic.WithTx(tx),
		addressLogic:             s.addressLogic.WithTx(tx),
		contactTagLogic:          s.contactTagLogic,
		customFieldLogic:         s.customFieldLogic,
		customerRelatedDataLogic: s.customerRelatedDataLogic.WithTx(tx),
		metadataLogic:            s.metadataLogic.WithTx(tx),
		companySettingLogic:      s.companySettingLogic.WithTx(tx),
		txManager:                s.txManager,
	}
}

func (s *MetadataService) CreateCustomer(ctx context.Context,
	req *customerpb.CreateCustomerRequest) (*customerpb.CreateCustomerResponse, error) {
	if req.GetOrganization() == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	metadata, err := s.metadataLogic.CreateMetadata(ctx, &metadatalogic.Metadata{
		OrganizationType: req.GetOrganization().GetType(),
		OrganizationID:   req.GetOrganization().GetId(),
		Type:             customerpb.CustomerType_CUSTOMER,
		GivenName:        req.GetGivenName(),
		FamilyName:       req.GetFamilyName(),
		CustomFields:     req.GetCustomFields(),
		LifeCycleID:      req.GetLifecycleId(),
		OwnerStaffID:     req.GetOwnerStaffId(),
		ActionStateID:    req.GetActionStateId(),
		AvatarPath:       req.GetAvatarPath(),
		ReferralSourceID: req.GetReferralSourceId(),
		CustomerCode:     random.String(16),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateCustomerResponse{
		Customer: s.metadataToCustomerPB(metadata),
	}, nil
}

func (s *MetadataService) GetCustomer(ctx context.Context,
	req *customerpb.GetCustomerRequest) (*customerpb.GetCustomerResponse, error) {
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 验证这是Customer类型的记录
	if metadata.Type != customerpb.CustomerType_CUSTOMER {
		return nil, errs.Newm(codes.NotFound, "customer not found")
	}

	return &customerpb.GetCustomerResponse{
		Customer: s.metadataToCustomerPB(metadata),
	}, nil
}

func (s *MetadataService) ListCustomers(ctx context.Context,
	req *customerpb.ListCustomersRequest) (*customerpb.ListCustomersResponse, error) {

	// 构建过滤器，添加nil检查
	filter := &metadatalogic.ListMetadataFilter{
		Type:              customerpb.CustomerType_CUSTOMER,
		LifecycleIDs:      req.Filter.LifecycleIds,
		OwnerStaffIDs:     req.Filter.OwnerStaffIds,
		ReferralSourceIDs: req.Filter.ReferralSourceIds,
	}

	// 转换状态
	if len(req.Filter.States) > 0 {
		metadataStates := make([]customerpb.Metadata_State, len(req.Filter.States))
		for i, state := range req.Filter.States {
			metadataStates[i] = customerpb.Metadata_State(state)
		}
		filter.States = metadataStates
	}

	if len(req.GetFilter().GetIds()) == 0 && len(req.GetFilter().GetOrganizations()) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "at least one of ids or organizations is required")
	}

	filter.Organizations = req.GetFilter().GetOrganizations()

	pagination := &metadatalogic.ListMetadataPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: false,
	}

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		pagination.ReturnTotalSize = true
		pagination.PageSize = 0
	} else if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &metadatalogic.ListMetadataOrderBy{
		Field:     customerpb.ListMetadataRequest_Sorting_ID,
		Direction: customerpb.Direction_ASC,
	}
	if req.Sorting != nil {
		orderBy.Field = customerpb.ListMetadataRequest_Sorting_Field(req.Sorting.Field)
		orderBy.Direction = customerpb.Direction(req.Sorting.Direction)
	}

	response, err := s.metadataLogic.ListMetadata(ctx, &metadatalogic.ListMetadataRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	customers := make([]*customerpb.Customer, 0, len(response.Metadata))
	for _, metadata := range response.Metadata {
		customers = append(customers, s.metadataToCustomerPB(metadata))
	}

	return &customerpb.ListCustomersResponse{
		Customers:     customers,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateCustomer(ctx context.Context,
	req *customerpb.UpdateCustomerRequest) (*customerpb.UpdateCustomerResponse, error) {
	// 验证 state 字段不能为 DELETED
	if req.State != nil && *req.State == customerpb.Customer_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	// 首先验证这是Customer类型的记录
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if metadata.Type != customerpb.CustomerType_CUSTOMER {
		return nil, errs.Newm(codes.NotFound, "customer not found")
	}

	updateReq := &metadatalogic.UpdateMetadataRequest{
		GivenName:        req.GetGivenName(),
		FamilyName:       req.GetFamilyName(),
		CustomFields:     req.GetCustomFields(),
		LifeCycleID:      req.GetLifecycleId(),
		OwnerStaffID:     req.GetOwnerStaffId(),
		ActionStateID:    req.GetActionStateId(),
		AvatarPath:       req.GetAvatarPath(),
		ReferralSourceID: req.GetReferralSourceId(),
	}
	if req.State != nil {
		updateReq.State = customerpb.Metadata_State(*req.State)
	}

	updatedMetadata, err := s.metadataLogic.UpdateMetadata(ctx, req.Id, updateReq)
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateCustomerResponse{
		Customer: s.metadataToCustomerPB(updatedMetadata),
	}, nil
}

func (s *MetadataService) DeleteCustomer(ctx context.Context,
	req *customerpb.DeleteCustomerRequest) (*customerpb.DeleteCustomerResponse, error) {
	// 首先验证这是Customer类型的记录
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if metadata.Type != customerpb.CustomerType_CUSTOMER {
		return nil, errs.Newm(codes.NotFound, "customer not found")
	}

	err = s.metadataLogic.DeleteMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteCustomerResponse{}, nil
}

func (s *MetadataService) CreateContact(ctx context.Context,
	req *customerpb.CreateContactRequest) (*customerpb.CreateContactResponse, error) {
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.CustomerId)
	if err != nil {
		return nil, err
	}

	contact := &contact.Contact{
		CustomerID: metadata.ID,
		IsSelf:     req.GetIsSelf(),
		GivenName:  req.GetGivenName(),
		FamilyName: req.GetFamilyName(),
		Email:      req.GetEmail(),
		Note:       req.GetNote(),
		Phone:      req.GetPhone().GetE164Number(),
	}

	for _, tag := range req.GetTags() {
		contact.Tags = append(contact.Tags, &contacttag.ContactTag{
			ID:               tag.GetId(),
			Name:             tag.GetName(),
			State:            tag.GetState(),
			Color:            tag.GetColor(),
			SortOrder:        tag.GetSortOrder(),
			Description:      tag.GetDescription(),
			Type:             tag.GetType(),
			OrganizationType: metadata.OrganizationType,
			OrganizationID:   metadata.OrganizationID,
		})
	}

	createdContact, err := s.contactLogic.Create(ctx, contact)
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateContactResponse{
		Contact: createdContact.ToPB(),
	}, nil
}

func (s *MetadataService) GetContact(ctx context.Context,
	req *customerpb.GetContactRequest) (*customerpb.GetContactResponse, error) {
	contact, err := s.contactLogic.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &customerpb.GetContactResponse{
		Contact: contact.ToPB(),
	}, nil
}

func (s *MetadataService) ListContacts(ctx context.Context,
	req *customerpb.ListContactsRequest) (*customerpb.ListContactsResponse, error) {

	// 构建过滤器，添加nil检查
	filter := &contact.ListContactsFilter{
		States:          req.GetFilter().GetStates(),
		IDs:             req.GetFilter().GetIds(),
		CustomerIDs:     req.GetFilter().GetCustomerIds(),
		Emails:          req.GetFilter().GetEmails(),
		OrganizationRef: nil,
		TagTypes:        req.GetFilter().GetTagTypes(),
	}
	for _, phone := range req.GetFilter().GetPhones() {
		if phone.GetE164Number() != "" {
			filter.Phones = append(filter.Phones, phone.GetE164Number())
		}
	}
	pagination := &contact.ListContactsPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: false,
	}

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		pagination.ReturnTotalSize = true
		pagination.PageSize = 0
	} else if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &contact.ListContactsOrderBy{
		Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.contactLogic.List(ctx, &contact.ListContactsRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	contacts := make([]*customerpb.Contact, 0, len(response.Contacts))
	for _, c := range response.Contacts {
		contacts = append(contacts, c.ToPB())
	}

	return &customerpb.ListContactsResponse{
		Contacts:      contacts,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateContact(ctx context.Context,
	req *customerpb.UpdateContactRequest) (*customerpb.UpdateContactResponse, error) {
	return s.updateContact(ctx, req, false)
}

func (s *MetadataService) updateContact(ctx context.Context,
	req *customerpb.UpdateContactRequest, isInTx bool) (*customerpb.
	UpdateContactResponse, error) {
	// 验证 state 字段不能为 DELETED
	if req.State != nil && *req.State == customerpb.Contact_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	updateReq := &contact.UpdateContactRequest{
		ID:         req.Id,
		GivenName:  req.GetGivenName(),
		FamilyName: req.GetFamilyName(),
		Email:      req.GetEmail(),
		Note:       req.GetNote(),
		State:      req.GetState(),
		Phone:      req.GetPhone().GetE164Number(),
		UpdateTags: req.GetUpdateTags(),
	}
	for _, tag := range req.GetTags() {
		updateReq.Tags = append(updateReq.Tags, &contacttag.ContactTag{
			ID:               tag.GetId(),
			Name:             tag.GetName(),
			State:            tag.GetState(),
			Color:            tag.GetColor(),
			SortOrder:        tag.GetSortOrder(),
			Description:      tag.GetDescription(),
			Type:             tag.GetType(),
			OrganizationID:   tag.GetOrganization().GetId(),
			OrganizationType: tag.GetOrganization().GetType(),
		})
	}

	contact, err := s.contactLogic.Update(ctx, updateReq, isInTx)
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateContactResponse{
		Contact: contact.ToPB(),
	}, nil
}

func (s *MetadataService) DeleteContact(ctx context.Context,
	req *customerpb.DeleteContactRequest) (*customerpb.DeleteContactResponse, error) {
	err := s.contactLogic.Delete(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteContactResponse{}, nil
}

func (s *MetadataService) CreateContactTag(ctx context.Context,
	req *customerpb.CreateContactTagRequest) (*customerpb.CreateContactTagResponse, error) {
	if req.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	logicContactTag, err := s.contactTagLogic.Create(ctx, &contacttag.ContactTag{
		OrganizationType: req.GetOrganization().GetType(),
		OrganizationID:   req.GetOrganization().GetId(),
		Name:             req.GetName(),
		Description:      req.GetDescription(),
		Color:            req.GetColor(),
		SortOrder:        req.GetSortOrder(),
		State:            req.GetState(),
		Type:             req.GetType(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateContactTagResponse{
		ContactTag: logicContactTag.ToPB(),
	}, nil
}

func (s *MetadataService) GetContactTag(ctx context.Context,
	req *customerpb.GetContactTagRequest) (*customerpb.GetContactTagResponse, error) {
	contactTag, err := s.contactTagLogic.Get(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &customerpb.GetContactTagResponse{
		ContactTag: contactTag.ToPB(),
	}, nil
}

func (s *MetadataService) ListContactTags(ctx context.Context,
	req *customerpb.ListContactTagsRequest) (*customerpb.ListContactTagsResponse, error) {

	if req.GetFilter() == nil || len(req.GetFilter().GetOrganizations()) == 0 ||
		req.GetFilter().GetOrganizations()[0] == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	filter := &contacttag.ListContactTagsFilter{
		States:        req.GetFilter().GetStates(),
		Name:          req.GetFilter().GetName(),
		Types:         req.GetFilter().GetTypes(),
		Organizations: req.GetFilter().GetOrganizations(),
	}

	if len(req.GetFilter().GetIds()) > 0 {
		filter.IDs = req.GetFilter().GetIds()
	}

	pagination := &contacttag.ListContactTagsPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: false,
	}

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		pagination.ReturnTotalSize = true
		pagination.PageSize = 0
	} else if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &contacttag.ListContactTagsOrderBy{
		Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}
	response, err := s.contactTagLogic.List(ctx, &contacttag.ListContactTagsRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	contactTags := make([]*customerpb.ContactTag, 0, len(response.ContactTags))
	for _, c := range response.ContactTags {
		contactTags = append(contactTags, c.ToPB())
	}

	return &customerpb.ListContactTagsResponse{
		ContactTags:   contactTags,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateContactTag(ctx context.Context,
	req *customerpb.UpdateContactTagRequest) (*customerpb.UpdateContactTagResponse, error) {
	// 验证 state 字段不能为 DELETED
	if req.GetState() == customerpb.ContactTag_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	contactTag, err := s.contactTagLogic.Update(ctx, &contacttag.UpdateContactTagRequest{
		ID:          req.GetId(),
		Name:        req.GetName(),
		Description: req.GetDescription(),
		Color:       req.GetColor(),
		SortOrder:   req.GetSortOrder(),
		State:       req.GetState(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateContactTagResponse{
		ContactTag: contactTag.ToPB(),
	}, nil
}

func (s *MetadataService) DeleteContactTag(ctx context.Context,
	req *customerpb.DeleteContactTagRequest) (*customerpb.DeleteContactTagResponse, error) {
	err := s.contactTagLogic.Delete(ctx, req.Id) // 不再使用 inactivate 参数，直接硬删除
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteContactTagResponse{}, nil
}

func (s *MetadataService) CreateLead(ctx context.Context,
	req *customerpb.CreateLeadRequest) (*customerpb.CreateLeadResponse, error) {
	if req.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	metadata, err := s.metadataLogic.CreateMetadata(ctx, &metadatalogic.Metadata{
		OrganizationType: req.GetOrganization().GetType(),
		OrganizationID:   req.GetOrganization().GetId(),
		Type:             customerpb.CustomerType_LEAD,
		GivenName:        req.GetGivenName(),
		FamilyName:       req.GetFamilyName(),
		CustomFields:     req.GetCustomFields(),
		LifeCycleID:      req.GetLifecycleId(),
		OwnerStaffID:     req.GetOwnerStaffId(),
		ActionStateID:    req.GetActionStateId(),
		AvatarPath:       req.GetAvatarPath(),
		ReferralSourceID: req.GetReferralSourceId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateLeadResponse{
		Lead: s.metadataToLeadPB(metadata),
	}, nil
}

func (s *MetadataService) GetLead(ctx context.Context,
	req *customerpb.GetLeadRequest) (*customerpb.GetLeadResponse, error) {
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 验证这是Lead类型的记录
	if metadata.Type != customerpb.CustomerType_LEAD {
		return nil, errs.Newm(codes.NotFound, "lead not found")
	}

	return &customerpb.GetLeadResponse{
		Lead: s.metadataToLeadPB(metadata),
	}, nil
}

func (s *MetadataService) ListLeads(ctx context.Context,
	req *customerpb.ListLeadsRequest) (*customerpb.ListLeadsResponse, error) {
	filter := &metadatalogic.ListMetadataFilter{
		Type: customerpb.CustomerType_LEAD,
	}
	if req.Filter != nil {
		filter.Organizations = req.Filter.Organizations
		if len(req.Filter.States) > 0 {
			metadataStates := make([]customerpb.Metadata_State, len(req.Filter.States))
			for i, state := range req.Filter.States {
				metadataStates[i] = customerpb.Metadata_State(state)
			}
			filter.States = metadataStates
		}
		if len(req.Filter.Ids) > 0 {
			filter.IDs = req.Filter.Ids
		}
		filter.ConvertedCustomerIDs = req.Filter.ConvertedCustomerIds
		filter.IsConverted = req.Filter.IsConverted
	}
	pagination := &metadatalogic.ListMetadataPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: false,
	}

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		pagination.ReturnTotalSize = true
		pagination.PageSize = 0
	} else if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &metadatalogic.ListMetadataOrderBy{
		Field:     customerpb.ListMetadataRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = customerpb.ListMetadataRequest_Sorting_Field(req.Sorting.Field)
		orderBy.Direction = customerpb.Direction(req.Sorting.Direction)
	}

	response, err := s.metadataLogic.ListMetadata(ctx, &metadatalogic.ListMetadataRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	leads := make([]*customerpb.Lead, 0, len(response.Metadata))
	for _, metadata := range response.Metadata {
		leads = append(leads, s.metadataToLeadPB(metadata))
	}

	return &customerpb.ListLeadsResponse{
		Leads:         leads,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateLead(ctx context.Context,
	req *customerpb.UpdateLeadRequest) (*customerpb.UpdateLeadResponse, error) {
	// 验证 state 字段不能为 DELETED
	if req.GetState() == customerpb.Lead_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	// 首先验证这是Lead类型的记录
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if metadata.Type != customerpb.CustomerType_LEAD {
		return nil, errs.Newm(codes.NotFound, "lead not found")
	}

	updateReq := &metadatalogic.UpdateMetadataRequest{
		GivenName:    req.GetGivenName(),
		FamilyName:   req.GetFamilyName(),
		CustomFields: req.GetCustomFields(),
		LifeCycleID:  req.GetLifecycleId(),
		OwnerStaffID: req.GetOwnerStaffId(),
		AvatarPath:   req.GetAvatarPath(),
	}
	if req.State != nil {
		updateReq.State = customerpb.Metadata_State(*req.State)
	}

	updatedMetadata, err := s.metadataLogic.UpdateMetadata(ctx, req.Id, updateReq)
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateLeadResponse{
		Lead: s.metadataToLeadPB(updatedMetadata),
	}, nil
}

func (s *MetadataService) DeleteLead(ctx context.Context,
	req *customerpb.DeleteLeadRequest) (*customerpb.DeleteLeadResponse, error) {
	// 首先验证这是Lead类型的记录
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if metadata.Type != customerpb.CustomerType_LEAD {
		return nil, errs.Newm(codes.NotFound, "lead not found")
	}

	err = s.metadataLogic.DeleteMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteLeadResponse{}, nil
}

func (s *MetadataService) CreateAddress(ctx context.Context,
	req *customerpb.CreateAddressRequest) (*customerpb.CreateAddressResponse, error) {
	if req.GetOrganization() == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}
	if req.Address == nil {
		return nil, errs.Newm(codes.InvalidArgument, "address is required")
	}

	// 验证客户是否存在
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.GetCustomerId())
	if err != nil {
		return nil, err
	}

	// 构建地址请求
	addressReq := &address.Address{
		CustomerID:         metadata.ID,
		OrganizationType:   customerpb.OrganizationRef_Type(req.GetOrganization().GetType()),
		OrganizationID:     req.GetOrganization().GetId(),
		Type:               req.Type,
		Revision:           req.GetAddress().GetRevision(),
		Organization:       req.GetAddress().GetOrganization(),
		SortingCode:        req.GetAddress().GetSortingCode(),
		RegionCode:         req.GetAddress().GetRegionCode(),
		LanguageCode:       req.GetAddress().GetLanguageCode(),
		PostalCode:         req.GetAddress().GetPostalCode(),
		AdministrativeArea: req.GetAddress().GetAdministrativeArea(),
		Locality:           req.GetAddress().GetLocality(),
		Sublocality:        req.GetAddress().GetSublocality(),
		AddressLines:       req.GetAddress().GetAddressLines(),
		Recipients:         req.GetAddress().GetRecipients(),
		Latitude:           req.GetLatlng().GetLatitude(),
		Longitude:          req.GetLatlng().GetLongitude(),
	}

	createdAddress, err := s.addressLogic.Create(ctx, addressReq)
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateAddressResponse{
		Address: createdAddress.ToPB(),
	}, nil
}

func (s *MetadataService) GetAddress(ctx context.Context,
	req *customerpb.GetAddressRequest) (*customerpb.GetAddressResponse, error) {
	address, err := s.addressLogic.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &customerpb.GetAddressResponse{
		Address: address.ToPB(),
	}, nil
}

func (s *MetadataService) ListAddresses(ctx context.Context,
	req *customerpb.ListAddressesRequest) (*customerpb.ListAddressesResponse, error) {
	// 构建过滤器
	filter := &address.ListAddressesFilter{
		States:           req.GetFilter().GetStates(),
		IDs:              req.GetFilter().GetIds(),
		CustomerIDs:      req.GetFilter().GetCustomerIds(),
		OrganizationRefs: req.GetFilter().GetOrganizations(),
		Types:            req.GetFilter().GetTypes(),
		SpatialFilter:    req.GetFilter().GetSpatialFilter(),
	}

	pagination := &address.ListAddressesPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: false,
	}

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		pagination.ReturnTotalSize = true
		pagination.PageSize = 0
	} else if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &address.ListAddressesOrderBy{
		Field:     customerpb.ListAddressesRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.addressLogic.List(ctx, &address.ListAddressesRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}

	addresses := make([]*customerpb.Address, 0, len(response.Addresses))
	for _, addr := range response.Addresses {
		addresses = append(addresses, addr.ToPB())
	}

	return &customerpb.ListAddressesResponse{
		Addresses:     addresses,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateAddress(ctx context.Context,
	req *customerpb.UpdateAddressRequest) (*customerpb.UpdateAddressResponse, error) {
	// 验证 state 字段不能为 DELETED
	if req.GetState() == customerpb.Address_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	// 构建更新请求
	updateReq := &address.UpdateAddressRequest{
		Type:               req.GetType(),
		State:              req.GetState(),
		Revision:           req.GetAddress().GetRevision(),
		RegionCode:         req.GetAddress().GetRegionCode(),
		LanguageCode:       req.GetAddress().GetLanguageCode(),
		PostalCode:         req.GetAddress().GetPostalCode(),
		SortingCode:        req.GetAddress().GetSortingCode(),
		AdministrativeArea: req.GetAddress().GetAdministrativeArea(),
		Locality:           req.GetAddress().GetLocality(),
		Sublocality:        req.GetAddress().GetSublocality(),
		AddressLines:       req.GetAddress().GetAddressLines(),
		Recipients:         req.GetAddress().GetRecipients(),
		Organization:       req.GetAddress().GetOrganization(),
		Latitude:           req.GetLatlng().GetLatitude(),
		Longitude:          req.GetLatlng().GetLongitude(),
	}

	updatedAddress, err := s.addressLogic.Update(ctx, req.Id, updateReq)
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateAddressResponse{
		Address: updatedAddress.ToPB(),
	}, nil
}

func (s *MetadataService) DeleteAddress(ctx context.Context,
	req *customerpb.DeleteAddressRequest) (*customerpb.DeleteAddressResponse, error) {
	err := s.addressLogic.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteAddressResponse{}, nil
}

func (s *MetadataService) CreateCustomField(ctx context.Context,
	req *customerpb.CreateCustomFieldRequest) (*customerpb.CreateCustomFieldResponse, error) {
	if req.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	// 构建定义
	def := &customfieldlogic.Definition{
		OrganizationType: req.GetOrganization().GetType(),
		OrganizationID:   req.GetOrganization().GetId(),
		AssociationType:  req.GetAssociationType(),
		FieldLabel:       req.GetLabel(),
		FieldType:        req.GetType(),
		IsRequired:       req.GetIsRequired(),
		DisplayOrder:     int(req.GetDisplayOrder()),
		HelpText:         req.GetHelpText(),
		DefaultValue:     req.GetDefaultValue(),
	}
	// 转换选项
	var options []*customfieldlogic.Option
	for _, pbOpt := range req.Options {
		opt, err := s.convertPBOption(pbOpt)
		if err != nil {
			return nil, err
		}
		options = append(options, opt)
	}
	createdDwo, err := s.customFieldLogic.Create(ctx, &customfieldlogic.DefinitionWithOptions{
		Definition: def,
		Options:    options,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateCustomFieldResponse{
		CustomField: createdDwo.ToPB(),
	}, nil
}

func (s *MetadataService) GetCustomField(ctx context.Context,
	req *customerpb.GetCustomFieldRequest) (*customerpb.GetCustomFieldResponse, error) {
	params := &customfieldlogic.GetDefinitionParams{
		ID: req.Id,
	}

	dwo, err := s.customFieldLogic.Get(ctx, params)
	if err != nil {
		return nil, err
	}

	return &customerpb.GetCustomFieldResponse{
		CustomField: dwo.ToPB(),
	}, nil
}

func (s *MetadataService) ListCustomFields(ctx context.Context,
	req *customerpb.ListCustomFieldsRequest) (*customerpb.ListCustomFieldsResponse, error) {
	filter := req.GetFilter()
	if filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}

	if len(filter.Organizations) == 0 || filter.Organizations[0] == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	// 构建过滤器
	listFilter := &customfieldlogic.ListDefinitionsFilter{
		IDs:             filter.Ids,
		Organizations:   filter.GetOrganizations(),
		AssociationType: filter.GetAssociationTypes(),
		Types:           filter.GetTypes(),
		States:          filter.States,
	}

	// 构建分页
	pagination := &customfieldlogic.ListDefinitionsPagination{
		PageSize:        req.PageSize,
		Cursor:          req.PageToken,
		ReturnTotalSize: false,
	}

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		pagination.ReturnTotalSize = true
		pagination.PageSize = 0
		pagination.Cursor = ""
	}

	// 构建排序
	var orderBy *customfieldlogic.ListDefinitionsOrderBy
	if req.Sorting != nil {
		orderBy = &customfieldlogic.ListDefinitionsOrderBy{
			Field:     req.Sorting.Field,
			Direction: req.Sorting.Direction,
		}
	}

	params := &customfieldlogic.ListDefinitionsParams{
		Filter:     listFilter,
		Pagination: pagination,
		OrderBy:    orderBy,
	}

	result, err := s.customFieldLogic.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 转换为 protobuf
	customFields := make([]*customerpb.CustomField, 0, len(result.Definitions))
	for _, dwo := range result.Definitions {
		customFields = append(customFields, dwo.ToPB())
	}

	response := &customerpb.ListCustomFieldsResponse{
		CustomFields:  customFields,
		NextPageToken: result.NextToken,
	}

	if result.TotalSize != nil {
		response.TotalSize = result.TotalSize
	}

	return response, nil
}

func (s *MetadataService) UpdateCustomField(ctx context.Context,
	req *customerpb.UpdateCustomFieldRequest) (*customerpb.UpdateCustomFieldResponse, error) {
	// Service层做基本的参数校验
	if req.Id <= 0 {
		return nil, errs.Newm(codes.InvalidArgument, "id must be greater than 0")
	}

	// 转换protobuf结构为Logic层的参数结构
	params := &customfieldlogic.UpdateFieldParams{
		ID: req.Id,
	}

	// 只有非nil的字段才会被设置，实现精确的局部更新
	if req.Label != nil {
		params.Label = req.Label
	}
	if req.IsRequired != nil {
		params.IsRequired = req.IsRequired
	}
	if req.State != nil {
		params.State = req.State
	}
	if req.DefaultValue != nil {
		params.DefaultValue = req.DefaultValue
	}
	if req.DisplayOrder != nil {
		order := int(*req.DisplayOrder)
		params.DisplayOrder = &order
	}
	if req.HelpText != nil {
		params.HelpText = req.HelpText
	}
	if len(req.Options) > 0 {
		var opts []*customfieldlogic.Option
		for _, pbOpt := range req.Options {
			opt, err := s.convertPBOption(pbOpt)
			if err != nil {
				return nil, err
			}
			opts = append(opts, opt)
		}
		params.Options = opts
	}

	// 委托给Logic层处理业务逻辑
	updatedDwo, err := s.customFieldLogic.Update(ctx, params)
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateCustomFieldResponse{
		CustomField: updatedDwo.ToPB(),
	}, nil
}

// ==================== Unified Metadata Methods ====================

func (s *MetadataService) CreateMetadata(ctx context.Context,
	req *customerpb.CreateMetadataRequest) (*customerpb.CreateMetadataResponse, error) {
	if req.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	if req.CustomerType == customerpb.CustomerType_CUSTOMER_TYPE_UNSPECIFIED {
		return nil, errs.Newm(codes.InvalidArgument, "customer_type is required")
	}

	logicMetadata := &metadatalogic.Metadata{
		OrganizationType: req.Organization.Type,
		OrganizationID:   req.Organization.Id,
		Type:             req.CustomerType,
		GivenName:        req.GivenName,
		FamilyName:       req.FamilyName,
		CustomFields:     req.GetCustomFields(),
		LifeCycleID:      req.GetLifecycleId(),
		OwnerStaffID:     req.GetOwnerStaffId(),
		ActionStateID:    req.GetActionStateId(),
		AvatarPath:       req.GetAvatarPath(),
		ReferralSourceID: req.GetReferralSourceId(),
	}

	createdMetadata, err := s.metadataLogic.CreateMetadata(ctx, logicMetadata)
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateMetadataResponse{
		Metadata: createdMetadata.ToPB(),
	}, nil
}

func (s *MetadataService) GetMetadata(ctx context.Context,
	req *customerpb.GetMetadataRequest) (*customerpb.GetMetadataResponse, error) {
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &customerpb.GetMetadataResponse{
		Metadata: metadata.ToPB(),
	}, nil
}

func (s *MetadataService) ListMetadata(ctx context.Context,
	req *customerpb.ListMetadataRequest) (*customerpb.ListMetadataResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}

	if len(req.GetFilter().GetIds()) == 0 && len(req.GetFilter().GetOrganizations()) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "ids or organization is required in filter")
	}

	logicReq := &metadatalogic.ListMetadataRequest{}
	logicReq.FromProto(req)

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		logicReq.Pagination.ReturnTotalSize = true
		logicReq.Pagination.PageSize = 0
	}

	response, err := s.metadataLogic.ListMetadata(ctx, logicReq)
	if err != nil {
		return nil, err
	}

	metadata := make([]*customerpb.Metadata, 0, len(response.Metadata))
	for _, m := range response.Metadata {
		metadata = append(metadata, m.ToPB())
	}

	return &customerpb.ListMetadataResponse{
		Metadata:      metadata,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateMetadata(ctx context.Context,
	req *customerpb.UpdateMetadataRequest) (*customerpb.UpdateMetadataResponse, error) {
	if req.GetState() == customerpb.Metadata_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	updateReq := &metadatalogic.UpdateMetadataRequest{
		GivenName:        req.GetGivenName(),
		FamilyName:       req.GetFamilyName(),
		CustomFields:     req.GetCustomFields(),
		LifeCycleID:      req.GetLifecycleId(),
		OwnerStaffID:     req.GetOwnerStaffId(),
		ActionStateID:    req.GetActionStateId(),
		AvatarPath:       req.GetAvatarPath(),
		ReferralSourceID: req.GetReferralSourceId(),
		State:            req.GetState(),
	}

	metadata, err := s.metadataLogic.UpdateMetadata(ctx, req.Id, updateReq)
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateMetadataResponse{
		Metadata: metadata.ToPB(),
	}, nil
}

func (s *MetadataService) DeleteMetadata(ctx context.Context,
	req *customerpb.DeleteMetadataRequest) (*customerpb.DeleteMetadataResponse, error) {
	err := s.metadataLogic.DeleteMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteMetadataResponse{}, nil
}

func (s *MetadataService) BatchUpdateCustomFields(ctx context.Context,
	req *customerpb.BatchUpdateCustomFieldsRequest) (*customerpb.BatchUpdateCustomFieldsResponse, error) {

	if len(req.Requests) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "requests is required")
	}

	if req.Parent == "" {
		return nil, errs.Newm(codes.InvalidArgument, "parent is required")
	}

	// 准备批量更新的实体列表
	var updates []*customfieldlogic.DefinitionWithOptions

	for _, updateReq := range req.Requests {
		// 先获取现有的自定义字段
		getParams := &customfieldlogic.GetDefinitionParams{
			ID: updateReq.Id,
		}

		dwo, err := s.customFieldLogic.Get(ctx, getParams)
		if err != nil {
			return nil, err
		}

		// Apply updates inline
		if updateReq.Label != nil {
			dwo.Definition.FieldLabel = *updateReq.Label
		}
		if updateReq.IsRequired != nil {
			dwo.Definition.IsRequired = *updateReq.IsRequired
		}
		if updateReq.State != nil {
			if *updateReq.State == customerpb.CustomField_STATE_UNSPECIFIED {
				return nil, errs.Newm(codes.InvalidArgument, "state cannot be UNSPECIFIED")
			}
			dwo.Definition.State = *updateReq.State
		}
		if updateReq.DisplayOrder != nil {
			dwo.Definition.DisplayOrder = int(*updateReq.DisplayOrder)
		}
		// DefaultValue 需要特殊处理，因为可能需要更新为零值（如空字符串）
		if updateReq.DefaultValue != nil {
			dwo.Definition.DefaultValue = updateReq.DefaultValue
		}
		if updateReq.HelpText != nil {
			dwo.Definition.HelpText = *updateReq.HelpText
		}
		if len(updateReq.Options) > 0 {
			needsOptions := dwo.Definition.FieldType == customerpb.CustomField_SELECT ||
				dwo.Definition.FieldType == customerpb.CustomField_MULTI_SELECT
			if !needsOptions {
				return nil, errs.Newm(codes.InvalidArgument,
					"only SELECT and MULTI_SELECT fields can have options")
			}
			var opts []*customfieldlogic.Option
			for _, pbOpt := range updateReq.Options {
				opt, err := s.convertPBOption(pbOpt)
				if err != nil {
					return nil, err
				}
				opts = append(opts, opt)
			}
			dwo.Options = opts
		}

		updates = append(updates, dwo)
	}

	// 调用 logic 层的批量更新
	updatedDwos, err := s.customFieldLogic.BatchUpdate(ctx, req.Parent, updates)
	if err != nil {
		return nil, err
	}

	// 转换回 protobuf
	var updatedFields []*customerpb.CustomField
	for _, dwo := range updatedDwos {
		updatedFields = append(updatedFields, dwo.ToPB())
	}

	return &customerpb.BatchUpdateCustomFieldsResponse{
		CustomFields: updatedFields,
	}, nil
}

func (s *MetadataService) DeleteCustomField(ctx context.Context,
	req *customerpb.DeleteCustomFieldRequest) (*customerpb.DeleteCustomFieldResponse, error) {
	// 先获取现有的自定义字段
	getParams := &customfieldlogic.GetDefinitionParams{
		ID: req.Id,
	}

	dwo, err := s.customFieldLogic.Get(ctx, getParams)
	if err != nil {
		return nil, err
	}

	// 软删除：标记Definition和所有Options为删除状态
	deletedState := customerpb.CustomField_DELETED
	var deletedOptions []*customfieldlogic.Option
	for _, opt := range dwo.Options {
		opt.State = customerpb.CustomField_DELETED
		deletedOptions = append(deletedOptions, opt)
	}

	params := &customfieldlogic.UpdateFieldParams{
		ID:      req.Id,
		State:   &deletedState,
		Options: deletedOptions,
	}
	_, err = s.customFieldLogic.Update(ctx, params)
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteCustomFieldResponse{}, nil
}

// ==================== CustomerRelatedData Methods ====================

func (s *MetadataService) CreateCustomerRelatedData(ctx context.Context,
	req *customerpb.CreateCustomerRelatedDataRequest) (*customerpb.CreateCustomerRelatedDataResponse, error) {
	if req.GetCustomerId() == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "customer_id is required")
	}

	if req.GetPreferredBusinessId() == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "preferred_business_id is required")
	}

	// 构建 CustomerRelatedData，对于有数据库默认值的字段，如果为空则不设置
	relatedData := &customerrelateddata.CustomerRelatedData{
		CustomerID:             req.GetCustomerId(),
		PreferredBusinessID:    req.GetPreferredBusinessId(),
		CompanyID:              req.GetCompanyId(),
		IsBlockMessage:         req.GetIsBlockMessage(),
		IsBlockOnlineBooking:   req.GetIsBlockOnlineBooking(),
		LoginEmail:             req.GetLoginEmail(),
		ReferralSourceID:       req.GetReferralSourceId(),
		ReferralSourceDesc:     req.GetReferralSourceDesc(),
		SendAutoEmail:          req.GetSendAutoEmail(),
		SendAutoMessage:        req.GetSendAutoMessage(),
		SendAppAutoMessage:     req.GetSendAppAutoMessage(),
		UnconfirmedReminderBy:  req.GetUnconfirmedReminderBy(),
		PreferredGroomerID:     req.GetPreferredGroomerId(),
		PreferredFrequencyDay:  req.GetPreferredFrequencyDay(),
		PreferredFrequencyType: req.GetPreferredFrequencyType(),
		LastServiceTime:        req.GetLastServiceTime(),
		Source:                 req.GetSource(),
		ExternalID:             req.GetExternalId(),
		CreateBy:               req.GetCreateBy(),
		UpdateBy:               req.GetUpdateBy(),
		ShareApptStatus:        req.GetShareApptStatus(),
		ShareRangeType:         req.GetShareRangeType(),
		ShareRangeValue:        req.GetShareRangeValue(),
		PreferredDay:           req.GetPreferredDay(),
		PreferredTime:          req.GetPreferredTime(),
		AccountID:              req.GetAccountId(),
		CustomerCode:           req.GetCustomerCode(),
		IsUnsubscribed:         req.GetIsUnsubscribed(),
		ActionState:            req.GetActionState(),
		CustomizeLifeCycleID:   req.GetCustomizeLifeCycleId(),
		CustomizeActionStateID: req.GetCustomizeActionStateId(),
		PreferredTipEnable:     req.GetPreferredTipEnable(),
		PreferredTipType:       req.GetPreferredTipType(),
		PreferredTipAmount:     req.GetPreferredTipAmount(),
		PreferredTipPercentage: req.GetPreferredTipPercentage(),
	}

	// 只有当 ClientColor 不为空时才设置，否则让数据库使用默认值
	if req.GetClientColor() != "" {
		relatedData.ClientColor = req.GetClientColor()
	}

	logicData, err := s.customerRelatedDataLogic.Create(ctx, relatedData)

	if err != nil {
		return nil, err
	}

	return &customerpb.CreateCustomerRelatedDataResponse{
		CustomerRelatedData: logicData.ToPB(),
	}, nil
}

func (s *MetadataService) GetCustomerRelatedData(ctx context.Context,
	req *customerpb.GetCustomerRelatedDataRequest) (*customerpb.GetCustomerRelatedDataResponse, error) {
	data, err := s.customerRelatedDataLogic.Get(ctx, req.CustomerId)
	if err != nil {
		return nil, err
	}

	return &customerpb.GetCustomerRelatedDataResponse{
		CustomerRelatedData: data.ToPB(),
	}, nil
}

func (s *MetadataService) ListCustomerRelatedData(ctx context.Context,
	req *customerpb.ListCustomerRelatedDataRequest) (*customerpb.ListCustomerRelatedDataResponse, error) {

	// 构建过滤器
	filter := &customerrelateddata.ListCustomerRelatedDataFilter{
		IDs:                  req.GetFilter().GetIds(),
		CustomerIDs:          req.GetFilter().GetCustomerIds(),
		BusinessIDs:          req.GetFilter().GetBusinessIds(),
		CompanyIDs:           req.GetFilter().GetCompanyIds(),
		States:               req.GetFilter().GetStates(),
		CustomerCodes:        req.GetFilter().GetCustomerCodes(),
		AccountIDs:           req.GetFilter().GetAccountIds(),
		Sources:              req.GetFilter().GetSources(),
		IsLapsed:             req.GetFilter().GetIsLapsed(),
		IsBlockMessage:       req.GetFilter().GetIsBlockMessage(),
		IsBlockOnlineBooking: req.GetFilter().GetIsBlockOnlineBooking(),
		LastServiceTimeGap:   req.GetFilter().GetLastServiceTimeGap(),
		PreferredGroomerIDs:  req.GetFilter().GetPreferredGroomerIds(),
	}

	pagination := &customerrelateddata.ListCustomerRelatedDataPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: false,
	}

	// 当 page_size=0 且 page_token="" 时，返回所有数据
	if req.PageSize == 0 && req.PageToken == "" {
		pagination.ReturnTotalSize = true
		pagination.PageSize = 0
	} else if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &customerrelateddata.ListCustomerRelatedDataOrderBy{
		Field:     customerpb.ListCustomerRelatedDataRequest_Sorting_ID,
		Direction: customerpb.Direction_ASC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.customerRelatedDataLogic.List(
		ctx, &customerrelateddata.ListCustomerRelatedDataRequest{
			Filter:     filter,
			Pagination: pagination,
			OrderBy:    orderBy,
		})
	if err != nil {
		return nil, err
	}

	dataList := make([]*customerpb.CustomerRelatedData, 0, len(response.CustomerRelatedData))
	for _, data := range response.CustomerRelatedData {
		dataList = append(dataList, data.ToPB())
	}

	return &customerpb.ListCustomerRelatedDataResponse{
		CustomerRelatedData: dataList,
		NextPageToken:       response.NextToken,
		TotalSize:           response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateCustomerRelatedData(ctx context.Context,
	req *customerpb.UpdateCustomerRelatedDataRequest) (*customerpb.UpdateCustomerRelatedDataResponse, error) {
	customerID := req.CustomerId
	metadata, err := s.metadataLogic.GetMetadata(ctx, customerID)
	if err != nil {
		return nil, err
	}

	if metadata == nil {
		return nil, errs.Newm(codes.NotFound, "customer related data not found")
	}

	updateReq := &customerrelateddata.UpdateCustomerRelatedDataRequest{
		ID: metadata.ID,
	}

	// 只有当字段有值时才设置指针
	if req.IsBlockMessage != nil {
		updateReq.IsBlockMessage = customerrelateddata.Int32Ptr(req.GetIsBlockMessage())
	}
	if req.IsBlockOnlineBooking != nil {
		updateReq.IsBlockOnlineBooking = customerrelateddata.Int32Ptr(req.GetIsBlockOnlineBooking())
	}
	if req.LoginEmail != nil {
		updateReq.LoginEmail = customerrelateddata.StringPtr(req.GetLoginEmail())
	}
	if req.ReferralSourceId != nil {
		updateReq.ReferralSourceID = customerrelateddata.Int32Ptr(req.GetReferralSourceId())
	}
	if req.ReferralSourceDesc != nil {
		updateReq.ReferralSourceDesc = customerrelateddata.StringPtr(req.GetReferralSourceDesc())
	}
	if req.SendAutoEmail != nil {
		updateReq.SendAutoEmail = customerrelateddata.Int32Ptr(req.GetSendAutoEmail())
	}
	if req.SendAutoMessage != nil {
		updateReq.SendAutoMessage = customerrelateddata.Int32Ptr(req.GetSendAutoMessage())
	}
	if req.SendAppAutoMessage != nil {
		updateReq.SendAppAutoMessage = customerrelateddata.Int32Ptr(req.GetSendAppAutoMessage())
	}
	if req.UnconfirmedReminderBy != nil {
		updateReq.UnconfirmedReminderBy = req.GetUnconfirmedReminderBy()
	}
	if req.PreferredGroomerId != nil {
		updateReq.PreferredGroomerID = customerrelateddata.Int32Ptr(req.GetPreferredGroomerId())
	}
	if req.PreferredFrequencyDay != nil {
		updateReq.PreferredFrequencyDay = customerrelateddata.Int32Ptr(req.GetPreferredFrequencyDay())
	}
	if req.PreferredFrequencyType != nil {
		updateReq.PreferredFrequencyType = customerrelateddata.Int32Ptr(req.GetPreferredFrequencyType())
	}
	if req.PreferredDay != nil {
		updateReq.PreferredDay = customerrelateddata.StringPtr(req.GetPreferredDay())
	}
	if req.PreferredTime != nil {
		updateReq.PreferredTime = customerrelateddata.StringPtr(req.GetPreferredTime())
	}
	if req.IsUnsubscribed != nil {
		updateReq.IsUnsubscribed = customerrelateddata.BoolPtr(req.GetIsUnsubscribed())
	}
	if req.CustomizeLifeCycleId != nil {
		updateReq.CustomizeLifeCycleID = customerrelateddata.Int64Ptr(req.GetCustomizeLifeCycleId())
	}
	if req.CustomizeActionStateId != nil {
		updateReq.CustomizeActionStateID = customerrelateddata.Int64Ptr(req.GetCustomizeActionStateId())
	}
	if req.PreferredBusinessId != nil {
		updateReq.PreferredBusinessID = customerrelateddata.Int64Ptr(req.GetPreferredBusinessId())
	}
	if req.PreferredTipEnable != nil {
		updateReq.PreferredTipEnable = customerrelateddata.Int32Ptr(req.GetPreferredTipEnable())
	}
	if req.PreferredTipType != nil {
		updateReq.PreferredTipType = customerrelateddata.Int32Ptr(req.GetPreferredTipType())
	}
	if req.PreferredTipAmount != nil {
		updateReq.PreferredTipAmount = customerrelateddata.Float64Ptr(req.GetPreferredTipAmount())
	}
	if req.PreferredTipPercentage != nil {
		updateReq.PreferredTipPercentage = customerrelateddata.Int32Ptr(req.GetPreferredTipPercentage())
	}

	// 只有当 ClientColor 字段被设置且不为空时才更新
	if req.ClientColor != nil && *req.ClientColor != "" {
		updateReq.ClientColor = customerrelateddata.StringPtr(*req.ClientColor)
	}

	if req.Birthday != nil {
		birthday := req.Birthday.AsTime()
		updateReq.Birthday = &birthday
	}

	data, err := s.customerRelatedDataLogic.Update(ctx, metadata.ID, updateReq)
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateCustomerRelatedDataResponse{
		CustomerRelatedData: data.ToPB(),
	}, nil
}

func (s *MetadataService) DeleteCustomerRelatedData(ctx context.Context,
	req *customerpb.DeleteCustomerRelatedDataRequest) (*customerpb.DeleteCustomerRelatedDataResponse, error) {
	customerID := req.CustomerId
	relatedData, err := s.customerRelatedDataLogic.Get(ctx, customerID)
	if err != nil {
		return nil, err
	}

	if relatedData == nil {
		return nil, errs.Newm(codes.NotFound, "customer related data not found")
	}

	err = s.customerRelatedDataLogic.Delete(ctx, relatedData.ID)
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteCustomerRelatedDataResponse{}, nil
}

func (s *MetadataService) BatchUpdateCustomerRelatedData(ctx context.Context,
	req *customerpb.BatchUpdateCustomerRelatedDataRequest) (*customerpb.BatchUpdateCustomerRelatedDataResponse, error) {

	if len(req.Requests) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "requests is required")
	}

	// 准备批量更新的请求列表
	var updates []*customerrelateddata.UpdateCustomerRelatedDataRequest

	for _, updateReq := range req.Requests {
		metadata, err := s.metadataLogic.GetMetadata(ctx, updateReq.CustomerId)
		if err != nil {
			return nil, err
		}
		if metadata == nil {
			return nil, errs.Newm(codes.NotFound, "customer not found")
		}

		// 构建更新请求
		logicUpdateReq := &customerrelateddata.UpdateCustomerRelatedDataRequest{
			ID: metadata.ID,
		}

		// 只有当字段有值时才设置指针
		if updateReq.IsBlockMessage != nil {
			logicUpdateReq.IsBlockMessage = customerrelateddata.Int32Ptr(updateReq.GetIsBlockMessage())
		}
		if updateReq.IsBlockOnlineBooking != nil {
			logicUpdateReq.IsBlockOnlineBooking = customerrelateddata.Int32Ptr(updateReq.GetIsBlockOnlineBooking())
		}
		if updateReq.LoginEmail != nil {
			logicUpdateReq.LoginEmail = customerrelateddata.StringPtr(updateReq.GetLoginEmail())
		}
		if updateReq.ReferralSourceId != nil {
			logicUpdateReq.ReferralSourceID = customerrelateddata.Int32Ptr(updateReq.GetReferralSourceId())
		}
		if updateReq.ReferralSourceDesc != nil {
			logicUpdateReq.ReferralSourceDesc = customerrelateddata.StringPtr(updateReq.GetReferralSourceDesc())
		}
		if updateReq.SendAutoEmail != nil {
			logicUpdateReq.SendAutoEmail = customerrelateddata.Int32Ptr(updateReq.GetSendAutoEmail())
		}
		if updateReq.SendAutoMessage != nil {
			logicUpdateReq.SendAutoMessage = customerrelateddata.Int32Ptr(updateReq.GetSendAutoMessage())
		}
		if updateReq.SendAppAutoMessage != nil {
			logicUpdateReq.SendAppAutoMessage = customerrelateddata.Int32Ptr(updateReq.GetSendAppAutoMessage())
		}
		if updateReq.UnconfirmedReminderBy != nil {
			logicUpdateReq.UnconfirmedReminderBy = updateReq.GetUnconfirmedReminderBy()
		}
		if updateReq.PreferredGroomerId != nil {
			logicUpdateReq.PreferredGroomerID = customerrelateddata.Int32Ptr(updateReq.GetPreferredGroomerId())
		}
		if updateReq.PreferredFrequencyDay != nil {
			logicUpdateReq.PreferredFrequencyDay = customerrelateddata.Int32Ptr(updateReq.GetPreferredFrequencyDay())
		}
		if updateReq.PreferredFrequencyType != nil {
			logicUpdateReq.PreferredFrequencyType = customerrelateddata.Int32Ptr(updateReq.GetPreferredFrequencyType())
		}
		if updateReq.PreferredDay != nil {
			logicUpdateReq.PreferredDay = customerrelateddata.StringPtr(updateReq.GetPreferredDay())
		}
		if updateReq.PreferredTime != nil {
			logicUpdateReq.PreferredTime = customerrelateddata.StringPtr(updateReq.GetPreferredTime())
		}
		if updateReq.IsUnsubscribed != nil {
			logicUpdateReq.IsUnsubscribed = customerrelateddata.BoolPtr(updateReq.GetIsUnsubscribed())
		}
		if updateReq.CustomizeLifeCycleId != nil {
			logicUpdateReq.CustomizeLifeCycleID = customerrelateddata.Int64Ptr(updateReq.GetCustomizeLifeCycleId())
		}
		if updateReq.CustomizeActionStateId != nil {
			logicUpdateReq.CustomizeActionStateID = customerrelateddata.Int64Ptr(updateReq.GetCustomizeActionStateId())
		}
		if updateReq.PreferredBusinessId != nil {
			logicUpdateReq.PreferredBusinessID = customerrelateddata.Int64Ptr(updateReq.GetPreferredBusinessId())
		}
		if updateReq.PreferredTipEnable != nil {
			logicUpdateReq.PreferredTipEnable = customerrelateddata.Int32Ptr(updateReq.GetPreferredTipEnable())
		}
		if updateReq.PreferredTipType != nil {
			logicUpdateReq.PreferredTipType = customerrelateddata.Int32Ptr(updateReq.GetPreferredTipType())
		}
		if updateReq.PreferredTipAmount != nil {
			logicUpdateReq.PreferredTipAmount = customerrelateddata.Float64Ptr(updateReq.GetPreferredTipAmount())
		}
		if updateReq.PreferredTipPercentage != nil {
			logicUpdateReq.PreferredTipPercentage = customerrelateddata.Int32Ptr(updateReq.GetPreferredTipPercentage())
		}

		// 只有当 ClientColor 字段被设置且不为空时才更新
		if updateReq.ClientColor != nil && *updateReq.ClientColor != "" {
			logicUpdateReq.ClientColor = customerrelateddata.StringPtr(*updateReq.ClientColor)
		}

		if updateReq.Birthday != nil {
			birthday := updateReq.Birthday.AsTime()
			logicUpdateReq.Birthday = &birthday
		}

		updates = append(updates, logicUpdateReq)
	}

	// 调用 logic 层的批量更新
	updatedData, err := s.customerRelatedDataLogic.BatchUpdate(ctx, updates)
	if err != nil {
		return nil, err
	}

	// 转换回 protobuf
	var result []*customerpb.CustomerRelatedData
	for _, data := range updatedData {
		result = append(result, data.ToPB())
	}

	return &customerpb.BatchUpdateCustomerRelatedDataResponse{
		CustomerRelatedData: result,
	}, nil
}

func (s *MetadataService) CreateCustomerAggregate(ctx context.Context,
	req *customerpb.CreateCustomerAggregateRequest) (*customerpb.CreateCustomerAggregateResponse, error) {
	if req.GetOrganization() == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}
	org := req.GetOrganization()

	if req.Customer == nil {
		return nil, errs.Newm(codes.InvalidArgument, "customer is required")
	}

	// 首先创建Customer metadata
	metadata, err := s.metadataLogic.CreateMetadata(ctx, &metadatalogic.Metadata{
		OrganizationType: org.GetType(),
		OrganizationID:   org.GetId(),
		Type:             customerpb.CustomerType_CUSTOMER,
		GivenName:        req.GetCustomer().GetGivenName(),
		FamilyName:       req.GetCustomer().GetFamilyName(),
		CustomFields:     req.GetCustomer().GetCustomFields(),
		LifeCycleID:      req.GetCustomer().GetLifecycleId(),
		OwnerStaffID:     req.GetCustomer().GetOwnerStaffId(),
		ActionStateID:    req.GetCustomer().GetActionStateId(),
		AvatarPath:       req.GetCustomer().GetAvatarPath(),
		ReferralSourceID: req.GetCustomer().GetReferralSourceId(),
		CustomerCode:     random.String(16),
	})
	if err != nil {
		return nil, err
	}

	response := &customerpb.CustomerAggregate{
		Customer: s.metadataToCustomerPB(metadata),
	}

	// 创建关联的地址
	for _, addr := range req.GetAddresses() {
		addressReq := &address.Address{
			CustomerID:         metadata.ID,
			Type:               addr.GetType(),
			OrganizationID:     org.GetId(),
			OrganizationType:   org.GetType(),
			Revision:           addr.GetAddress().GetRevision(),
			Organization:       addr.GetAddress().GetOrganization(),
			SortingCode:        addr.GetAddress().GetSortingCode(),
			RegionCode:         addr.GetAddress().GetRegionCode(),
			LanguageCode:       addr.GetAddress().GetLanguageCode(),
			PostalCode:         addr.GetAddress().GetPostalCode(),
			AdministrativeArea: addr.GetAddress().GetAdministrativeArea(),
			Locality:           addr.GetAddress().GetLocality(),
			Sublocality:        addr.GetAddress().GetSublocality(),
			AddressLines:       addr.GetAddress().GetAddressLines(),
			Recipients:         addr.GetAddress().GetRecipients(),
			Latitude:           addr.GetLatlng().GetLatitude(),
			Longitude:          addr.GetLatlng().GetLongitude(),
		}
		createdAddr, err := s.addressLogic.Create(ctx, addressReq)
		if err != nil {
			return nil, err
		}
		response.Addresses = append(response.Addresses, createdAddr.ToPB())
	}

	// 创建关联的联系人
	for _, cont := range req.GetContacts() {
		contactReq := &contact.Contact{
			CustomerID:       metadata.ID,
			OrganizationType: org.GetType(),
			OrganizationID:   org.GetId(),
			IsSelf:           cont.GetIsSelf(),
			GivenName:        cont.GetGivenName(),
			FamilyName:       cont.GetFamilyName(),
			Email:            cont.GetEmail(),
			Note:             cont.GetNote(),
			Phone:            cont.GetPhone().GetE164Number(),
		}
		createdContact, err := s.contactLogic.Create(ctx, contactReq)
		if err != nil {
			return nil, err
		}
		response.Contacts = append(response.Contacts, createdContact.ToPB())
	}

	// 创建关联的客户相关数据
	if req.GetCustomerRelatedData() != nil {
		relatedDataReq := &customerrelateddata.CustomerRelatedData{
			CustomerID: metadata.ID,
		}
		relatedDataReq.FromProto(req.GetCustomerRelatedData())
		createdRelatedData, err := s.customerRelatedDataLogic.Create(ctx, relatedDataReq)
		if err != nil {
			return nil, err
		}
		response.RelatedData = createdRelatedData.ToPB()
	}

	return &customerpb.CreateCustomerAggregateResponse{
		CustomerAggregate: response,
	}, nil
}

// convertPBOption 转换选项
func (s *MetadataService) convertPBOption(pbOpt *customerpb.CustomField_Option) (*customfieldlogic.Option, error) {
	if pbOpt == nil {
		return nil, nil
	}

	// 设置 option 状态，如果请求中有指定则使用，否则默认为 ACTIVE
	state := customerpb.CustomField_ACTIVE
	if pbOpt.State != customerpb.CustomField_STATE_UNSPECIFIED {
		state = pbOpt.State
	}

	return &customfieldlogic.Option{
		Label:     pbOpt.GetLabel(),
		Value:     pbOpt.GetValue(),
		SortOrder: int(pbOpt.GetSortOrder()),
		State:     state,
	}, nil
}

// metadataToCustomerPB 将Metadata转换为Customer protobuf对象
func (s *MetadataService) metadataToCustomerPB(metadata *metadatalogic.Metadata) *customerpb.Customer {
	if metadata == nil {
		return nil
	}

	return &customerpb.Customer{
		Id: metadata.ID,
		Organization: &customerpb.OrganizationRef{
			Type: metadata.OrganizationType,
			Id:   metadata.OrganizationID,
		},
		GivenName:        metadata.GivenName,
		FamilyName:       metadata.FamilyName,
		State:            customerpb.Customer_State(metadata.State),
		CustomFields:     metadata.CustomFields,
		LifecycleId:      metadata.LifeCycleID,
		OwnerStaffId:     metadata.OwnerStaffID,
		ActionStateId:    metadata.ActionStateID,
		AvatarPath:       metadata.AvatarPath,
		CustomerCode:     metadata.CustomerCode,
		ReferralSourceId: metadata.ReferralSourceID,
		CreateTime:       metadata.ToPB().CreateTime,
		UpdateTime:       metadata.ToPB().UpdateTime,
		DeleteTime:       metadata.ToPB().DeleteTime,
	}
}

// metadataToLeadPB 将Metadata转换为Lead protobuf对象
func (s *MetadataService) metadataToLeadPB(metadata *metadatalogic.Metadata) *customerpb.Lead {
	if metadata == nil {
		return nil
	}

	return &customerpb.Lead{
		Id: metadata.ID,
		Organization: &customerpb.OrganizationRef{
			Type: metadata.OrganizationType,
			Id:   metadata.OrganizationID,
		},
		GivenName:           metadata.GivenName,
		FamilyName:          metadata.FamilyName,
		State:               customerpb.Lead_State(metadata.State),
		CustomFields:        metadata.CustomFields,
		LifecycleId:         metadata.LifeCycleID,
		OwnerStaffId:        metadata.OwnerStaffID,
		ActionStateId:       metadata.ActionStateID,
		AvatarPath:          metadata.AvatarPath,
		ReferralSourceId:    metadata.ReferralSourceID,
		CreateTime:          metadata.ToPB().CreateTime,
		UpdateTime:          metadata.ToPB().UpdateTime,
		DeleteTime:          metadata.ToPB().DeleteTime,
		ConvertedCustomerId: metadata.ConvertToCustomerID,
	}
}

// buildMetadataFromRequest 从请求构建metadata对象
func (s *MetadataService) buildMetadataFromRequest(
	req *customerpb.CreateMetadataAggregateRequest) *metadatalogic.Metadata {
	return &metadatalogic.Metadata{
		OrganizationType: req.GetOrganization().GetType(),
		OrganizationID:   req.GetOrganization().GetId(),
		Type:             req.GetMetadata().GetCustomerType(),
		GivenName:        req.GetMetadata().GetGivenName(),
		FamilyName:       req.GetMetadata().GetFamilyName(),
		CustomFields:     req.GetMetadata().GetCustomFields(),
		LifeCycleID:      req.GetMetadata().GetLifecycleId(),
		OwnerStaffID:     req.GetMetadata().GetOwnerStaffId(),
		ActionStateID:    req.GetMetadata().GetActionStateId(),
		AvatarPath:       req.GetMetadata().GetAvatarPath(),
		ReferralSourceID: req.GetMetadata().GetReferralSourceId(),
	}
}

// convertAddressesFromRequest 从请求转换地址信息
func (s *MetadataService) convertAddressesFromRequest(
	req *customerpb.CreateMetadataAggregateRequest) []*address.Address {
	org := req.GetOrganization()
	var addresses []*address.Address
	for _, addr := range req.GetAddresses() {
		postalAddr := addr.GetAddress()
		latLng := addr.GetLatlng()

		addressEntity := &address.Address{
			CustomerID:       0, // 将在聚合创建时设置
			Type:             addr.GetType(),
			OrganizationType: org.GetType(),
			OrganizationID:   org.GetId(),
		}

		// 设置postal address字段
		if postalAddr != nil {
			addressEntity.Revision = postalAddr.GetRevision()
			addressEntity.RegionCode = postalAddr.GetRegionCode()
			addressEntity.LanguageCode = postalAddr.GetLanguageCode()
			addressEntity.Organization = postalAddr.GetOrganization()
			addressEntity.PostalCode = postalAddr.GetPostalCode()
			addressEntity.SortingCode = postalAddr.GetSortingCode()
			addressEntity.AdministrativeArea = postalAddr.GetAdministrativeArea()
			addressEntity.Locality = postalAddr.GetLocality()
			addressEntity.Sublocality = postalAddr.GetSublocality()
			addressEntity.AddressLines = postalAddr.GetAddressLines()
			addressEntity.Recipients = postalAddr.GetRecipients()
		}

		// 设置经纬度
		if latLng != nil {
			addressEntity.Latitude = latLng.GetLatitude()
			addressEntity.Longitude = latLng.GetLongitude()
		}

		addresses = append(addresses, addressEntity)
	}

	return addresses
}

// convertContactsFromRequest 从请求转换联系人信息
func (s *MetadataService) convertContactsFromRequest(
	req *customerpb.CreateMetadataAggregateRequest) []*contact.Contact {
	org := req.GetOrganization()
	var contacts []*contact.Contact
	for _, cont := range req.GetContacts() {
		// 转换电话号码
		phoneStr := ""
		if phone := cont.GetPhone(); phone != nil {
			phoneStr = phone.GetE164Number()
		}

		// 转换标签
		var tags []*contacttag.ContactTag
		for _, tag := range cont.GetTags() {
			contactTag := &contacttag.ContactTag{
				ID:        tag.GetId(),
				Name:      tag.GetName(),
				Color:     tag.GetColor(),
				SortOrder: tag.GetSortOrder(),
				State:     tag.GetState(),
				Type:      tag.GetType(),
			}
			// 安全地访问Organization字段
			if org != nil {
				contactTag.OrganizationType = org.GetType()
				contactTag.OrganizationID = org.GetId()
			}
			tags = append(tags, contactTag)
		}

		contacts = append(contacts, &contact.Contact{
			CustomerID: 0, // 将在聚合创建时设置
			GivenName:  cont.GetGivenName(),
			FamilyName: cont.GetFamilyName(),
			Email:      cont.GetEmail(),
			Phone:      phoneStr,
			IsSelf:     cont.GetIsSelf(),
			Note:       cont.GetNote(),
			Tags:       tags,
		})
	}

	return contacts
}

// convertCustomerRelatedDataFromRequest 从请求转换客户相关数据
func (s *MetadataService) convertCustomerRelatedDataFromRequest(
	req *customerpb.CreateMetadataAggregateRequest) *customerrelateddata.CustomerRelatedData {
	if req.GetCustomerRelatedData() == nil {
		return nil
	}

	crd := req.GetCustomerRelatedData()
	if crd.GetCompanyId() == 0 {
		if req.GetOrganization().GetType() == customerpb.OrganizationRef_COMPANY {
			crd.CompanyId = req.GetOrganization().GetId()
		}
	}

	// 转换isRecurring指针
	var isRecurring *int32
	if hasRecurring := crd.GetIsRecurring(); hasRecurring != 0 {
		isRecurring = &hasRecurring
	}

	// 转换ShareApptJSON指针
	var shareApptJSON *string
	if jsonStr := crd.GetShareApptJson(); jsonStr != "" {
		shareApptJSON = &jsonStr
	}

	// 转换生日时间戳到time.Time
	var birthday *time.Time
	if birthdayPb := crd.GetBirthday(); birthdayPb != nil {
		birthdayTime := birthdayPb.AsTime()
		birthday = &birthdayTime
	}

	result := &customerrelateddata.CustomerRelatedData{
		CustomerID:             0, // 将在聚合创建时设置
		PreferredBusinessID:    crd.GetPreferredBusinessId(),
		CompanyID:              crd.GetCompanyId(),
		IsBlockMessage:         crd.GetIsBlockMessage(),
		IsBlockOnlineBooking:   crd.GetIsBlockOnlineBooking(),
		LoginEmail:             crd.GetLoginEmail(),
		ReferralSourceID:       crd.GetReferralSourceId(),
		ReferralSourceDesc:     crd.GetReferralSourceDesc(),
		SendAutoEmail:          crd.GetSendAutoEmail(),
		SendAutoMessage:        crd.GetSendAutoMessage(),
		SendAppAutoMessage:     crd.GetSendAppAutoMessage(),
		UnconfirmedReminderBy:  crd.GetUnconfirmedReminderBy(),
		PreferredGroomerID:     crd.GetPreferredGroomerId(),
		PreferredFrequencyDay:  crd.GetPreferredFrequencyDay(),
		PreferredFrequencyType: crd.GetPreferredFrequencyType(),
		LastServiceTime:        crd.GetLastServiceTime(),
		Source:                 crd.GetSource(),
		ExternalID:             crd.GetExternalId(),
		CreateBy:               crd.GetCreateBy(),
		UpdateBy:               crd.GetUpdateBy(),
		IsRecurring:            isRecurring,
		ShareApptStatus:        crd.GetShareApptStatus(),
		ShareRangeType:         crd.GetShareRangeType(),
		ShareRangeValue:        crd.GetShareRangeValue(),
		ShareApptJSON:          shareApptJSON,
		PreferredDay:           crd.GetPreferredDay(),
		PreferredTime:          crd.GetPreferredTime(),
		AccountID:              crd.GetAccountId(),
		CustomerCode:           crd.GetCustomerCode(),
		IsUnsubscribed:         crd.GetIsUnsubscribed(),
		Birthday:               birthday,
		ActionState:            crd.GetActionState(),
		CustomizeLifeCycleID:   crd.GetCustomizeLifeCycleId(),
		CustomizeActionStateID: crd.GetCustomizeActionStateId(),
		PreferredTipEnable:     crd.GetPreferredTipEnable(),
		PreferredTipType:       crd.GetPreferredTipType(),
		PreferredTipAmount:     crd.GetPreferredTipAmount(),
		PreferredTipPercentage: crd.GetPreferredTipPercentage(),
	}

	// 只有当 ClientColor 不为空时才设置，否则让数据库使用默认值
	if crd.GetClientColor() != "" {
		result.ClientColor = crd.GetClientColor()
	}

	return result
}

// buildMetadataAggregateResponse 构建聚合响应
func (s *MetadataService) buildMetadataAggregateResponse(
	aggregateResp *metadatalogic.AggregateResponse) *customerpb.MetadataAggregate {
	pbAggregate := &customerpb.MetadataAggregate{
		Metadata: aggregateResp.Metadata.ToPB(),
	}

	// 转换地址
	for _, addr := range aggregateResp.Addresses {
		pbAggregate.Addresses = append(pbAggregate.Addresses, addr.ToPB())
	}

	// 转换联系人
	for _, cont := range aggregateResp.Contacts {
		pbAggregate.Contacts = append(pbAggregate.Contacts, cont.ToPB())
	}

	// 转换客户相关数据
	if aggregateResp.RelatedData != nil {
		pbAggregate.RelatedData = aggregateResp.RelatedData.ToPB()
	}

	return pbAggregate
}

// CreateMetadataAggregate 聚合创建元数据及相关实体
func (s *MetadataService) CreateMetadataAggregate(ctx context.Context,
	req *customerpb.CreateMetadataAggregateRequest) (*customerpb.CreateMetadataAggregateResponse, error) {
	if req.GetOrganization() == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}
	if req.GetMetadata() == nil {
		return nil, errs.Newm(codes.InvalidArgument, "metadata is required")
	}

	// 构建聚合请求
	aggregateReq := &metadatalogic.AggregateRequest{
		Metadata:    s.buildMetadataFromRequest(req),
		Addresses:   s.convertAddressesFromRequest(req),
		Contacts:    s.convertContactsFromRequest(req),
		RelatedData: s.convertCustomerRelatedDataFromRequest(req),
	}

	// 调用聚合创建
	aggregateResp, err := s.metadataLogic.CreateMetadataAggregate(ctx, aggregateReq)
	if err != nil {
		return nil, err
	}

	// 构建并返回响应
	return &customerpb.CreateMetadataAggregateResponse{
		MetadataAggregate: s.buildMetadataAggregateResponse(aggregateResp),
	}, nil
}

// ==================== Customer Creation Setting Methods ====================

// GetCompanySetting 获取公司设置
func (s *MetadataService) GetCompanySetting(ctx context.Context,
	req *customerpb.GetCompanySettingRequest) (*customerpb.GetCompanySettingResponse, error) {

	companySetting, err := s.companySettingLogic.GetCompanySettingByCompanyID(ctx, req.CompanyId)
	if err != nil {
		return nil, err
	}

	return &customerpb.GetCompanySettingResponse{
		CompanySetting: companySetting.ToPB(),
	}, nil
}

// UpdateCompanySetting 更新公司设置
func (s *MetadataService) UpdateCompanySetting(ctx context.Context,
	req *customerpb.UpdateCompanySettingRequest) (*customerpb.UpdateCompanySettingResponse, error) {

	// 构建要更新的公司设置
	companySetting := &companysetting.CompanySetting{}

	// 设置 CustomerCreationSetting
	companySetting.CustomerCreationSetting = &companysetting.CustomerCreationSetting{
		EnableCreationFromSMS:  req.GetEnableCreationViaSms(),
		EnableCreationFromCall: req.GetEnableCreationViaCall(),
	}

	// 设置 DefaultPreferredSetting
	if req.DefaultPreferredSetting != nil {
		companySetting.DefaultPreferredSetting = &companysetting.DefaultPreferredSetting{
			DefaultPreferredFrequencyType:  req.DefaultPreferredSetting.GetDefaultPreferredFrequencyType(),
			DefaultPreferredCalendarPeriod: req.DefaultPreferredSetting.GetDefaultPreferredCalendarPeriod(),
			DefaultPreferredFrequencyValue: req.DefaultPreferredSetting.GetDefaultPreferredFrequencyValue(),
		}
	}

	// 先尝试获取现有设置，判断是创建还是更新
	_, err := s.companySettingLogic.GetCompanySettingByCompanyID(ctx, req.CompanyId)
	var updatedCompanySetting *companysetting.CompanySetting

	if err != nil {
		// 如果不存在，创建新的设置
		updatedCompanySetting, err = s.companySettingLogic.CreateCompanySetting(ctx, req.CompanyId, companySetting)
		if err != nil {
			return nil, err
		}
	} else {
		// 如果存在，更新现有设置
		updatedCompanySetting, err = s.companySettingLogic.UpdateCompanySetting(ctx, req.CompanyId, companySetting)
		if err != nil {
			return nil, err
		}
	}

	return &customerpb.UpdateCompanySettingResponse{
		CompanySetting: updatedCompanySetting.ToPB(),
	}, nil
}

func (s *MetadataService) UpdateCustomerAggregate(ctx context.Context,
	request *customerpb.UpdateCustomerAggregateRequest) (*customerpb.UpdateCustomerAggregateResponse,
	error) {
	// init
	ops := make([]func(context.Context, *gorm.DB) error, 0)
	metaData := make([]*customerpb.Metadata, 0, len(request.GetMetadataRequests()))
	contacts := make([]*customerpb.Contact, 0, len(request.GetContactRequests()))
	addresses := make([]*customerpb.Address, 0, len(request.GetAddressRequests()))
	rels := make([]*customerpb.CustomerRelatedData, 0, len(request.GetRelatedRequests()))

	// add tx ops
	for _, r := range request.GetMetadataRequests() {
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			metadataResp, err := s.WithTx(tx).UpdateMetadata(ctx, r)
			if err != nil {
				log.ErrorContextf(opCtx, "UpdateCustomerAggregate UpdateMetadata err, err:%v", err)

				return err
			}
			metaData = append(metaData, metadataResp.GetMetadata())

			return nil
		})
	}
	for _, r := range request.GetContactRequests() {
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			contactResp, err := s.WithTx(tx).updateContact(ctx, r, true)
			if err != nil {
				log.ErrorContextf(opCtx, "UpdateCustomerAggregate UpdateContact err, err:%v", err)

				return err
			}
			contacts = append(contacts, contactResp.GetContact())

			return nil
		})
	}
	for _, r := range request.GetAddressRequests() {
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			addressResp, err := s.WithTx(tx).UpdateAddress(ctx, r)
			if err != nil {
				log.ErrorContextf(opCtx, "UpdateCustomerAggregate UpdateAddress err, err:%v", err)

				return err
			}
			addresses = append(addresses, addressResp.GetAddress())

			return nil
		})
	}
	for _, r := range request.GetRelatedRequests() {
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			relResp, err := s.WithTx(tx).UpdateCustomerRelatedData(ctx, r)
			if err != nil {
				log.ErrorContextf(opCtx, "UpdateCustomerAggregate UpdateCustomerRelatedData err, err:%v", err)

				return err
			}
			rels = append(rels, relResp.GetCustomerRelatedData())

			return nil
		})
	}

	// execute ops
	if err := s.txManager.ExecuteInTransaction(ctx, ops); err != nil {
		log.ErrorContextf(ctx, "UpdateCustomerAggregate ExecuteInTransaction tx err, err:%v", err)

		return nil, err
	}

	return &customerpb.UpdateCustomerAggregateResponse{
		Metadata:    metaData,
		Contacts:    contacts,
		Addresses:   addresses,
		RelatedData: rels,
	}, nil
}

func (s *MetadataService) ListCustomerAggregate(ctx context.Context,
	request *customerpb.ListCustomerAggregateRequest) (*customerpb.ListCustomerAggregateResponse, error) {
	if len(request.GetCustomerIds()) == 0 {
		return &customerpb.ListCustomerAggregateResponse{
			Data: map[int64]*customerpb.ListCustomerAggregateResponse_CustomerAggregateData{},
		}, nil
	}
	if len(request.GetCustomerIds()) > 200 {
		return nil, status.Errorf(codes.InvalidArgument, "customerIDs size is too large")
	}

	// load data info
	var (
		customerIDs = funk.UniqInt64(request.GetCustomerIds())
		g           errgroup.Group

		metadataMap map[int64]*customerpb.Metadata
		contactMap  map[int64][]*customerpb.Contact
		addressMap  map[int64][]*customerpb.Address
		relMap      map[int64][]*customerpb.CustomerRelatedData
	)

	// list meta
	g.Go(func() error {
		var err error
		metadataMap, err = s.loadMetadata(ctx, customerIDs)

		return err
	})

	for _, field := range request.GetFields() {
		switch field {
		case customerpb.ListCustomerAggregateRequest_CONTACT:
			g.Go(func() error {
				var err error
				contactMap, err = s.loadContacts(ctx, customerIDs)

				return err
			})
		case customerpb.ListCustomerAggregateRequest_ADDRESS:
			g.Go(func() error {
				var err error
				addressMap, err = s.loadAddresses(ctx, customerIDs)

				return err
			})
		case customerpb.ListCustomerAggregateRequest_RELATED:
			g.Go(func() error {
				var err error
				relMap, err = s.loadRelatedData(ctx, customerIDs)

				return err
			})
		}
	}

	// load all
	if err := g.Wait(); err != nil {
		log.ErrorContextf(ctx, "ListCustomerAggregate Load all err, err:%v", err)

		return nil, err
	}

	// merge data
	res := make(map[int64]*customerpb.ListCustomerAggregateResponse_CustomerAggregateData, len(metadataMap))
	for _, customerID := range customerIDs {
		metadata := metadataMap[customerID]
		if metadata == nil {
			log.InfoContextf(ctx, "ListCustomerAggregate customerID miss metadata, customerID:%d", customerID)

			continue
		}
		item := &customerpb.ListCustomerAggregateResponse_CustomerAggregateData{
			Metadata: metadata,
		}
		if len(contactMap[customerID]) > 0 {
			item.Contacts = contactMap[customerID]
		}
		if len(addressMap[customerID]) > 0 {
			item.Addresses = addressMap[customerID]
		}
		if len(relMap[customerID]) > 0 {
			item.RelatedData = relMap[customerID]
		}
		res[customerID] = item
	}

	return &customerpb.ListCustomerAggregateResponse{
		Data: res,
	}, nil
}

// 定义子函数加载不同的数据
func (s *MetadataService) loadMetadata(ctx context.Context, customerIDs []int64) (
	map[int64]*customerpb.Metadata, error) {
	resp, err := s.metadataLogic.ListMetadata(ctx, &metadatalogic.ListMetadataRequest{
		Filter: &metadatalogic.ListMetadataFilter{
			IDs: customerIDs,
		},
		Pagination: &metadatalogic.ListMetadataPagination{
			PageSize:        0,
			ReturnTotalSize: false,
		},
		OrderBy: &metadatalogic.ListMetadataOrderBy{
			Field:     customerpb.ListMetadataRequest_Sorting_ID,
			Direction: customerpb.Direction_ASC,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "loadMetadata error: %v", err)

		return nil, err
	}
	metadataMap := make(map[int64]*customerpb.Metadata, len(resp.GetMetadata()))
	for _, metadata := range resp.GetMetadata() {
		if metadata == nil {
			continue
		}
		metadataMap[metadata.ID] = metadata.ToPB()
	}

	return metadataMap, nil
}

func (s *MetadataService) loadContacts(ctx context.Context, customerIDs []int64) (
	map[int64][]*customerpb.Contact, error) {
	resp, err := s.contactLogic.List(ctx, &contact.ListContactsRequest{
		Filter: &contact.ListContactsFilter{
			CustomerIDs: customerIDs,
		},
		Pagination: &contact.ListContactsPagination{
			PageSize:        0,
			ReturnTotalSize: false,
		},
		OrderBy: &contact.ListContactsOrderBy{
			Field:     customerpb.ListContactsRequest_Sorting_ID,
			Direction: customerpb.Direction_ASC,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "loadContacts error: %v", err)

		return nil, err
	}
	contactMap := make(map[int64][]*customerpb.Contact, len(resp.GetContacts()))
	for _, c := range resp.GetContacts() {
		if c == nil {
			continue
		}
		contactMap[c.CustomerID] = append(contactMap[c.CustomerID], c.ToPB())
	}

	return contactMap, nil
}

func (s *MetadataService) loadAddresses(ctx context.Context, customerIDs []int64) (
	map[int64][]*customerpb.Address, error) {
	resp, err := s.addressLogic.List(ctx, &address.ListAddressesRequest{
		Filter: &address.ListAddressesFilter{
			CustomerIDs: customerIDs,
		},
		Pagination: &address.ListAddressesPagination{
			PageSize:        0,
			ReturnTotalSize: false,
		},
		OrderBy: &address.ListAddressesOrderBy{
			Field:     customerpb.ListAddressesRequest_Sorting_ID,
			Direction: customerpb.Direction_ASC,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "loadAddresses error: %v", err)

		return nil, err
	}
	addressMap := make(map[int64][]*customerpb.Address, len(resp.GetAddresses()))
	for _, a := range resp.GetAddresses() {
		if a == nil {
			continue
		}
		addressMap[a.CustomerID] = append(addressMap[a.CustomerID], a.ToPB())
	}

	return addressMap, nil
}

func (s *MetadataService) loadRelatedData(ctx context.Context, customerIDs []int64) (
	map[int64][]*customerpb.CustomerRelatedData, error) {
	resp, err := s.customerRelatedDataLogic.List(ctx, &customerrelateddata.ListCustomerRelatedDataRequest{
		Filter: &customerrelateddata.ListCustomerRelatedDataFilter{
			CustomerIDs: customerIDs,
		},
		Pagination: &customerrelateddata.ListCustomerRelatedDataPagination{
			PageSize:        0,
			ReturnTotalSize: false,
		},
		OrderBy: &customerrelateddata.ListCustomerRelatedDataOrderBy{
			Field:     customerpb.ListCustomerRelatedDataRequest_Sorting_ID,
			Direction: customerpb.Direction_ASC,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "loadRelatedData error: %v", err)

		return nil, err
	}
	relMap := make(map[int64][]*customerpb.CustomerRelatedData, len(resp.GetCustomerRelatedData()))
	for _, r := range resp.GetCustomerRelatedData() {
		if r == nil {
			continue
		}
		relMap[r.CustomerID] = append(relMap[r.CustomerID], r.ToPB())
	}

	return relMap, nil
}
