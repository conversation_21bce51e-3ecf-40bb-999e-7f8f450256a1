package lead

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	customerRepo            customerrepo.Repository
	customerRelatedDataRepo customerrelateddatarepo.Repository
}

func New() *Logic {
	return &Logic{
		customerRepo:            customerrepo.New(),
		customerRelatedDataRepo: customerrelateddatarepo.New(),
	}
}

func NewByParams(
	customerRepo customerrepo.Repository,
	customerRelatedDataRepo customerrelateddatarepo.Repository,
) *Logic {
	return &Logic{
		customerRepo:            customerRepo,
		customerRelatedDataRepo: customerRelatedDataRepo,
	}
}

func (l *Logic) Create(ctx context.Context, lead *Lead) (*Lead, error) {
	// create customer, set output only columns
	now := time.Now().UTC()
	lead.CreatedTime = now
	lead.UpdatedTime = now
	lead.State = customerpb.Lead_ACTIVE
	lead.Type = customerpb.CustomerType_LEAD
	lead.CustomerCode = random.String(16)
	dbLead := lead.ToDB()

	dbLead, err := l.customerRepo.Create(ctx, dbLead)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_LEAD_FAILED)
	}

	// create default customer related data，参考 customer.go 的实现
	customerRelatedData := &customerrelateddata.CustomerRelatedData{
		CustomerID:   dbLead.ID,
		CustomerCode: dbLead.CustomerCode,
	}
	_, err = l.customerRelatedDataRepo.Create(ctx, customerRelatedData.ToDB())
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_CUSTOMER_AGGREGATE_FAILED)
	}

	// convert to logic lead, and return
	return convertToLead(dbLead), nil
}

func (l *Logic) Get(ctx context.Context, id int64) (*Lead, error) {
	dbLead, err := l.customerRepo.Get(ctx, id, customerpb.CustomerType_LEAD)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_LEAD_NOT_FOUND)
		}

		return nil, err
	}

	return convertToLead(dbLead), nil
}

func (l *Logic) List(ctx context.Context, req *ListLeadsRequest) (*ListLeadsResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListLeadsOrderBy{
			Field:     customerpb.ListLeadsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.Direction_DESC,
		}
	}

	// 使用分页工具类处理page token
	paginationHelper := customerutils.NewPaginationHelper()
	pageToken := ""
	if req.Pagination != nil {
		pageToken = req.Pagination.Cursor
	}
	paginationInfo, err := paginationHelper.ProcessPageToken(pageToken, req.Pagination.PageSize)
	if err != nil {
		return nil, errs.Newm(codes.InvalidArgument, fmt.Sprintf("invalid page token: %v", err))
	}

	// 构建分页参数
	pagination := &customerrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		ReturnTotalSize: paginationInfo.ShouldReturnTotalSize(req.Pagination.ReturnTotalSize),
	}

	if paginationInfo.IsPageNumberMode() {
		// 使用offset分页
		pagination.Offset = &paginationInfo.Offset
	} else {
		// 使用cursor分页
		pagination.Cursor = paginationInfo.Cursor
	}

	states := make([]customerpb.Customer_State, len(req.Filter.States))
	for i, state := range req.Filter.States {
		states[i] = customerpb.Customer_State(state)
	}
	dbLeads, err := l.customerRepo.ListByCursor(ctx, &customerrepo.ListFilter{
		IDs:                  req.Filter.IDs,
		Organizations:        req.Filter.Organizations,
		Type:                 customerpb.CustomerType_LEAD,
		States:               states,
		OwnerStaffIDs:        req.Filter.OwnerStaffIDs,
		LifecycleIDs:         req.Filter.LifecycleIDs,
		ConvertedCustomerIDs: req.Filter.ConvertedCustomerIDs,
		IsConverted:          req.Filter.IsConverted,
	}, pagination, &customerrepo.OrderBy{
		Field:     customerpb.ListCustomersRequest_Sorting_Field(req.OrderBy.Field),
		Direction: customerpb.Direction(req.OrderBy.Direction),
	})
	if err != nil {
		return nil, err
	}

	result := &ListLeadsResponse{
		Leads:   convertToLeads(dbLeads.Data),
		HasNext: dbLeads.HasNext,
	}
	if dbLeads.TotalCount != nil {
		result.TotalSize = dbLeads.TotalCount
	}

	// 生成下一页token
	if dbLeads.HasNext && len(dbLeads.Data) > 0 {
		var nextCursor *postgres.Cursor
		if paginationInfo.IsCursorMode() {
			// cursor模式，生成cursor
			lastLead := dbLeads.Data[len(dbLeads.Data)-1]
			nextCursor = &postgres.Cursor{
				ID:        lastLead.ID,
				CreatedAt: lastLead.CreatedTime,
			}
		}
		result.NextToken = paginationHelper.GenerateNextToken(paginationInfo, dbLeads.HasNext, nextCursor)
	}

	return result, nil
}

func (l *Logic) Update(ctx context.Context, id int64, updateRef *UpdateLeadRequest) (*Lead, error) {
	// check lead exists
	lead, err := l.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	lead.UpdatedTime = now
	lead.GivenName = updateRef.GivenName
	lead.FamilyName = updateRef.FamilyName
	lead.CustomFields = updateRef.CustomFields
	lead.LifeCycleID = updateRef.LifeCycleID
	lead.OwnerStaffID = updateRef.OwnerStaffID
	lead.State = updateRef.State
	lead.AvatarPath = updateRef.AvatarPath

	updatedLead, err := l.customerRepo.Update(ctx, lead.ToDB())
	if err != nil {
		return nil, err
	}

	return convertToLead(updatedLead), nil
}

func (l *Logic) Delete(ctx context.Context, id int64) error {
	_, err := l.Get(ctx, id)
	if err != nil {
		return err
	}

	err = l.customerRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func convertToLeads(dbLeads []*customerrepo.Customer) []*Lead {
	leads := make([]*Lead, len(dbLeads))
	for i, dbLead := range dbLeads {
		leads[i] = convertToLead(dbLead)
	}

	return leads
}

func convertToLead(dbLead *customerrepo.Customer) *Lead {
	return &Lead{
		ID:                  dbLead.ID,
		OrganizationType:    dbLead.OrganizationType,
		OrganizationID:      dbLead.OrganizationID,
		GivenName:           dbLead.GivenName,
		FamilyName:          dbLead.FamilyName,
		Type:                dbLead.Type,
		State:               customerpb.Lead_State(dbLead.State),
		CustomFields:        dbLead.CustomFields,
		LifeCycleID:         dbLead.LifeCycleID,
		OwnerStaffID:        dbLead.OwnerStaffID,
		ActionStateID:       dbLead.ActionStateID,
		AvatarPath:          dbLead.AvatarPath,
		CustomerCode:        dbLead.CustomerCode,
		ReferralSourceID:    dbLead.ReferralSourceID,
		ConvertToCustomerID: dbLead.ConvertToCustomerID,
		DeletedTime:         dbLead.DeletedTime,
		CreatedTime:         dbLead.CreatedTime,
		UpdatedTime:         dbLead.UpdatedTime,
	}
}
