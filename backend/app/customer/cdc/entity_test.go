package cdc

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/bytedance/sonic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFlexibleTime_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name        string
		jsonData    string
		expected    *time.Time
		expectError bool
	}{
		{
			name:     "毫秒级时间戳",
			jsonData: "**********000",                   // 真正的毫秒级时间戳
			expected: timePtr(time.Unix(**********, 0)), // **********000毫秒 = **********秒
		},
		{
			name:     "秒级时间戳",
			jsonData: "**********",
			expected: timePtr(time.Unix(**********, 0)),
		},
		{
			name:     "RFC3339格式",
			jsonData: `"2025-01-15T10:30:45Z"`,
			expected: timePtr(time.Date(2025, 1, 15, 10, 30, 45, 0, time.UTC)),
		},
		{
			name:     "常见日期时间格式",
			jsonData: `"2025-01-15 10:30:45"`,
			expected: timePtr(time.Date(2025, 1, 15, 10, 30, 45, 0, time.UTC)),
		},
		{
			name:     "日期格式",
			jsonData: `"2025-01-15"`,
			expected: timePtr(time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC)),
		},
		{
			name:     "RFC3339Nano格式",
			jsonData: `"2025-01-15T10:30:45.123456789Z"`,
			expected: timePtr(time.Date(2025, 1, 15, 10, 30, 45, 123456789, time.UTC)),
		},
		{
			name:     "Unix时间戳字符串(毫秒)",
			jsonData: `"**********000"`,
			expected: timePtr(time.Unix(**********, 0)),
		},
		{
			name:     "Unix时间戳字符串(秒)",
			jsonData: `"**********"`,
			expected: timePtr(time.Unix(**********, 0)),
		},
		{
			name:     "null值",
			jsonData: "null",
			expected: nil,
		},
		{
			name:     "空字符串",
			jsonData: `""`,
			expected: nil,
		},
		{
			name:        "无效格式",
			jsonData:    `"invalid-date"`,
			expected:    nil,
			expectError: true,
		},
		{
			name:        "负数",
			jsonData:    `"-283651200000"`,
			expected:    timePtr(time.Date(1961, 1, 5, 0, 0, 0, 0, time.UTC)),
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var ft FlexibleTime
			err := json.Unmarshal([]byte(tt.jsonData), &ft)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)

			if tt.expected == nil {
				assert.Nil(t, ft.Time)
			} else {
				require.NotNil(t, ft.Time)
				assert.True(t, tt.expected.Equal(*ft.Time),
					"Expected %v, got %v", tt.expected.Format(time.RFC3339Nano), ft.Time.Format(time.RFC3339Nano))
			}
		})
	}
}

func TestFlexibleTime_MarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		ft       FlexibleTime
		expected string
	}{
		{
			name:     "正常时间",
			ft:       FlexibleTime{Time: timePtr(time.Date(2025, 1, 15, 10, 30, 45, 0, time.UTC))},
			expected: `"2025-01-15T10:30:45Z"`,
		},
		{
			name:     "null时间",
			ft:       FlexibleTime{Time: nil},
			expected: "null",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := json.Marshal(tt.ft)
			require.NoError(t, err)
			assert.JSONEq(t, tt.expected, string(data))
		})
	}
}

func TestCustomerRow_UnmarshalJSON_WithSonic(t *testing.T) {
	// 测试完整的 CustomerMessage unmarshal
	testJSON := `{
		"before": {
			"id": 11280968,
			"business_id": 102260,
			"avatar_path": "",
			"email": "<EMAIL>",
			"first_name": "Test",
			"last_name": "User",
			"status": 1,
			"inactive": 0,
			"client_color": "#000000",
			"is_block_message": 0,
			"is_block_online_booking": 0,
			"login_email": "",
			"referral_source_id": 0,
			"referral_source_desc": "",
			"send_auto_email": 0,
			"send_auto_message": 1,
			"send_app_auto_message": 1,
			"unconfirmed_reminder_by": 1,
			"preferred_groomer_id": 0,
			"preferred_frequency_day": 28,
			"preferred_frequency_type": 1,
			"last_service_time": "2025-06-21",
			"source": "unknown",
			"external_id": "",
			"create_time": **********,
			"update_time": **********,
			"create_by": 0,
			"update_by": 0,
			"is_recurring": null,
			"share_appt_status": 0,
			"share_range_type": 0,
			"share_range_value": 0,
			"share_appt_json": null,
			"preferred_day": "[0, 1, 2, 3, 4, 5, 6]",
			"preferred_time": "[0, 1435]",
			"account_id": 0,
			"customer_code": "BOF7BCE4",
			"is_unsubscribed": 0,
			"company_id": 102188,
			"birthday": ************,
			"type": "CUSTOMER",
			"life_cycle": "LIFE_CYCLE_UNSPECIFIED",
			"action_state": "ACTION_STATE_UNSPECIFIED",
			"allocate_staff_id": 0,
			"customize_life_cycle_id": 0,
			"customize_action_state_id": 0
		},
		"after": null,
		"source": {
			"version": "1.9.8.Final",
			"connector": "mysql",
			"name": "mysql_binlog_source",
			"ts_ms": **********000,
			"snapshot": "false",
			"db": "moe_customer",
			"table": "moe_business_customer"
		},
		"op": "u",
		"ts_ms": *************
	}`

	var cdcMsg CustomerMessage
	err := sonic.Unmarshal([]byte(testJSON), &cdcMsg)
	require.NoError(t, err)

	// 验证基本字段
	assert.Equal(t, "u", cdcMsg.Op)
	assert.Equal(t, int64(*************), cdcMsg.TSMs)
	assert.NotNil(t, cdcMsg.Before)
	assert.Nil(t, cdcMsg.After)

	// 验证 birthday 字段
	require.NotNil(t, cdcMsg.Before.Birthday)
	require.NotNil(t, cdcMsg.Before.Birthday.Time)

	// ************ 被当作秒级时间戳处理，因为它 < 1e12
	expectedBirthday := time.Unix(************, 0)
	assert.True(t, expectedBirthday.Equal(*cdcMsg.Before.Birthday.Time),
		"Expected %v, got %v", expectedBirthday.Format(time.RFC3339), cdcMsg.Before.Birthday.Time.Format(time.RFC3339))

	// 验证其他字段
	assert.Equal(t, uint64(11280968), cdcMsg.Before.ID)
	assert.Equal(t, int64(102260), cdcMsg.Before.BusinessID)
	assert.Equal(t, "Test", cdcMsg.Before.FirstName)
	assert.Equal(t, "User", cdcMsg.Before.LastName)
}

func TestCustomerRow_UnmarshalJSON_WithDifferentBirthdayFormats(t *testing.T) {
	baseJSON := `{
		"id": 1,
		"business_id": 100,
		"first_name": "Test",
		"last_name": "User",
		"birthday": %s
	}`

	tests := []struct {
		name         string
		birthdayJSON string
		expectedTime *time.Time
	}{
		{
			name:         "毫秒时间戳",
			birthdayJSON: "**********000", // 真正的毫秒级时间戳
			expectedTime: timePtr(time.Unix(**********, 0)),
		},
		{
			name:         "秒时间戳",
			birthdayJSON: "**********",
			expectedTime: timePtr(time.Unix(**********, 0)),
		},
		{
			name:         "ISO日期字符串",
			birthdayJSON: `"2025-01-15T10:30:45Z"`,
			expectedTime: timePtr(time.Date(2025, 1, 15, 10, 30, 45, 0, time.UTC)),
		},
		{
			name:         "日期字符串",
			birthdayJSON: `"2025-01-15"`,
			expectedTime: timePtr(time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC)),
		},
		{
			name:         "null值",
			birthdayJSON: "null",
			expectedTime: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonStr := fmt.Sprintf(baseJSON, tt.birthdayJSON)

			var row CustomerRow
			err := json.Unmarshal([]byte(jsonStr), &row)
			require.NoError(t, err)

			if tt.expectedTime == nil {
				assert.Nil(t, row.Birthday)
			} else {
				require.NotNil(t, row.Birthday)
				require.NotNil(t, row.Birthday.Time)
				assert.True(t, tt.expectedTime.Equal(*row.Birthday.Time))
			}
		})
	}
}

func TestFlexibleTime_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		jsonData    string
		expected    *time.Time
		expectError bool
	}{
		{
			name:        "无效的JSON",
			jsonData:    "invalid-json",
			expectError: true,
		},
		{
			name:        "无效的数字",
			jsonData:    "abc123",
			expectError: true,
		},
		{
			name:        "空数组",
			jsonData:    "[]",
			expectError: true,
		},
		{
			name:        "空对象",
			jsonData:    "{}",
			expectError: true,
		},
		{
			name:     "零值时间戳",
			jsonData: "0",
			expected: timePtr(time.Unix(0, 0)),
		},
		{
			name:     "负数时间戳",
			jsonData: "-1000",
			expected: timePtr(time.Unix(-1000, 0)), // -1000秒，不是-1秒
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var ft FlexibleTime
			err := json.Unmarshal([]byte(tt.jsonData), &ft)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)

			if tt.expected == nil {
				assert.Nil(t, ft.Time)
			} else {
				require.NotNil(t, ft.Time)
				assert.True(t, tt.expected.Equal(*ft.Time))
			}
		})
	}
}

// 基准测试
func BenchmarkFlexibleTime_UnmarshalJSON_Timestamp(b *testing.B) {
	jsonData := []byte("************")

	for i := 0; i < b.N; i++ {
		var ft FlexibleTime
		_ = json.Unmarshal(jsonData, &ft)
	}
}

func BenchmarkFlexibleTime_UnmarshalJSON_String(b *testing.B) {
	jsonData := []byte(`"2025-01-15T10:30:45Z"`)

	for i := 0; i < b.N; i++ {
		var ft FlexibleTime
		_ = json.Unmarshal(jsonData, &ft)
	}
}

func BenchmarkCustomerMessage_UnmarshalJSON_Sonic(b *testing.B) {
	testJSON := []byte(`{
		"before": {
			"id": 11280968,
			"business_id": 102260,
			"first_name": "Test",
			"last_name": "User",
			"birthday": ************,
			"create_time": **********,
			"update_time": **********
		},
		"op": "u",
		"ts_ms": *************
	}`)

	for i := 0; i < b.N; i++ {
		var cdcMsg CustomerMessage
		_ = sonic.Unmarshal(testJSON, &cdcMsg)
	}
}

// timePtr 辅助函数用于创建时间指针
func timePtr(t time.Time) *time.Time {
	return &t
}
