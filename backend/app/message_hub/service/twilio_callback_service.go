package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/message_hub/logic"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

type TwilioCallbackService struct {
	messagehubpb.UnimplementedTwilioCallbackServiceServer
	sms *logic.SmsLogic
}

func NewTwilioCallbackService() *TwilioCallbackService {
	return &TwilioCallbackService{
		sms: logic.NewSmsLogic(),
	}
}

func (s *TwilioCallbackService) HandleSmsStatusCallback(ctx context.Context,
	req *messagehubpb.HandleSmsStatusCallbackRequest) (*messagehubpb.HandleSmsStatusCallbackResponse, error) {
	err := s.sms.HandleSmsStatusCallback(ctx, req)
	if err != nil {
		return nil, err
	}

	return &messagehubpb.HandleSmsStatusCallbackResponse{}, nil
}
