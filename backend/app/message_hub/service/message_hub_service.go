package service

import (
	"context"

	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/app/message_hub/logic"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

type MessageHub struct {
	messagehubpb.UnimplementedMessageHubServiceServer
	sms  *logic.SmsLogic
	call *logic.CallLogic
}

func NewMessageHub() *MessageHub {
	return &MessageHub{
		sms:  logic.NewSmsLogic(),
		call: logic.NewCallLogic(),
	}
}

func (g MessageHub) SendMessage(ctx context.Context, req *messagehubpb.SendMessageRequest) (
	*messagehubpb.SendMessageResponse, error) {
	var resp *messagehubpb.SendMessageResponse
	var err error
	switch req.Payload.(type) {
	case *messagehubpb.SendMessageRequest_Sms:
		resp, err = g.sms.Send(ctx, req.GetSms())
	case *messagehubpb.SendMessageRequest_Call:
		resp, err = g.call.Send(ctx, req.GetCall())
	default:
		err = errs.New(codes.InvalidArgument)
	}

	return resp, err
}
