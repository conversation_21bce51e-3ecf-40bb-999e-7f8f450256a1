package messagehubutils

// message providers
const (
	MessageProviderTwilio = "twilio"
	MessageProviderAWS    = "aws"
)

// message directions
const (
	MessageDirectionInbound  = "inbound"
	MessageDirectionOutbound = "outbound"
)

// message types
const (
	MessageTypeSMS   = "sms"
	MessageTypeMMS   = "mms"
	MessageTypeCall  = "call"
	MessageTypeEmail = "email"
)

// message callback types
const (
	MessageCallbackTypeKafka = "kafka"
	MessageCallbackTypeGrpc  = "grpc"
)

// error messages
//
//nolint:lll
var (
	TwilioErrorMessage = map[string]string{
		"30003": "The destination handset you are trying to reach is switched off, or doesn't have sufficient signal. Please try again later.",
		"30005": "The destination number you are trying to reach is unknown and may no longer exist, or belongs to a landline. Please check the number and try again later.",
		"30006": "This message was sent to a landline, or an unreachable carrier. Please try again or contact support to investigate.",
		"30007": "This message was blocked by carrier as it contains sensitive terms violating the A2P communication regulation. Please revise or rephrase your message and resend.",
		"30008": "This message was undelivered due to an unknown error. Please try again or contact support to investigate.",
		"30024": "This message was undelivered due to carrier registration latency. Please try again after 10 minutes. Contact support if the issue persists.",
		"30034": "This message was undelivered due to carrier registration latency. Contact support if the issue persists.",
	}
)
