-- 消息主表，只插入，不更新
create table message (
    id text,
    type text,
    sender text,
    recipient text,
    direction text,
    provider text,
    content text,
    callbacks jsonb,
    created_at timestamp with time zone default current_timestamp,
    primary key (id, type)
) PARTITION BY LIST (type);

comment on table message is 'Main message table, insert only, no updates';
comment on column message.id is 'Unique identifier for the message';
comment on column message.type is 'Type of the message (e.g., sms, call, email), also used as partition key';
comment on column message.sender is 'Sender of the message';
comment on column message.recipient is 'Recipient of the message';
comment on column message.direction is 'Direction of the message (e.g., inbound, outbound)';
comment on column message.provider is 'Provider used to send the message';
comment on column message.content is 'The text content of the message';
comment on column message.callbacks is 'Parameters associated with the callback';
comment on column message.created_at is 'Timestamp when the message was created';

-- sms 消息分区表
CREATE TABLE message_sms PARTITION OF message FOR VALUES IN ('sms');
create index idx_message_sms_sender on message_sms (sender);
create index idx_message_sms_recipient on message_sms (recipient);
create index idx_message_sms_created_at on message_sms (created_at);
comment on index idx_message_sms_sender is 'Index on the sender for quick lookups.';
comment on index idx_message_sms_recipient is 'Index on the recipient for quick lookups.';
comment on index idx_message_sms_created_at is 'Index on the creation timestamp to efficiently query messages by time.';

-- call 消息分区表
CREATE TABLE message_call PARTITION OF message FOR VALUES IN ('call');
create index idx_message_call_sender on message_call (sender);
create index idx_message_call_recipient on message_call (recipient);
create index idx_message_call_created_at on message_call (created_at);
comment on index idx_message_call_sender is 'Index on the sender for quick lookups.';
comment on index idx_message_call_recipient is 'Index on the recipient for quick lookups.';
comment on index idx_message_call_created_at is 'Index on the creation timestamp to efficiently query messages by time.';

-- CREATE TABLE message_email PARTITION OF message FOR VALUES IN ('email');

-- 消息最新状态表，id 与主表相同
create table message_state (
   id text,
   type text,
   state text,
   error_code text,
   error_message text,
   extra jsonb,
   created_at timestamp with time zone default current_timestamp,
   updated_at timestamp with time zone default current_timestamp,
   primary key (id, type)
) PARTITION BY LIST (type);

comment on table message_state is 'Stores the latest state of a message. The id is the same as in the message table.';
comment on column message_state.id is 'Message ID, foreign key to message.id';
comment on column message_state.state is 'The latest state of the message (e.g., sent, delivered, failed)';
comment on column message_state.error_code is 'Error code if the message failed to send';
comment on column message_state.error_message is 'Error message providing details about a failure';
comment on column message_state.extra is 'Additional information about the message state, such as twilio sid';
comment on column message_state.created_at is 'Timestamp when the state was created';
comment on column message_state.updated_at is 'Timestamp when the state was last updated';

CREATE TABLE message_state_sms PARTITION OF message_state FOR VALUES IN ('sms');
create index idx_message_state_sms_state on message_state_sms (state);

CREATE TABLE message_state_call PARTITION OF message_state FOR VALUES IN ('call');
create index idx_message_state_call_state on message_state_call (state);


create table platform_number (
    id text primary key,
    country text,
    number text,
    provider text,
    extra jsonb,
    created_at timestamp with time zone default current_timestamp,
    deleted_at timestamp with time zone default current_timestamp
);

comment on table platform_number is 'Official number of MoeGo platform';
comment on column platform_number.id is 'Primary key, unique identifier for a platform number record';
comment on column platform_number.country is 'Country code, e.g., US, CA';
comment on column platform_number.number is 'Phone number';
comment on column platform_number.provider is 'Service provider of the number';
comment on column platform_number.extra is 'Additional information in JSON format';
comment on column platform_number.created_at is 'Timestamp when the record was created';
comment on column platform_number.deleted_at is 'Timestamp when the record was deleted, default if not deleted';
