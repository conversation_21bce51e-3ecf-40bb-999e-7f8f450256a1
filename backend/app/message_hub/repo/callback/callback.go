package callback

import (
	"context"
	"fmt"
	"sync"
	"time"

	"golang.org/x/sync/singleflight"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

const (
	// forgetDelayAfterFailure 定义了在连接创建失败后，延迟多久才将 key 从 singleflight 中移除。
	// 这可以防止在下游服务持续不可用时，客户端发起过于频繁的重连尝试（即“重试风暴”）。
	// 相当于一个临时的冷却期。
	forgetDelayAfterFailure = 3 * time.Second
)

var (
	// clients 是一个线程安全的 map，用于缓存已建立的 gRPC 客户端连接。
	// 键是服务器地址 (target)，值是 *grpc.ClientConn。
	clients = &sync.Map{}
	// g 用于确保对同一个 target 的连接创建操作，在并发场景下只执行一次，
	// 防止缓存击穿导致多个 goroutine 重复创建连接。
	g = &singleflight.Group{}
)

// getConn 为指定的目标地址获取一个 gRPC 连接。
// 此函数采用“先检查再协调”的模式，以实现高效的连接复用。
func getConn(target string) (*grpc.ClientConn, error) {
	// 快速路径：首先尝试从缓存中加载连接。
	// 这是最高频的场景，一旦连接建立，后续所有请求都将通过此路径快速返回。
	if conn, ok := clients.Load(target); ok {
		return conn.(*grpc.ClientConn), nil //nolint:errcheck
	}

	// 慢速路径：缓存未命中，需要创建新连接。
	// 使用 singleflight.Do 来确保同一时间只有一个 goroutine 会执行连接创建逻辑。
	// 其他并发请求相同 target 的 goroutine 会在此处等待，并共享第一个成功创建的连接结果。
	conn, err, _ := g.Do(target, func() (interface{}, error) {
		// 在 singleflight 内部，我们确信这段代码对于同一个 target 只会执行一次。
		// 因此，直接创建新连接即可。
		newConn, err := grpc.NewClient(target, grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			// 如果创建连接失败，我们不会立即 Forget。
			// 而是启动一个后台 goroutine，在延迟一段时间后，再执行 Forget 操作。
			// 这样做可以给下游服务恢复的时间，避免无谓的、过于频繁的重试。
			// 同时，主流程会立即返回错误，让调用方快速失败。
			go func() {
				defer func() {
					if r := recover(); r != nil {
						log.Errorf("panic: %v", r)
					}
				}()
				time.Sleep(forgetDelayAfterFailure)
				g.Forget(target)
			}()

			return nil, err
		}
		// 连接创建成功后，将其存入缓存，供后续请求使用。
		clients.Store(target, newConn)

		return newConn, nil
	})

	if err != nil {
		return nil, fmt.Errorf("fail to create gRPC connection，target address %s, %w", target, err)
	}

	// g.Do 返回的是 interface{} 类型，需要进行类型断言。
	return conn.(*grpc.ClientConn), nil //nolint:errcheck
}

func InvokeStateCallback(ctx context.Context, target string, payload *messagehubpb.HandleStateRequest) (
	*messagehubpb.HandleStateResponse, error) {
	conn, err := getConn(target)
	if err != nil {
		return nil, err
	}
	client := messagehubpb.NewCallbackServiceClient(conn)

	return client.HandleState(ctx, payload)
}

func InvokeReplyCallback(ctx context.Context, target string, payload *messagehubpb.HandleReplyRequest) (
	*messagehubpb.HandleReplyResponse, error) {
	conn, err := getConn(target)
	if err != nil {
		return nil, err
	}
	client := messagehubpb.NewCallbackServiceClient(conn)

	return client.HandleReply(ctx, payload)
}
