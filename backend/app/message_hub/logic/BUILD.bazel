load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "logic",
    srcs = [
        "call.go",
        "sms.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/message_hub/logic",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/message_hub/repo/db",
        "//backend/app/message_hub/repo/twilio",
        "//backend/app/message_hub/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/env",
        "//backend/proto/message_hub/v1:message_hub",
        "@com_github_twilio_twilio_go//rest/api/v2010:api",
        "@org_golang_google_grpc//codes",
    ],
)

go_test(
    name = "logic_test",
    srcs = ["sms_test.go"],
    embed = [":logic"],
    deps = [
        "//backend/app/message_hub/repo/db",
        "//backend/app/message_hub/repo/mock/db",
        "//backend/app/message_hub/repo/mock/twilio",
        "//backend/common/utils/pointer",
        "//backend/proto/message_hub/v1:message_hub",
        "@com_github_stretchr_testify//suite",
        "@com_github_twilio_twilio_go//rest/api/v2010:api",
        "@org_golang_google_genproto//googleapis/type/phone_number",
        "@org_uber_go_mock//gomock",
    ],
)
