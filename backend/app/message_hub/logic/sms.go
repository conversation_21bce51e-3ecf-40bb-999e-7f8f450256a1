package logic

import (
	"context"
	"net/url"
	"strconv"
	"time"

	api "github.com/twilio/twilio-go/rest/api/v2010"
	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/app/message_hub/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/message_hub/repo/twilio"
	messagehubutils "github.com/MoeGolibrary/moego/backend/app/message_hub/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/env"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

type SmsLogic struct {
	twilio *twilio.Twilio
	db     db.MessageHub
}

func NewSmsLogic() *SmsLogic {
	return NewSmsLogicByParams(twilio.New(), db.NewMessageHub())
}

func NewSmsLogicByParams(twilio *twilio.Twilio, db db.MessageHub) *SmsLogic {
	return &SmsLogic{
		twilio: twilio,
		db:     db,
	}
}

func (l *SmsLogic) GetStatusCallbackURL(id string) string {
	q := url.Values{}
	q.Set("id", id)

	u := url.URL{
		Scheme:   "https",
		Host:     env.GetDomainHost(env.GO),
		Path:     "/moego.bff/message-hub/handleSmsStatusCallback",
		RawQuery: q.Encode(),
	}

	return u.String()
}

func (l *SmsLogic) Send(ctx context.Context,
	payload *messagehubpb.SendSmsPayload) (*messagehubpb.SendMessageResponse, error) {

	// build message
	msg, err := l.buildMessage(ctx, payload)
	if err != nil {
		return nil, err
	}

	// save to db
	if err := l.db.CreateMessage(ctx, msg); err != nil {
		return nil, err
	}

	// send to twilio TODO: refactor to multiple providers
	sms, err := l.sendToTwilio(ctx, msg)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "Sent SMS with sid: %s", *sms.Sid)

	// save send state
	if err := l.saveSendState(ctx, msg, sms); err != nil {
		return nil, err
	}

	return &messagehubpb.SendMessageResponse{
		MessageId: msg.ID,
	}, nil
}

func (l *SmsLogic) HandleSmsStatusCallback(ctx context.Context,
	req *messagehubpb.HandleSmsStatusCallbackRequest) error {
	log.InfoContextf(ctx, "Received Twilio callback: %+v", req)
	var errorCode *string
	if req.ErrorCode != nil {
		code := strconv.Itoa(int(*req.ErrorCode))
		errorCode = &code
	}
	stateStr := req.GetMessageStatus()
	state := &db.MessageState{
		ID:        req.GetCustom().GetId(),
		Type:      messagehubutils.MessageTypeSMS,
		State:     &stateStr,
		ErrorCode: errorCode,
	}

	return l.db.UpdateMessageState(ctx, state)
}

func (l *SmsLogic) buildMessage(ctx context.Context, payload *messagehubpb.SendSmsPayload) (*db.Message, error) {
	// if sender is empty, use platform number
	sender := payload.GetSender().GetE164Number()
	if len(sender) == 0 {
		number, err := l.getPlatformNumber(ctx, payload.GetRegionCode())
		if err != nil {
			return nil, err
		}
		sender = number.Number
	}

	// set callbacks
	var callbacks *db.Callbacks
	if payload.GetStateCallback() != nil {
		var t string
		var p map[string]any
		switch payload.GetStateCallback().GetMethod().(type) {
		case *messagehubpb.Callback_Kafka:
			{
				t = messagehubutils.MessageCallbackTypeKafka
				p = map[string]any{
					"topic": payload.GetStateCallback().GetKafka().GetTopic(),
				}
			}
		case *messagehubpb.Callback_Grpc:
			{
				t = messagehubutils.MessageCallbackTypeGrpc
				p = map[string]any{
					"server": payload.GetStateCallback().GetGrpc().GetServer(),
				}
			}
		}
		callbacks = &db.Callbacks{
			State: &db.Callback{
				Type:   t,
				Params: p,
			},
		}
	}

	return &db.Message{
		ID:        messagehubutils.NewUUID(),
		Provider:  messagehubutils.MessageProviderTwilio,
		Direction: messagehubutils.MessageDirectionOutbound,
		Type:      messagehubutils.MessageTypeSMS,
		Sender:    sender,
		Recipient: payload.GetRecipient().GetE164Number(),
		Content:   payload.GetContent(),
		Callbacks: callbacks,
	}, nil
}

func (l *SmsLogic) sendToTwilio(ctx context.Context, msg *db.Message) (
	*api.ApiV2010Message, error) {

	params := (&api.CreateMessageParams{}).
		SetBody(msg.Content).
		SetFrom(msg.Sender).
		SetTo(msg.Recipient).
		SetStatusCallback(l.GetStatusCallbackURL(msg.ID))

	log.InfoContextf(ctx, "Sending SMS from %s to %s with content: %s", msg.Sender, msg.Recipient, msg.Content)

	return l.twilio.SendSMS(params)
}

func (l *SmsLogic) saveSendState(ctx context.Context, msg *db.Message, sms *api.ApiV2010Message) error {

	var errorCode *string
	if sms.ErrorCode != nil {
		code := strconv.Itoa(*sms.ErrorCode)
		errorCode = &code
	}

	createdAt := time.Now()
	if sms.DateCreated != nil {
		if d, err := parseRFC2822(*sms.DateCreated); err == nil {
			createdAt = d
		}
	}

	state := &db.MessageState{
		ID:           msg.ID,
		Type:         msg.Type,
		State:        sms.Status,
		ErrorCode:    errorCode,
		ErrorMessage: sms.ErrorMessage,
		ParsedExtra: &db.SmsStateDetails{
			Sid:                 sms.Sid,
			NumSegments:         sms.NumSegments,
			MessagingServiceSid: sms.MessagingServiceSid,
		},
		CreatedAt: createdAt,
	}

	return l.db.CreateMessageState(ctx, state)
}

func (l *SmsLogic) getPlatformNumber(ctx context.Context, country string) (*db.PlatformNumber, error) {
	numbers, err := l.db.ListPlatformNumbers(ctx, country)
	if err != nil {
		return nil, err
	}

	if len(numbers) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "Unsupported country")
	}

	// TODO: 随机选择一个
	return numbers[0], nil
}

func parseRFC2822(s string) (time.Time, error) {
	// Twilio uses RFC 2822 format, e.g., "Mon, 02 Jan 2006 15:04:05 -0700"
	layout := "Mon, 02 Jan 2006 15:04:05 -0700"

	return time.Parse(layout, s)
}
