load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "grpc",
    srcs = [
        "client.go",
        "client_transport.go",
        "codec.go",
        "grpc.go",
        "header.go",
        "json.go",
        "pool.go",
        "server.go",
        "server_transport.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/server",
        "//backend/common/rpc/framework/transport",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//encoding",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//peer",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
    ],
)
