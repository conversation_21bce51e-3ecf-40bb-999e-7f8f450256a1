// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/common.proto

package fulfillmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 排序方式
type SortType int32

const (
	// SORT_TYPE_UNSPECIFIED 默认排序类型
	SortType_SORT_TYPE_UNSPECIFIED SortType = 0
	// SORT_TYPE_START_TIME 按服务开始时间
	SortType_SORT_TYPE_START_TIME SortType = 1
)

// Enum value maps for SortType.
var (
	SortType_name = map[int32]string{
		0: "SORT_TYPE_UNSPECIFIED",
		1: "SORT_TYPE_START_TIME",
	}
	SortType_value = map[string]int32{
		"SORT_TYPE_UNSPECIFIED": 0,
		"SORT_TYPE_START_TIME":  1,
	}
)

func (x SortType) Enum() *SortType {
	p := new(SortType)
	*p = x
	return p
}

func (x SortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[0].Descriptor()
}

func (SortType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[0]
}

func (x SortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortType.Descriptor instead.
func (SortType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{0}
}

// 日期类型
type DateType int32

const (
	// 未指定日期类型
	DateType_DATE_TYPE_UNSPECIFIED DateType = 0
	// 每日日期
	DateType_DATE_TYPE_DATE_EVERYDAY DateType = 1
	// 特定日期
	DateType_DATE_TYPE_SPECIFIC_DATE DateType = 2
	// 每日包含退房日
	DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY DateType = 3
	// 每日排除入住日
	DateType_DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY DateType = 4
	// 最后一天
	DateType_DATE_TYPE_LAST_DAY DateType = 5
	// 第一天
	DateType_DATE_TYPE_FIRST_DAY DateType = 6
)

// Enum value maps for DateType.
var (
	DateType_name = map[int32]string{
		0: "DATE_TYPE_UNSPECIFIED",
		1: "DATE_TYPE_DATE_EVERYDAY",
		2: "DATE_TYPE_SPECIFIC_DATE",
		3: "DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY",
		4: "DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY",
		5: "DATE_TYPE_LAST_DAY",
		6: "DATE_TYPE_FIRST_DAY",
	}
	DateType_value = map[string]int32{
		"DATE_TYPE_UNSPECIFIED":                  0,
		"DATE_TYPE_DATE_EVERYDAY":                1,
		"DATE_TYPE_SPECIFIC_DATE":                2,
		"DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY": 3,
		"DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY":  4,
		"DATE_TYPE_LAST_DAY":                     5,
		"DATE_TYPE_FIRST_DAY":                    6,
	}
)

func (x DateType) Enum() *DateType {
	p := new(DateType)
	*p = x
	return p
}

func (x DateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DateType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[1].Descriptor()
}

func (DateType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[1]
}

func (x DateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DateType.Descriptor instead.
func (DateType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{1}
}

// 服务类型枚举
type ServiceType int32

const (
	// 未指定的服务类型
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	// 服务类型
	ServiceType_SERVICE_TYPE_SERVICE ServiceType = 1
	// 选项类型
	ServiceType_SERVICE_TYPE_OPTION ServiceType = 2
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE_TYPE_SERVICE",
		2: "SERVICE_TYPE_OPTION",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED": 0,
		"SERVICE_TYPE_SERVICE":     1,
		"SERVICE_TYPE_OPTION":      2,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[2].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[2]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{2}
}

// 覆盖类型枚举，用于定义不同的覆盖策略
type OverrideType int32

const (
	// 未指定
	OverrideType_OVERRIDE_TYPE_UNSPECIFIED OverrideType = 0
	// 覆盖类型 1 - override by location (business)
	OverrideType_OVERRIDE_TYPE_LOCATION OverrideType = 1
	// override by pet (client)
	OverrideType_OVERRIDE_TYPE_PET OverrideType = 2
	// override by staff
	OverrideType_OVERRIDE_TYPE_STAFF OverrideType = 3
)

// Enum value maps for OverrideType.
var (
	OverrideType_name = map[int32]string{
		0: "OVERRIDE_TYPE_UNSPECIFIED",
		1: "OVERRIDE_TYPE_LOCATION",
		2: "OVERRIDE_TYPE_PET",
		3: "OVERRIDE_TYPE_STAFF",
	}
	OverrideType_value = map[string]int32{
		"OVERRIDE_TYPE_UNSPECIFIED": 0,
		"OVERRIDE_TYPE_LOCATION":    1,
		"OVERRIDE_TYPE_PET":         2,
		"OVERRIDE_TYPE_STAFF":       3,
	}
)

func (x OverrideType) Enum() *OverrideType {
	p := new(OverrideType)
	*p = x
	return p
}

func (x OverrideType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OverrideType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[3].Descriptor()
}

func (OverrideType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[3]
}

func (x OverrideType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OverrideType.Descriptor instead.
func (OverrideType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{3}
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 不合法的参数
	ErrCode_ERR_CODE_INVALID ErrCode = 122000
	// ------ fulfillment report error code ------
	ErrCode_ERR_CODE_SEND_REPORT ErrCode = 123001
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		122000: "ERR_CODE_INVALID",
		123001: "ERR_CODE_SEND_REPORT",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_INVALID":     122000,
		"ERR_CODE_SEND_REPORT": 123001,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_common_proto_enumTypes[4].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_common_proto_enumTypes[4]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{4}
}

// 分页信息
type PaginationRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 偏移量，默认0
	Offset int32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 每页数量，默认200
	Limit         int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRef) Reset() {
	*x = PaginationRef{}
	mi := &file_backend_proto_fulfillment_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRef) ProtoMessage() {}

func (x *PaginationRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRef.ProtoReflect.Descriptor instead.
func (*PaginationRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *PaginationRef) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PaginationRef) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

var File_backend_proto_fulfillment_v1_common_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_common_proto_rawDesc = "" +
	"\n" +
	")backend/proto/fulfillment/v1/common.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"=\n" +
	"\rPaginationRef\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x05R\x06offset\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit*?\n" +
	"\bSortType\x12\x19\n" +
	"\x15SORT_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14SORT_TYPE_START_TIME\x10\x01*\xe7\x01\n" +
	"\bDateType\x12\x19\n" +
	"\x15DATE_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17DATE_TYPE_DATE_EVERYDAY\x10\x01\x12\x1b\n" +
	"\x17DATE_TYPE_SPECIFIC_DATE\x10\x02\x12*\n" +
	"&DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY\x10\x03\x12)\n" +
	"%DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY\x10\x04\x12\x16\n" +
	"\x12DATE_TYPE_LAST_DAY\x10\x05\x12\x17\n" +
	"\x13DATE_TYPE_FIRST_DAY\x10\x06*^\n" +
	"\vServiceType\x12\x1c\n" +
	"\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14SERVICE_TYPE_SERVICE\x10\x01\x12\x17\n" +
	"\x13SERVICE_TYPE_OPTION\x10\x02*y\n" +
	"\fOverrideType\x12\x1d\n" +
	"\x19OVERRIDE_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16OVERRIDE_TYPE_LOCATION\x10\x01\x12\x15\n" +
	"\x11OVERRIDE_TYPE_PET\x10\x02\x12\x17\n" +
	"\x13OVERRIDE_TYPE_STAFF\x10\x03*N\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x16\n" +
	"\x10ERR_CODE_INVALID\x10\x90\xb9\a\x12\x1a\n" +
	"\x14ERR_CODE_SEND_REPORT\x10\xf9\xc0\aBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_common_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_common_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_common_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_common_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_common_proto_rawDesc), len(file_backend_proto_fulfillment_v1_common_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_common_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_backend_proto_fulfillment_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_fulfillment_v1_common_proto_goTypes = []any{
	(SortType)(0),         // 0: backend.proto.fulfillment.v1.SortType
	(DateType)(0),         // 1: backend.proto.fulfillment.v1.DateType
	(ServiceType)(0),      // 2: backend.proto.fulfillment.v1.ServiceType
	(OverrideType)(0),     // 3: backend.proto.fulfillment.v1.OverrideType
	(ErrCode)(0),          // 4: backend.proto.fulfillment.v1.ErrCode
	(*PaginationRef)(nil), // 5: backend.proto.fulfillment.v1.PaginationRef
}
var file_backend_proto_fulfillment_v1_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_common_proto_init() }
func file_backend_proto_fulfillment_v1_common_proto_init() {
	if File_backend_proto_fulfillment_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_common_proto_rawDesc), len(file_backend_proto_fulfillment_v1_common_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_common_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_common_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_common_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_common_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_common_proto = out.File
	file_backend_proto_fulfillment_v1_common_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_common_proto_depIdxs = nil
}
