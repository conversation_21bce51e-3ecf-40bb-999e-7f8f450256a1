// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/calendar.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DailyCapacityEntry 每日容量条目
type DailyCapacityEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 日期 (格式: YYYY-MM-DD)
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// 使用的容量
	Used          int32 `protobuf:"varint,2,opt,name=used,proto3" json:"used,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DailyCapacityEntry) Reset() {
	*x = DailyCapacityEntry{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DailyCapacityEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyCapacityEntry) ProtoMessage() {}

func (x *DailyCapacityEntry) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyCapacityEntry.ProtoReflect.Descriptor instead.
func (*DailyCapacityEntry) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{0}
}

func (x *DailyCapacityEntry) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *DailyCapacityEntry) GetUsed() int32 {
	if x != nil {
		return x.Used
	}
	return 0
}

// LodgingType 住宿信息
type LodgingType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 住宿类型
	UnitType int32 `protobuf:"varint,2,opt,name=unit_type,json=unitType,proto3" json:"unit_type,omitempty"`
	// 住宿名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 最大宠物数量
	MaxPetNumber int32 `protobuf:"varint,4,opt,name=max_pet_number,json=maxPetNumber,proto3" json:"max_pet_number,omitempty"`
	// 最大宠物总数
	MaxPetTotalNumber int32 `protobuf:"varint,5,opt,name=max_pet_total_number,json=maxPetTotalNumber,proto3" json:"max_pet_total_number,omitempty"`
	// 每日容量使用情况 (按日期排序)
	DailyCapacityUsed []*DailyCapacityEntry `protobuf:"bytes,6,rep,name=daily_capacity_used,json=dailyCapacityUsed,proto3" json:"daily_capacity_used,omitempty"`
	// 是否需要宠物大小过滤
	NeedPetSizeFilter bool `protobuf:"varint,7,opt,name=need_pet_size_filter,json=needPetSizeFilter,proto3" json:"need_pet_size_filter,omitempty"`
	// 宠物大小ID
	PetSizeIds []int64 `protobuf:"varint,8,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// 总容量
	TotalCapacity int32 `protobuf:"varint,9,opt,name=total_capacity,json=totalCapacity,proto3" json:"total_capacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LodgingType) Reset() {
	*x = LodgingType{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingType) ProtoMessage() {}

func (x *LodgingType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingType.ProtoReflect.Descriptor instead.
func (*LodgingType) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{1}
}

func (x *LodgingType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingType) GetUnitType() int32 {
	if x != nil {
		return x.UnitType
	}
	return 0
}

func (x *LodgingType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingType) GetMaxPetNumber() int32 {
	if x != nil {
		return x.MaxPetNumber
	}
	return 0
}

func (x *LodgingType) GetMaxPetTotalNumber() int32 {
	if x != nil {
		return x.MaxPetTotalNumber
	}
	return 0
}

func (x *LodgingType) GetDailyCapacityUsed() []*DailyCapacityEntry {
	if x != nil {
		return x.DailyCapacityUsed
	}
	return nil
}

func (x *LodgingType) GetNeedPetSizeFilter() bool {
	if x != nil {
		return x.NeedPetSizeFilter
	}
	return false
}

func (x *LodgingType) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *LodgingType) GetTotalCapacity() int32 {
	if x != nil {
		return x.TotalCapacity
	}
	return 0
}

// LodgingUnit 住宿单元
type LodgingUnit struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿单元ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 住宿单元名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 住宿类型ID
	LodgingTypeId int64 `protobuf:"varint,3,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LodgingUnit) Reset() {
	*x = LodgingUnit{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnit) ProtoMessage() {}

func (x *LodgingUnit) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnit.ProtoReflect.Descriptor instead.
func (*LodgingUnit) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{2}
}

func (x *LodgingUnit) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUnit) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingUnit) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

// LodgingAppointmentInfo 住宿预约信息
type LodgingAppointmentInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,3,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,6,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	// 住宿单元ID
	LodgingUnitId int64 `protobuf:"varint,7,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
	// 住宿类型ID
	LodgingTypeId int64 `protobuf:"varint,8,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LodgingAppointmentInfo) Reset() {
	*x = LodgingAppointmentInfo{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingAppointmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAppointmentInfo) ProtoMessage() {}

func (x *LodgingAppointmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAppointmentInfo.ProtoReflect.Descriptor instead.
func (*LodgingAppointmentInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{3}
}

func (x *LodgingAppointmentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingAppointmentInfo) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *LodgingAppointmentInfo) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *LodgingAppointmentInfo) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *LodgingAppointmentInfo) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *LodgingAppointmentInfo) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

func (x *LodgingAppointmentInfo) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

func (x *LodgingAppointmentInfo) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

// LodgingFilter 住宿过滤条件
type LodgingFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿类型ID列表
	LodgingTypeIds []int64 `protobuf:"varint,1,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LodgingFilter) Reset() {
	*x = LodgingFilter{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingFilter) ProtoMessage() {}

func (x *LodgingFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingFilter.ProtoReflect.Descriptor instead.
func (*LodgingFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{4}
}

func (x *LodgingFilter) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// PetInfo 宠物信息
type PetInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 宠物头像路径
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: avatar_path is clear and descriptive --)
	AvatarPath string `protobuf:"bytes,2,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// 宠物名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 宠物类型
	Type          int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetInfo) Reset() {
	*x = PetInfo{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetInfo) ProtoMessage() {}

func (x *PetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetInfo.ProtoReflect.Descriptor instead.
func (*PetInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{5}
}

func (x *PetInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetInfo) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *PetInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// DailyPetCount 每日宠物数量统计
type DailyPetCount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 日期（YYYY-MM-DD格式）
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: string date format is appropriate for daily statistics --)
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// 按care type分组的宠物数量统计
	// key: care_type_id (int64)
	// value: 该care type的宠物数量 (int32)
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	PetCareTypeCount map[int64]int32 `protobuf:"bytes,2,rep,name=pet_care_type_count,json=petCareTypeCount,proto3" json:"pet_care_type_count,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// 需要住宿的宠物数量
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: pet_count_with_lodging is clear and descriptive --)
	PetCountWithLodging int32 `protobuf:"varint,3,opt,name=pet_count_with_lodging,json=petCountWithLodging,proto3" json:"pet_count_with_lodging,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *DailyPetCount) Reset() {
	*x = DailyPetCount{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DailyPetCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyPetCount) ProtoMessage() {}

func (x *DailyPetCount) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyPetCount.ProtoReflect.Descriptor instead.
func (*DailyPetCount) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{6}
}

func (x *DailyPetCount) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *DailyPetCount) GetPetCareTypeCount() map[int64]int32 {
	if x != nil {
		return x.PetCareTypeCount
	}
	return nil
}

func (x *DailyPetCount) GetPetCountWithLodging() int32 {
	if x != nil {
		return x.PetCountWithLodging
	}
	return 0
}

var File_backend_proto_fulfillment_v1_calendar_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_calendar_proto_rawDesc = "" +
	"\n" +
	"+backend/proto/fulfillment/v1/calendar.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a'backend/proto/offering/v1/service.proto\"<\n" +
	"\x12DailyCapacityEntry\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12\x12\n" +
	"\x04used\x18\x02 \x01(\x05R\x04used\"\x81\x03\n" +
	"\vLodgingType\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tunit_type\x18\x02 \x01(\x05R\bunitType\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12$\n" +
	"\x0emax_pet_number\x18\x04 \x01(\x05R\fmaxPetNumber\x12/\n" +
	"\x14max_pet_total_number\x18\x05 \x01(\x05R\x11maxPetTotalNumber\x12`\n" +
	"\x13daily_capacity_used\x18\x06 \x03(\v20.backend.proto.fulfillment.v1.DailyCapacityEntryR\x11dailyCapacityUsed\x12/\n" +
	"\x14need_pet_size_filter\x18\a \x01(\bR\x11needPetSizeFilter\x12 \n" +
	"\fpet_size_ids\x18\b \x03(\x03R\n" +
	"petSizeIds\x12%\n" +
	"\x0etotal_capacity\x18\t \x01(\x05R\rtotalCapacity\"Y\n" +
	"\vLodgingUnit\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12&\n" +
	"\x0flodging_type_id\x18\x03 \x01(\x03R\rlodgingTypeId\"\xda\x02\n" +
	"\x16LodgingAppointmentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12\x1d\n" +
	"\n" +
	"color_code\x18\x03 \x01(\tR\tcolorCode\x129\n" +
	"\n" +
	"start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12.\n" +
	"\x13service_instance_id\x18\x06 \x01(\x03R\x11serviceInstanceId\x12&\n" +
	"\x0flodging_unit_id\x18\a \x01(\x03R\rlodgingUnitId\x12&\n" +
	"\x0flodging_type_id\x18\b \x01(\x03R\rlodgingTypeId\"K\n" +
	"\rLodgingFilter\x12:\n" +
	"\x10lodging_type_ids\x18\x01 \x03(\x03B\x10\xbaH\r\x92\x01\n" +
	"\x10d\x18\x01\"\x04\"\x02(\x00R\x0elodgingTypeIds\"b\n" +
	"\aPetInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vavatar_path\x18\x02 \x01(\tR\n" +
	"avatarPath\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x04 \x01(\x05R\x04type\"\x8f\x02\n" +
	"\rDailyPetCount\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12p\n" +
	"\x13pet_care_type_count\x18\x02 \x03(\v2A.backend.proto.fulfillment.v1.DailyPetCount.PetCareTypeCountEntryR\x10petCareTypeCount\x123\n" +
	"\x16pet_count_with_lodging\x18\x03 \x01(\x05R\x13petCountWithLodging\x1aC\n" +
	"\x15PetCareTypeCountEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01Bt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_calendar_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_calendar_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_calendar_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_calendar_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_calendar_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_backend_proto_fulfillment_v1_calendar_proto_goTypes = []any{
	(*DailyCapacityEntry)(nil),     // 0: backend.proto.fulfillment.v1.DailyCapacityEntry
	(*LodgingType)(nil),            // 1: backend.proto.fulfillment.v1.LodgingType
	(*LodgingUnit)(nil),            // 2: backend.proto.fulfillment.v1.LodgingUnit
	(*LodgingAppointmentInfo)(nil), // 3: backend.proto.fulfillment.v1.LodgingAppointmentInfo
	(*LodgingFilter)(nil),          // 4: backend.proto.fulfillment.v1.LodgingFilter
	(*PetInfo)(nil),                // 5: backend.proto.fulfillment.v1.PetInfo
	(*DailyPetCount)(nil),          // 6: backend.proto.fulfillment.v1.DailyPetCount
	nil,                            // 7: backend.proto.fulfillment.v1.DailyPetCount.PetCareTypeCountEntry
	(*timestamppb.Timestamp)(nil),  // 8: google.protobuf.Timestamp
}
var file_backend_proto_fulfillment_v1_calendar_proto_depIdxs = []int32{
	0, // 0: backend.proto.fulfillment.v1.LodgingType.daily_capacity_used:type_name -> backend.proto.fulfillment.v1.DailyCapacityEntry
	8, // 1: backend.proto.fulfillment.v1.LodgingAppointmentInfo.start_time:type_name -> google.protobuf.Timestamp
	8, // 2: backend.proto.fulfillment.v1.LodgingAppointmentInfo.end_time:type_name -> google.protobuf.Timestamp
	7, // 3: backend.proto.fulfillment.v1.DailyPetCount.pet_care_type_count:type_name -> backend.proto.fulfillment.v1.DailyPetCount.PetCareTypeCountEntry
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_calendar_proto_init() }
func file_backend_proto_fulfillment_v1_calendar_proto_init() {
	if File_backend_proto_fulfillment_v1_calendar_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_calendar_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_calendar_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_calendar_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_calendar_proto = out.File
	file_backend_proto_fulfillment_v1_calendar_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_calendar_proto_depIdxs = nil
}
