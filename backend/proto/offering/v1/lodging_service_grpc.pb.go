// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/v1/lodging_service.proto

package offeringpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LodgingService_GetLodgingTypeList_FullMethodName = "/backend.proto.offering.v1.LodgingService/GetLodgingTypeList"
	LodgingService_ListLodgingUnit_FullMethodName    = "/backend.proto.offering.v1.LodgingService/ListLodgingUnit"
)

// LodgingServiceClient is the client API for LodgingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// LodgingService 住宿服务
type LodgingServiceClient interface {
	// GetLodgingTypeList 获取住宿类型列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetLodgingTypeListResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: company_id is clear and descriptive --)
	GetLodgingTypeList(ctx context.Context, in *GetLodgingTypeListRequest, opts ...grpc.CallOption) (*GetLodgingTypeListResponse, error)
	// ListLodgingUnit 获取住宿单元列表
	ListLodgingUnit(ctx context.Context, in *ListLodgingUnitRequest, opts ...grpc.CallOption) (*ListLodgingUnitResponse, error)
}

type lodgingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLodgingServiceClient(cc grpc.ClientConnInterface) LodgingServiceClient {
	return &lodgingServiceClient{cc}
}

func (c *lodgingServiceClient) GetLodgingTypeList(ctx context.Context, in *GetLodgingTypeListRequest, opts ...grpc.CallOption) (*GetLodgingTypeListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLodgingTypeListResponse)
	err := c.cc.Invoke(ctx, LodgingService_GetLodgingTypeList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingServiceClient) ListLodgingUnit(ctx context.Context, in *ListLodgingUnitRequest, opts ...grpc.CallOption) (*ListLodgingUnitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListLodgingUnitResponse)
	err := c.cc.Invoke(ctx, LodgingService_ListLodgingUnit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LodgingServiceServer is the server API for LodgingService service.
// All implementations must embed UnimplementedLodgingServiceServer
// for forward compatibility.
//
// LodgingService 住宿服务
type LodgingServiceServer interface {
	// GetLodgingTypeList 获取住宿类型列表
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetLodgingTypeListResponse is appropriate for this use case --)
	//
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: company_id is clear and descriptive --)
	GetLodgingTypeList(context.Context, *GetLodgingTypeListRequest) (*GetLodgingTypeListResponse, error)
	// ListLodgingUnit 获取住宿单元列表
	ListLodgingUnit(context.Context, *ListLodgingUnitRequest) (*ListLodgingUnitResponse, error)
	mustEmbedUnimplementedLodgingServiceServer()
}

// UnimplementedLodgingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLodgingServiceServer struct{}

func (UnimplementedLodgingServiceServer) GetLodgingTypeList(context.Context, *GetLodgingTypeListRequest) (*GetLodgingTypeListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLodgingTypeList not implemented")
}
func (UnimplementedLodgingServiceServer) ListLodgingUnit(context.Context, *ListLodgingUnitRequest) (*ListLodgingUnitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLodgingUnit not implemented")
}
func (UnimplementedLodgingServiceServer) mustEmbedUnimplementedLodgingServiceServer() {}
func (UnimplementedLodgingServiceServer) testEmbeddedByValue()                        {}

// UnsafeLodgingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LodgingServiceServer will
// result in compilation errors.
type UnsafeLodgingServiceServer interface {
	mustEmbedUnimplementedLodgingServiceServer()
}

func RegisterLodgingServiceServer(s grpc.ServiceRegistrar, srv LodgingServiceServer) {
	// If the following call pancis, it indicates UnimplementedLodgingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LodgingService_ServiceDesc, srv)
}

func _LodgingService_GetLodgingTypeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLodgingTypeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).GetLodgingTypeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LodgingService_GetLodgingTypeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).GetLodgingTypeList(ctx, req.(*GetLodgingTypeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingService_ListLodgingUnit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLodgingUnitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingServiceServer).ListLodgingUnit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LodgingService_ListLodgingUnit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingServiceServer).ListLodgingUnit(ctx, req.(*ListLodgingUnitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LodgingService_ServiceDesc is the grpc.ServiceDesc for LodgingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LodgingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.v1.LodgingService",
	HandlerType: (*LodgingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLodgingTypeList",
			Handler:    _LodgingService_GetLodgingTypeList_Handler,
		},
		{
			MethodName: "ListLodgingUnit",
			Handler:    _LodgingService_ListLodgingUnit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/v1/lodging_service.proto",
}
