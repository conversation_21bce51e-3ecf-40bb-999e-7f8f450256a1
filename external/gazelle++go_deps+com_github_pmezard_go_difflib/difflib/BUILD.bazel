load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "difflib",
    srcs = ["difflib.go"],
    importpath = "github.com/pmezard/go-difflib/difflib",
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":difflib",
    visibility = ["//visibility:public"],
)

go_test(
    name = "difflib_test",
    srcs = ["difflib_test.go"],
    embed = [":difflib"],
)
