load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "difftest",
    srcs = ["difftest.go"],
    importpath = "golang.org/x/tools/internal/diff/difftest",
    visibility = [
        "//:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
    deps = ["//internal/diff"],
)

alias(
    name = "go_default_library",
    actual = ":difftest",
    visibility = [
        "//:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
)

go_test(
    name = "difftest_test",
    srcs = ["difftest_test.go"],
    deps = [
        ":difftest",
        "//internal/testenv",
    ],
)
