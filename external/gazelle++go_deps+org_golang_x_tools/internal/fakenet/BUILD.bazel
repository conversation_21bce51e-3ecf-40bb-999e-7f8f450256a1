load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fakenet",
    srcs = ["conn.go"],
    importpath = "golang.org/x/tools/internal/fakenet",
    visibility = [
        "//:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
)

alias(
    name = "go_default_library",
    actual = ":fakenet",
    visibility = [
        "//:__subpackages__",
        "@org_golang_x_tools_go_vcs//:__subpackages__",
    ],
)
