load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "unmarshal_lib",
    srcs = ["main.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/unmarshal/cmd/unmarshal",
    visibility = ["//visibility:private"],
    deps = [
        "//go/analysis/passes/unmarshal",
        "//go/analysis/singlechecker",
    ],
)

go_binary(
    name = "unmarshal",
    embed = [":unmarshal_lib"],
    visibility = ["//visibility:public"],
)
