load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "a",
    srcs = [
        "a.go",
        "b.go",
    ],
    importpath = "golang.org/x/tools/go/analysis/passes/loopclosure/testdata/src/a",
    visibility = ["//visibility:public"],
    deps = ["@org_golang_x_sync//errgroup"],
)

alias(
    name = "go_default_library",
    actual = ":a",
    visibility = ["//visibility:public"],
)
