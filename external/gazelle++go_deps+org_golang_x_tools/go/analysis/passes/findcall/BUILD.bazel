load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "findcall",
    srcs = ["findcall.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/findcall",
    visibility = ["//visibility:public"],
    deps = ["//go/analysis"],
)

alias(
    name = "go_default_library",
    actual = ":findcall",
    visibility = ["//visibility:public"],
)

go_test(
    name = "findcall_test",
    srcs = ["findcall_test.go"],
    deps = [
        ":findcall",
        "//go/analysis/analysistest",
    ],
)
