v2;
package time;
pkgpath time;
import errors errors "errors";
import sync sync "sync";
import syscall syscall "syscall";
init time time..import runtime runtime..import sync sync..import syscall syscall..import;
init_graph 0 1 0 2 0 3 2 1 3 1 3 2;
const ANSIC = "Mon Jan _2 15:04:05 2006";
func After (d <type 1 "Duration" <type -4>
 func (d <type 1>) String () <type -16>;
 func (d <type 1>) Nanoseconds () <type -4>;
 func (d <type 1>) Seconds () <type -10>;
 func (d <type 1>) Minutes () <type -10>;
 func (d <type 1>) Hours () <type -10>;
>) <type 2 chan <- <type 3 "Time" <type 4 struct { .time.sec <type -4>; .time.nsec <type -3>; .time.loc <type 5 *<type 6 "Location" <type 7 struct { .time.name <type -16>; .time.zone <type 8 [] <type 9 ".time.zone" <type 10 struct { .time.name <type -16>; .time.offset <type -11>; .time.isDST <type -15>; }>>>; .time.tx <type 11 [] <type 12 ".time.zoneTrans" <type 13 struct { .time.when <type -4>; .time.index <type -5>; .time.isstd <type -15>; .time.isutc <type -15>; }>>>; .time.cacheStart <type -4>; .time.cacheEnd <type -4>; .time.cacheZone <type 14 *<type 9>>; }>
 func (l <type 15 *<type 6>>) .time.get () <type 15>;
 func (l <type 15>) String () <type -16>;
 func (l <type 15>) .time.lookup (sec <type -4>) (name <type -16>, offset <type -11>, isDST <type -15>, start <type -4>, end <type -4>);
 func (l <type 15>) .time.lookupFirstZone () <type -11>;
 func (l <type 15>) .time.firstZoneUsed () <type -15>;
 func (l <type 15>) .time.lookupName (name <type -16>, unix <type -4>) (offset <type -11>, isDST <type -15>, ok <type -15>);
>>; }>
 func (t <type 3>) String () <type -16>;
 func (t <type 3>) Format (layout <type -16>) <type -16>;
 func (t <type 3>) AppendFormat (b <type 16 [] <type -20>>, layout <type -16>) <type 17 [] <type -20>>;
 func (t <type 3>) After (u <type 3>) <type -15>;
 func (t <type 3>) Before (u <type 3>) <type -15>;
 func (t <type 3>) Equal (u <type 3>) <type -15>;
 func (t <type 3>) IsZero () <type -15>;
 func (t <type 3>) .time.abs () <type -8>;
 func (t <type 3>) .time.locabs () (name <type -16>, offset <type -11>, abs <type -8>);
 func (t <type 3>) Date () (year <type -11>, month <type 18 "Month" <type -11>
 func (m <type 18>) String () <type -16>;
>, day <type -11>);
 func (t <type 3>) Year () <type -11>;
 func (t <type 3>) Month () <type 18>;
 func (t <type 3>) Day () <type -11>;
 func (t <type 3>) Weekday () <type 19 "Weekday" <type -11>
 func (d <type 19>) String () <type -16>;
>;
 func (t <type 3>) ISOWeek () (year <type -11>, week <type -11>);
 func (t <type 3>) Clock () (hour <type -11>, min <type -11>, sec <type -11>);
 func (t <type 3>) Hour () <type -11>;
 func (t <type 3>) Minute () <type -11>;
 func (t <type 3>) Second () <type -11>;
 func (t <type 3>) Nanosecond () <type -11>;
 func (t <type 3>) YearDay () <type -11>;
 func (t <type 3>) Add (d <type 1>) <type 3>;
 func (t <type 3>) Sub (u <type 3>) <type 1>;
 func (t <type 3>) AddDate (years <type -11>, months <type -11>, days <type -11>) <type 3>;
 func (t <type 3>) .time.date (full <type -15>) (year <type -11>, month <type 18>, day <type -11>, yday <type -11>);
 func (t <type 3>) UTC () <type 3>;
 func (t <type 3>) Local () <type 3>;
 func (t <type 3>) In (loc <type 20 *<type 6>>) <type 3>;
 func (t <type 3>) Location () <type 21 *<type 6>>;
 func (t <type 3>) Zone () (name <type -16>, offset <type -11>);
 func (t <type 3>) Unix () <type -4>;
 func (t <type 3>) UnixNano () <type -4>;
 func (t <type 3>) MarshalBinary () (? <type 22 [] <type -20>>, ? <type -19>);
 func (t <type 23 *<type 3>>) UnmarshalBinary (data <type 24 [] <type -20>>) <type -19>;
 func (t <type 3>) GobEncode () (? <type 25 [] <type -20>>, ? <type -19>);
 func (t <type 23>) GobDecode (data <type 26 [] <type -20>>) <type -19>;
 func (t <type 3>) MarshalJSON () (? <type 27 [] <type -20>>, ? <type -19>);
 func (t <type 23>) UnmarshalJSON (data <type 28 [] <type -20>>) <type -19>;
 func (t <type 3>) MarshalText () (? <type 29 [] <type -20>>, ? <type -19>);
 func (t <type 23>) UnmarshalText (data <type 30 [] <type -20>>) <type -19>;
 func (t <type 3>) Truncate (d <type 1>) <type 3>;
 func (t <type 3>) Round (d <type 1>) <type 3>;
>>;
func AfterFunc (d <type 1>, f <type 31 ()>) <type 32 *<type 33 "Timer" <type 34 struct { C <type 35 chan <- <type 3>>; .time.r <type 36 ".time.runtimeTimer" <type 37 struct { .time.i <type -11>; .time.when <type -4>; .time.period <type -4>; .time.f <type 38 (? <type 39 interface { }>, ? <type -13>)>; .time.arg <type 40 interface { }>; .time.seq <type -13>; }>>; }>
 func (t <type 41 *<type 33>>) Stop () <type -15>;
 func (t <type 41>) Reset (d <type 1>) <type -15>;
>>;
const April <type 18> = 4 ;
const August <type 18> = 8 ;
func Date (year <type -11>, month <type 18>, day <type -11>, hour <type -11>, min <type -11>, sec <type -11>, nsec <type -11>, loc <type 42 *<type 6>>) <type 3>;
const December <type 18> = 12 ;
type <type 1>;
const February <type 18> = 2 ;
func FixedZone (name <type -16>, offset <type -11>) <type 15>;
const Friday <type 19> = 5 ;
const Hour <type 1> = 3600000000000 ;
const January <type 18> = 1 ;
const July <type 18> = 7 ;
const June <type 18> = 6 ;
const Kitchen = "3:04PM";
func LoadLocation (name <type -16>) (? <type 15>, ? <type -19>);
var Local <type 15>;
type <type 6>;
var LocationSource <type 43 (name <type -16>) (? <type 44 [] <type -20>>, ? <type -19>)>;
const March <type 18> = 3 ;
const May <type 18> = 5 ;
const Microsecond <type 1> = 1000 ;
const Millisecond <type 1> = 1000000 ;
const Minute <type 1> = 60000000000 ;
const Monday <type 19> = 1 ;
type <type 18>;
const Nanosecond <type 1> = 1 ;
func NewTicker (d <type 1>) <type 45 *<type 46 "Ticker" <type 47 struct { C <type 48 chan <- <type 3>>; .time.r <type 36>; }>
 func (t <type 49 *<type 46>>) Stop ();
>>;
func NewTimer (d <type 1>) <type 32>;
const November <type 18> = 11 ;
func Now () <type 3>;
const October <type 18> = 10 ;
func Parse (layout <type -16>, value <type -16>) (? <type 3>, ? <type -19>);
func ParseDuration (s <type -16>) (? <type 1>, ? <type -19>);
type <type 50 "ParseError" <type 51 struct { Layout <type -16>; Value <type -16>; LayoutElem <type -16>; ValueElem <type -16>; Message <type -16>; }>
 func (e <type 52 *<type 50>>) Error () <type -16>;
>;
func ParseInLocation (layout <type -16>, value <type -16>, loc <type 53 *<type 6>>) (? <type 3>, ? <type -19>);
const RFC1123 = "Mon, 02 Jan 2006 15:04:05 MST";
const RFC1123Z = "Mon, 02 Jan 2006 15:04:05 -0700";
const RFC3339 = "2006-01-02T15:04:05Z07:00";
const RFC3339Nano = "2006-01-02T15:04:05.999999999Z07:00";
const RFC822 = "02 Jan 06 15:04 MST";
const RFC822Z = "02 Jan 06 15:04 -0700";
const RFC850 = "Monday, 02-Jan-06 15:04:05 MST";
const RubyDate = "Mon Jan 02 15:04:05 -0700 2006";
const Saturday <type 19> = 6 ;
const Second <type 1> = 1000000000 ;
const September <type 18> = 9 ;
func Since (t <type 3>) <type 1>;
func Sleep (d <type 1>);
const Stamp = "Jan _2 15:04:05";
const StampMicro = "Jan _2 15:04:05.000000";
const StampMilli = "Jan _2 15:04:05.000";
const StampNano = "Jan _2 15:04:05.000000000";
const Sunday <type 19> = 0 ;
const Thursday <type 19> = 4 ;
func Tick (d <type 1>) <type 54 chan <- <type 3>>;
type <type 46>;
type <type 3>;
type <type 33>;
const Tuesday <type 19> = 2 ;
var UTC <type 15>;
func Unix (sec <type -4>, nsec <type -4>) <type 3>;
const UnixDate = "Mon Jan _2 15:04:05 MST 2006";
const Wednesday <type 19> = 3 ;
type <type 19>;
checksum C04E7F45B20C621A25DC74E9B13EA4FF6732E226;
