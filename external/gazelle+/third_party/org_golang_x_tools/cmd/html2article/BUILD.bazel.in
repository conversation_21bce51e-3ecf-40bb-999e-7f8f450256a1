load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "go_default_library",
    srcs = ["conv.go"],
    importpath = "golang.org/x/tools/cmd/html2article",
    visibility = ["//visibility:private"],
    deps = [
        "@org_golang_x_net//html:go_default_library",
        "@org_golang_x_net//html/atom:go_default_library",
    ],
)

go_binary(
    name = "html2article",
    embed = [":go_default_library"],
    visibility = ["//visibility:public"],
)
