load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = ["gccgoexportdata.go"],
    importpath = "golang.org/x/tools/go/gccgoexportdata",
    visibility = ["//visibility:public"],
    deps = ["//go/internal/gccgoimporter:go_default_library"],
)

go_test(
    name = "go_default_xtest",
    srcs = ["gccgoexportdata_test.go"],
    data = glob(["testdata/**"]),
    deps = [":go_default_library"],
)
