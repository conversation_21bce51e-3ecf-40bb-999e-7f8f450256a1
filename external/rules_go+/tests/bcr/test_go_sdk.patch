diff --git a/src/os/dir.go b/src/os/dir.go
index 5306bcb..d110a19 100644
--- a/src/os/dir.go
+++ b/src/os/dir.go
@@ -17,6 +17,8 @@ const (
 	readdirFileInfo
 )

+const SayHello = "Hello"
+
 // Readdir reads the contents of the directory associated with file and
 // returns a slice of up to n [FileInfo] values, as would be returned
 // by [Lstat], in directory order. Subsequent calls on the same file will yield
