load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

test_suite(name = "go_plugin_with_proto_library")

go_test(
    name = "go_default_test",
    srcs = ["all_test.go"],
    data = [":plugin"],
    deps = [":validate"],
)

go_binary(
    name = "plugin",
    srcs = ["plugin.go"],
    out = "plugin.so",
    linkmode = "plugin",
    deps = [":validate"],
)

proto_library(
    name = "validate_proto",
    srcs = ["validate.proto"],
)

go_proto_library(
    name = "validate",
    gc_goopts = ["-trimpath=$(BINDIR)=>."],
    importpath = "go_plugin_with_proto_library/validate",
    proto = ":validate_proto",
)
