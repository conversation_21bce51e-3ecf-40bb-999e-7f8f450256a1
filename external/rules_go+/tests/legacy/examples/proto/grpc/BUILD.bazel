load("@io_bazel_rules_go//go:def.bzl", "go_binary")
load("@io_bazel_rules_go//proto:def.bzl", "go_grpc_library", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "my_svc_proto",
    srcs = ["my_svc.proto"],
    deps = [
        "//tests/legacy/examples/proto/lib:lib_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
    ],
)

go_proto_library(
    name = "not_grpc",
    importpath = "github.com/bazelbuild/rules_go/examples/proto/grpc/my_svc_proto",
    proto = ":my_svc_proto",
    deps = [
        "//tests/legacy/examples/proto/lib:lib_go_proto",
    ],
)

go_grpc_library(
    name = "my_svc_go_proto",
    importpath = "github.com/bazelbuild/rules_go/examples/proto/grpc/my_svc_proto",
    proto = ":my_svc_proto",
    deps = [
        "//tests/legacy/examples/proto/lib:lib_go_proto",
    ],
)

go_binary(
    name = "test_grpc",
    srcs = ["main.go"],
    deps = [
        ":my_svc_go_proto",
        "//tests/legacy/examples/proto/lib:lib_go_proto",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:empty_go_proto",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_x_net//context:go_default_library",
    ],
)
