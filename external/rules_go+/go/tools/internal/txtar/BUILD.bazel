load("//go:def.bzl", "go_library", "go_test")

go_library(
    name = "txtar",
    srcs = ["archive.go"],
    importpath = "github.com/bazelbuild/rules_go/go/tools/internal/txtar",
    visibility = ["//go/tools:__subpackages__"],
)

go_test(
    name = "txtar_test",
    srcs = ["archive_test.go"],
    embed = [":txtar"],
)

alias(
    name = "go_default_library",
    actual = ":txtar",
    visibility = ["//go/tools:__subpackages__"],
)
