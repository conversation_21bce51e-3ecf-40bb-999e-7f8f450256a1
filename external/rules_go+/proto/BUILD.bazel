load("@bazel_skylib//:bzl_library.bzl", "bzl_library")
load(
    "//go/private/rules:transition.bzl",
    "non_go_reset_target",
)
load(
    "//proto:compiler.bzl",
    "go_proto_compiler",
)
load(
    "//proto/wkt:well_known_types.bzl",
    "GOGO_WELL_KNOWN_TYPE_REMAPS",
    "PROTO_RUNTIME_DEPS",
    "WELL_KNOWN_TYPES_APIV2",
    "WELL_KNOWN_TYPE_RULES",
)

go_proto_compiler(
    name = "go_proto_bootstrap",
    visibility = ["//visibility:public"],
    deps = PROTO_RUNTIME_DEPS,
)

go_proto_compiler(
    name = "go_proto",
    plugin = "@org_golang_google_protobuf//cmd/protoc-gen-go",
    visibility = ["//visibility:public"],
    deps = PROTO_RUNTIME_DEPS + WELL_KNOWN_TYPES_APIV2,
)

go_proto_compiler(
    name = "go_grpc",
    # TODO: Bring back the deprecation once Gazelle emits go_grpc_library.
    #    deprecation = "Migrate to //proto:go_grpc_v2 compiler (which you'll get automatically if you use the go_grpc_library() rule).",
    options = ["plugins=grpc"],
    plugin = "@com_github_golang_protobuf//protoc-gen-go",
    visibility = ["//visibility:public"],
    deps = PROTO_RUNTIME_DEPS + WELL_KNOWN_TYPES_APIV2 + [
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_x_net//context:go_default_library",
    ],
)

go_proto_compiler(
    name = "go_grpc_v2",
    # Do not break existing code with this upgrade
    # See https://github.com/grpc/grpc-go/blob/master/cmd/protoc-gen-go-grpc/README.md#future-proofing-services
    options = ["require_unimplemented_servers=false"],
    plugin = "@org_golang_google_grpc_cmd_protoc_gen_go_grpc//:protoc-gen-go-grpc",
    suffix = "_grpc.pb.go",
    visibility = ["//visibility:public"],
    deps = PROTO_RUNTIME_DEPS + WELL_KNOWN_TYPES_APIV2 + [
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_x_net//context:go_default_library",
    ],
)

GOGO_VARIANTS = [
    "combo",
    "gogo",
    "gogofast",
    "gogofaster",
    "gogoslick",
    "gogotypes",
    "gostring",
]

[go_proto_compiler(
    name = variant + "_proto",
    options = GOGO_WELL_KNOWN_TYPE_REMAPS,
    plugin = "@com_github_gogo_protobuf//protoc-gen-" + variant,
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_gogo_protobuf//gogoproto:go_default_library",
        "@com_github_gogo_protobuf//proto:go_default_library",
        "@com_github_gogo_protobuf//protoc-gen-gogo/descriptor:go_default_library",
        "@com_github_gogo_protobuf//sortkeys:go_default_library",
        "@com_github_gogo_protobuf//types:go_default_library",
    ] + WELL_KNOWN_TYPE_RULES.values(),
) for variant in GOGO_VARIANTS]

go_proto_compiler(
    name = "gofast_proto",
    plugin = "@com_github_gogo_protobuf//protoc-gen-gofast",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_golang_protobuf//proto:go_default_library",
    ] + WELL_KNOWN_TYPE_RULES.values(),
)

[go_proto_compiler(
    name = variant + "_grpc",
    options = ["plugins=grpc"] + GOGO_WELL_KNOWN_TYPE_REMAPS,
    plugin = "@com_github_gogo_protobuf//protoc-gen-" + variant,
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_gogo_protobuf//gogoproto:go_default_library",
        "@com_github_gogo_protobuf//proto:go_default_library",
        "@com_github_gogo_protobuf//protoc-gen-gogo/descriptor:go_default_library",
        "@com_github_gogo_protobuf//sortkeys:go_default_library",
        "@com_github_gogo_protobuf//types:go_default_library",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_x_net//context:go_default_library",
    ] + WELL_KNOWN_TYPE_RULES.values(),
) for variant in GOGO_VARIANTS]

go_proto_compiler(
    name = "gofast_grpc",
    options = ["plugins=grpc"],
    plugin = "@com_github_gogo_protobuf//protoc-gen-gofast",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_golang_protobuf//proto:go_default_library",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_x_net//context:go_default_library",
    ] + WELL_KNOWN_TYPE_RULES.values(),
)

non_go_reset_target(
    name = "protoc",
    dep = "@com_google_protobuf//:protoc",
    deprecation = "No longer used by rules_go, will be removed in a future release.",
    visibility = ["//visibility:public"],
)

filegroup(
    name = "all_rules",
    testonly = True,
    srcs = glob(["*.bzl"]) + [
        "//proto/private:all_files",
        "//proto/wkt:all_rules",
    ],
    visibility = ["//:__subpackages__"],
)

filegroup(
    name = "all_files",
    testonly = True,
    srcs = glob(["**"]) + [
        "//proto/private:all_files",
        "//proto/wkt:all_files",
    ],
    visibility = ["//:__subpackages__"],
)

bzl_library(
    name = "compiler",
    srcs = ["compiler.bzl"],
    visibility = ["//visibility:public"],
    deps = [
        "//go:def",
        "//go/private/rules:transition",
        "@bazel_skylib//lib:paths",
    ],
)

bzl_library(
    name = "def",
    srcs = ["def.bzl"],
    visibility = ["//visibility:public"],
    # Don't list dependency on @rules_proto//proto:defs
    deps = [
        "//go:def",
        "//go/private:providers",
        "//proto:compiler",
    ],  # keep
)

bzl_library(
    name = "gogo",
    srcs = ["gogo.bzl"],
    visibility = ["//visibility:public"],
)
